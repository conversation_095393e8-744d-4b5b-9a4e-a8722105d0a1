# Types of Machine Learning

A comprehensive guide to understanding different approaches to machine learning and when to apply each method.

## 🎯 Learning Objectives

By the end of this lesson, you will be able to:
- Distinguish between supervised, unsupervised, and reinforcement learning
- Identify appropriate learning types for different problems
- Understand the advantages and limitations of each approach
- Implement basic examples of each learning type
- Recognize real-world applications of different learning paradigms

## 📚 Overview of Learning Types

### The Learning Spectrum
```
Data Availability & Problem Type → Learning Approach

Labeled Data Available → Supervised Learning
├─ Predict Categories → Classification
└─ Predict Numbers → Regression

No Labels Available → Unsupervised Learning
├─ Find Groups → Clustering
├─ Reduce Dimensions → Dimensionality Reduction
└─ Find Patterns → Association Rules

Learning from Actions → Reinforcement Learning
├─ Game Playing → Q-Learning, Policy Gradients
├─ Robot Control → Actor-Critic Methods
└─ Resource Management → Multi-Armed Bandits
```

## 🏷️ Supervised Learning

### Definition
**Supervised Learning** uses labeled training data to learn a mapping function from inputs to outputs. The "supervisor" provides the correct answers during training.

### Key Characteristics
- **Training Data**: Input-output pairs (X, y)
- **Objective**: Learn function f: X → y
- **Evaluation**: Compare predictions with known correct answers
- **Performance**: Measured on unseen test data

### Types of Supervised Learning

#### 1. Classification
**Goal**: Predict discrete categories or classes

**Examples**:
- Email spam detection (spam/not spam)
- Image recognition (cat/dog/bird)
- Medical diagnosis (disease present/absent)
- Sentiment analysis (positive/negative/neutral)

**Common Algorithms**:
```python
# Classification Examples
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC

# Generate sample classification data
X, y = make_classification(n_samples=1000, n_features=4, n_classes=3, random_state=42)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Classification algorithms
classifiers = {
    'Logistic Regression': LogisticRegression(),
    'Decision Tree': DecisionTreeClassifier(),
    'Random Forest': RandomForestClassifier(),
    'SVM': SVC()
}

# Train and evaluate
for name, clf in classifiers.items():
    clf.fit(X_train, y_train)
    accuracy = clf.score(X_test, y_test)
    print(f"{name}: {accuracy:.3f}")
```

**Classification Metrics**:
- **Accuracy**: Correct predictions / Total predictions
- **Precision**: True Positives / (True Positives + False Positives)
- **Recall**: True Positives / (True Positives + False Negatives)
- **F1-Score**: Harmonic mean of precision and recall

#### 2. Regression
**Goal**: Predict continuous numerical values

**Examples**:
- House price prediction
- Stock price forecasting
- Temperature prediction
- Sales revenue estimation

**Common Algorithms**:
```python
# Regression Examples
from sklearn.datasets import make_regression
from sklearn.linear_model import LinearRegression
from sklearn.tree import DecisionTreeRegressor
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.metrics import mean_squared_error, r2_score

# Generate sample regression data
X, y = make_regression(n_samples=1000, n_features=4, noise=0.1, random_state=42)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Regression algorithms
regressors = {
    'Linear Regression': LinearRegression(),
    'Decision Tree': DecisionTreeRegressor(),
    'Random Forest': RandomForestRegressor(),
    'SVR': SVR()
}

# Train and evaluate
for name, reg in regressors.items():
    reg.fit(X_train, y_train)
    y_pred = reg.predict(X_test)
    mse = mean_squared_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    print(f"{name}: MSE={mse:.3f}, R²={r2:.3f}")
```

**Regression Metrics**:
- **Mean Squared Error (MSE)**: Average of squared differences
- **Root Mean Squared Error (RMSE)**: Square root of MSE
- **Mean Absolute Error (MAE)**: Average of absolute differences
- **R² Score**: Proportion of variance explained by the model

### Supervised Learning Process
```python
def supervised_learning_pipeline(X, y, algorithm, test_size=0.2):
    """
    Complete supervised learning pipeline
    """
    # 1. Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=42
    )
    
    # 2. Train model
    model = algorithm
    model.fit(X_train, y_train)
    
    # 3. Make predictions
    y_pred = model.predict(X_test)
    
    # 4. Evaluate performance
    if hasattr(model, 'predict_proba'):  # Classification
        accuracy = model.score(X_test, y_test)
        return model, {'accuracy': accuracy}
    else:  # Regression
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        return model, {'mse': mse, 'r2': r2}

# Example usage
model, metrics = supervised_learning_pipeline(X, y, RandomForestClassifier())
print("Model performance:", metrics)
```

## 🔍 Unsupervised Learning

### Definition
**Unsupervised Learning** finds hidden patterns in data without labeled examples. The algorithm discovers structure in data without knowing the "correct" answer.

### Key Characteristics
- **Training Data**: Only inputs (X), no target labels
- **Objective**: Discover hidden patterns or structure
- **Evaluation**: More challenging - often domain-specific
- **Applications**: Data exploration, preprocessing, feature engineering

### Types of Unsupervised Learning

#### 1. Clustering
**Goal**: Group similar data points together

**Examples**:
- Customer segmentation
- Image segmentation
- Gene sequencing analysis
- Social network analysis

**Common Algorithms**:
```python
# Clustering Examples
from sklearn.datasets import make_blobs
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.mixture import GaussianMixture
import matplotlib.pyplot as plt

# Generate sample clustering data
X, _ = make_blobs(n_samples=300, centers=4, cluster_std=0.60, random_state=0)

# Clustering algorithms
clustering_algorithms = {
    'K-Means': KMeans(n_clusters=4),
    'DBSCAN': DBSCAN(eps=0.3, min_samples=10),
    'Agglomerative': AgglomerativeClustering(n_clusters=4),
    'Gaussian Mixture': GaussianMixture(n_components=4)
}

# Apply clustering
fig, axes = plt.subplots(2, 2, figsize=(12, 10))
axes = axes.ravel()

for i, (name, algorithm) in enumerate(clustering_algorithms.items()):
    # Fit and predict
    if hasattr(algorithm, 'fit_predict'):
        labels = algorithm.fit_predict(X)
    else:
        algorithm.fit(X)
        labels = algorithm.predict(X)
    
    # Plot results
    axes[i].scatter(X[:, 0], X[:, 1], c=labels, cmap='viridis')
    axes[i].set_title(f'{name} Clustering')

plt.tight_layout()
plt.show()
```

**Clustering Evaluation**:
- **Silhouette Score**: Measures cluster cohesion and separation
- **Davies-Bouldin Index**: Ratio of within-cluster to between-cluster distances
- **Adjusted Rand Index**: Similarity to ground truth (if available)

#### 2. Dimensionality Reduction
**Goal**: Reduce the number of features while preserving important information

**Examples**:
- Data visualization (3D → 2D)
- Feature selection for machine learning
- Noise reduction in images
- Compression

**Common Algorithms**:
```python
# Dimensionality Reduction Examples
from sklearn.datasets import load_digits
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt

# Load high-dimensional data (64 features)
digits = load_digits()
X, y = digits.data, digits.target

# Dimensionality reduction algorithms
pca = PCA(n_components=2)
tsne = TSNE(n_components=2, random_state=42)

# Apply dimensionality reduction
X_pca = pca.fit_transform(X)
X_tsne = tsne.fit_transform(X[:500])  # TSNE is slow, use subset

# Visualize results
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

# PCA plot
scatter1 = ax1.scatter(X_pca[:, 0], X_pca[:, 1], c=y, cmap='tab10')
ax1.set_title('PCA Visualization')
ax1.set_xlabel('First Principal Component')
ax1.set_ylabel('Second Principal Component')

# t-SNE plot
scatter2 = ax2.scatter(X_tsne[:, 0], X_tsne[:, 1], c=y[:500], cmap='tab10')
ax2.set_title('t-SNE Visualization')
ax2.set_xlabel('t-SNE Dimension 1')
ax2.set_ylabel('t-SNE Dimension 2')

plt.tight_layout()
plt.show()

# Explained variance for PCA
print(f"PCA explained variance ratio: {pca.explained_variance_ratio_}")
print(f"Total variance explained: {pca.explained_variance_ratio_.sum():.3f}")
```

#### 3. Association Rule Learning
**Goal**: Find relationships between different items or features

**Examples**:
- Market basket analysis ("People who buy X also buy Y")
- Web usage patterns
- Protein sequences
- Recommendation systems

**Common Algorithms**:
```python
# Association Rules Example (conceptual)
def find_frequent_itemsets(transactions, min_support=0.5):
    """
    Simple implementation of frequent itemset mining
    """
    from collections import defaultdict, Counter
    
    # Count item frequencies
    item_counts = Counter()
    for transaction in transactions:
        for item in transaction:
            item_counts[item] += 1
    
    # Find frequent items
    n_transactions = len(transactions)
    frequent_items = {
        item: count for item, count in item_counts.items()
        if count / n_transactions >= min_support
    }
    
    return frequent_items

# Example market basket data
transactions = [
    ['milk', 'eggs', 'bread', 'cheese'],
    ['milk', 'eggs'],
    ['milk', 'bread'],
    ['eggs', 'bread', 'butter'],
    ['milk', 'eggs', 'bread', 'cheese', 'butter']
]

frequent_items = find_frequent_itemsets(transactions, min_support=0.4)
print("Frequent items:", frequent_items)
```

## 🎮 Reinforcement Learning

### Definition
**Reinforcement Learning** learns through interaction with an environment, receiving rewards or penalties for actions taken. The agent learns to maximize cumulative reward over time.

### Key Characteristics
- **Learning Method**: Trial and error through environmental interaction
- **Feedback**: Rewards and penalties (not direct supervision)
- **Objective**: Maximize long-term cumulative reward
- **Challenge**: Balance exploration vs exploitation

### Key Concepts

#### 1. Core Components
- **Agent**: The learner/decision maker
- **Environment**: The world the agent interacts with
- **State**: Current situation of the agent
- **Action**: What the agent can do
- **Reward**: Feedback from the environment
- **Policy**: Strategy for choosing actions

#### 2. The RL Process
```
Agent observes State → Chooses Action → Receives Reward → Environment transitions to new State
```

### RL Algorithms

#### 1. Q-Learning
**Goal**: Learn the value of taking each action in each state

```python
import numpy as np
import random

class QLearningAgent:
    def __init__(self, n_states, n_actions, learning_rate=0.1, 
                 discount_factor=0.9, epsilon=0.1):
        self.q_table = np.zeros((n_states, n_actions))
        self.lr = learning_rate
        self.gamma = discount_factor
        self.epsilon = epsilon
        self.n_actions = n_actions
    
    def choose_action(self, state):
        # Epsilon-greedy action selection
        if random.random() < self.epsilon:
            return random.randint(0, self.n_actions - 1)  # Explore
        else:
            return np.argmax(self.q_table[state])  # Exploit
    
    def update_q_table(self, state, action, reward, next_state):
        # Q-learning update rule
        old_value = self.q_table[state, action]
        next_max = np.max(self.q_table[next_state])
        
        new_value = reward + self.gamma * next_max
        self.q_table[state, action] = old_value + self.lr * (new_value - old_value)
    
    def train(self, environment, episodes=1000):
        for episode in range(episodes):
            state = environment.reset()
            done = False
            
            while not done:
                action = self.choose_action(state)
                next_state, reward, done = environment.step(action)
                self.update_q_table(state, action, reward, next_state)
                state = next_state
            
            # Decay epsilon (reduce exploration over time)
            if self.epsilon > 0.01:
                self.epsilon *= 0.995

# Simple Grid World Environment
class GridWorld:
    def __init__(self, size=5):
        self.size = size
        self.state = 0  # Start at top-left
        self.goal = size * size - 1  # Goal at bottom-right
    
    def reset(self):
        self.state = 0
        return self.state
    
    def step(self, action):
        # Actions: 0=up, 1=right, 2=down, 3=left
        row, col = divmod(self.state, self.size)
        
        if action == 0 and row > 0:  # Up
            row -= 1
        elif action == 1 and col < self.size - 1:  # Right
            col += 1
        elif action == 2 and row < self.size - 1:  # Down
            row += 1
        elif action == 3 and col > 0:  # Left
            col -= 1
        
        self.state = row * self.size + col
        
        # Reward structure
        if self.state == self.goal:
            reward = 100  # Reached goal
            done = True
        else:
            reward = -1  # Small penalty for each step
            done = False
        
        return self.state, reward, done

# Train Q-learning agent
env = GridWorld(size=5)
agent = QLearningAgent(n_states=25, n_actions=4)
agent.train(env, episodes=1000)

print("Trained Q-table:")
print(agent.q_table)
```

### RL Applications
- **Game Playing**: Chess, Go, video games
- **Robotics**: Robot navigation and control
- **Autonomous Vehicles**: Self-driving cars
- **Finance**: Algorithmic trading
- **Healthcare**: Treatment optimization
- **Resource Management**: Energy grid optimization

## 🔄 Semi-Supervised and Self-Supervised Learning

### Semi-Supervised Learning
**Definition**: Uses both labeled and unlabeled data for training

**When to Use**:
- Limited labeled data available
- Labeling is expensive or time-consuming
- Large amounts of unlabeled data exist

**Examples**:
```python
from sklearn.semi_supervised import LabelPropagation, LabelSpreading

# Semi-supervised learning example
def semi_supervised_example():
    # Generate data with limited labels
    X, y = make_classification(n_samples=1000, n_features=2, n_classes=2, random_state=42)
    
    # Only label 10% of the data
    n_labeled = 100
    y_semi = np.copy(y)
    y_semi[n_labeled:] = -1  # -1 indicates unlabeled
    
    # Train semi-supervised models
    label_prop = LabelPropagation()
    label_spread = LabelSpreading()
    
    label_prop.fit(X, y_semi)
    label_spread.fit(X, y_semi)
    
    # Evaluate on fully labeled data
    prop_score = label_prop.score(X, y)
    spread_score = label_spread.score(X, y)
    
    print(f"Label Propagation accuracy: {prop_score:.3f}")
    print(f"Label Spreading accuracy: {spread_score:.3f}")

semi_supervised_example()
```

### Self-Supervised Learning
**Definition**: Creates labels from the input data itself

**Common Approaches**:
- **Masking**: Predict masked portions of input
- **Rotation**: Predict rotation angle of images
- **Next Token Prediction**: Predict next word in sequence
- **Contrastive Learning**: Learn by contrasting similar/dissimilar examples

**Applications**:
- Pre-training large language models (GPT, BERT)
- Computer vision foundation models
- Speech recognition systems

## 🧪 Practical Exercise: Learning Type Selection

### Exercise 1: Problem Classification
**For each scenario, identify the appropriate learning type and justify your choice:**

1. **Email Spam Detection**
   - Available data: 10,000 emails labeled as spam/not spam
   - Learning type: ________________
   - Justification: ________________

2. **Customer Segmentation**
   - Available data: Customer purchase history, no predefined groups
   - Learning type: ________________
   - Justification: ________________

3. **Robot Navigation**
   - Available data: None initially, robot can try different actions
   - Learning type: ________________  
   - Justification: ________________

4. **Medical Image Analysis**
   - Available data: 1,000 labeled X-rays, 100,000 unlabeled X-rays
   - Learning type: ________________
   - Justification: ________________

### Exercise 2: Implementation Comparison
**Compare different learning approaches on the same dataset:**

```python
from sklearn.datasets import load_iris
from sklearn.cluster import KMeans
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split

# Load iris dataset
iris = load_iris()
X, y = iris.data, iris.target

# Supervised Learning (Classification)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
supervised_model = RandomForestClassifier(random_state=42)
supervised_model.fit(X_train, y_train)
supervised_accuracy = supervised_model.score(X_test, y_test)

# Unsupervised Learning (Clustering)
unsupervised_model = KMeans(n_clusters=3, random_state=42)
predicted_clusters = unsupervised_model.fit_predict(X)

# Compare results
print(f"Supervised Learning Accuracy: {supervised_accuracy:.3f}")
print(f"Actual classes: {set(y)}")
print(f"Predicted clusters: {set(predicted_clusters)}")

# Your task: Analyze the differences and discuss when each approach is appropriate
```

## 📊 Learning Type Comparison

| Aspect | Supervised | Unsupervised | Reinforcement |
|--------|------------|--------------|---------------|
| **Data Requirement** | Labeled examples | Unlabeled data | Environment interaction |
| **Learning Goal** | Predict labels | Find patterns | Maximize rewards |
| **Evaluation** | Test accuracy | Domain knowledge | Cumulative reward |
| **Applications** | Classification, Regression | Clustering, Visualization | Control, Games |
| **Challenges** | Need labeled data | Subjective evaluation | Exploration vs exploitation |
| **Feedback** | Immediate | None | Delayed rewards |

## ✅ Self-Assessment Questions

### Conceptual Understanding
1. **Explain the key difference between supervised and unsupervised learning with an example of each.**

2. **Why might you choose semi-supervised learning over fully supervised learning?**

3. **In reinforcement learning, what is the exploration vs exploitation dilemma?**

### Practical Application
1. **You have a dataset of customer transactions but no information about customer preferences. What learning approach would you use to understand customer behavior?**

2. **A medical researcher has 100 labeled chest X-rays showing pneumonia and 10,000 unlabeled chest X-rays. What learning strategy would you recommend?**

3. **You're building an AI to play a new board game. No historical game data exists. Which learning approach is most suitable?**

### Algorithm Selection
**For each use case, select the most appropriate learning type:**

- **Stock Price Prediction**: ________________
- **Social Media Post Categorization**: ________________
- **Autonomous Drone Control**: ________________
- **Market Basket Analysis**: ________________
- **Medical Diagnosis**: ________________
- **Customer Grouping**: ________________

## 🌟 Advanced Topics

### Multi-Task Learning
Learning multiple related tasks simultaneously to improve performance on each task.

### Transfer Learning
Using knowledge gained from one task to improve performance on related tasks.

### Online Learning
Continuously updating models as new data becomes available.

### Active Learning
Intelligently selecting which data points to label for maximum learning benefit.

---

**Next Lesson**: [AI Terminology](terminology.md)

*Estimated Study Time: 2-3 hours including exercises and implementations*
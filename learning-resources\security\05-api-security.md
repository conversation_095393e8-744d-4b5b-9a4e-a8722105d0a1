# API Security

## Overview

API security is critical for healthcare platforms that handle sensitive patient data and payment information. This guide covers comprehensive security measures for REST APIs, GraphQL endpoints, and microservices architectures, with specific focus on healthcare compliance and payment processing security.

## 🎯 Learning Objectives

By the end of this module, you will understand:
- API authentication and authorization mechanisms
- Rate limiting and throttling strategies
- Input validation and output filtering for APIs
- API security testing methodologies
- Monitoring and logging for API security

## 🔐 Authentication and Authorization

### 1. JWT (JSON Web Tokens) Implementation

#### Secure JWT Configuration
```javascript
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

class JWTService {
  static generateTokens(payload) {
    // Access token (short-lived)
    const accessToken = jwt.sign(
      payload,
      process.env.JWT_ACCESS_SECRET,
      { 
        expiresIn: '15m',
        issuer: 'healthcare-platform',
        audience: 'healthcare-api'
      }
    );

    // Refresh token (long-lived)
    const refreshToken = jwt.sign(
      { userId: payload.userId },
      process.env.JWT_REFRESH_SECRET,
      { 
        expiresIn: '7d',
        issuer: 'healthcare-platform'
      }
    );

    return { accessToken, refreshToken };
  }

  static verifyAccessToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_ACCESS_SECRET, {
        issuer: 'healthcare-platform',
        audience: 'healthcare-api'
      });
    } catch (error) {
      throw new Error('Invalid access token');
    }
  }

  static verifyRefreshToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_REFRESH_SECRET, {
        issuer: 'healthcare-platform'
      });
    } catch (error) {
      throw new Error('Invalid refresh token');
    }
  }
}

// Authentication middleware
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  try {
    const decoded = JWTService.verifyAccessToken(token);
    
    // Check if token is blacklisted
    const isBlacklisted = await redis.get(`blacklist:${token}`);
    if (isBlacklisted) {
      return res.status(401).json({ error: 'Token has been revoked' });
    }

    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Invalid or expired token' });
  }
};
```

### 2. OAuth 2.0 with PKCE for Healthcare APIs

```javascript
// OAuth 2.0 implementation for healthcare platform
const crypto = require('crypto');

class OAuth2Service {
  static generateCodeChallenge() {
    const codeVerifier = crypto.randomBytes(32).toString('base64url');
    const codeChallenge = crypto
      .createHash('sha256')
      .update(codeVerifier)
      .digest('base64url');
    
    return { codeVerifier, codeChallenge };
  }

  static async initiateAuth(req, res) {
    const { client_id, redirect_uri, scope, state } = req.query;
    
    // Validate client
    const client = await OAuthClient.findOne({ clientId: client_id });
    if (!client || !client.redirectUris.includes(redirect_uri)) {
      return res.status(400).json({ error: 'Invalid client or redirect URI' });
    }

    // Generate authorization code
    const authCode = crypto.randomBytes(32).toString('hex');
    
    // Store authorization details
    await redis.setex(`auth_code:${authCode}`, 600, JSON.stringify({
      clientId: client_id,
      redirectUri: redirect_uri,
      scope,
      userId: req.user.id
    }));

    res.redirect(`${redirect_uri}?code=${authCode}&state=${state}`);
  }

  static async exchangeCodeForToken(req, res) {
    const { code, client_id, client_secret, redirect_uri, code_verifier } = req.body;
    
    // Verify authorization code
    const authData = await redis.get(`auth_code:${code}`);
    if (!authData) {
      return res.status(400).json({ error: 'Invalid or expired authorization code' });
    }

    const { clientId, scope, userId } = JSON.parse(authData);
    
    // Verify PKCE challenge
    if (code_verifier) {
      const expectedChallenge = crypto
        .createHash('sha256')
        .update(code_verifier)
        .digest('base64url');
      
      // Verify against stored challenge
      const storedChallenge = await redis.get(`pkce:${code}`);
      if (expectedChallenge !== storedChallenge) {
        return res.status(400).json({ error: 'Invalid PKCE verification' });
      }
    }

    // Generate access token
    const tokens = JWTService.generateTokens({
      userId,
      clientId,
      scope: scope.split(' ')
    });

    // Clean up
    await redis.del(`auth_code:${code}`);
    await redis.del(`pkce:${code}`);

    res.json(tokens);
  }
}
```

### 3. Role-Based Access Control (RBAC)

```javascript
// RBAC implementation for healthcare platform
const permissions = {
  'patient': [
    'read:own_profile',
    'update:own_profile',
    'read:own_appointments',
    'create:appointments',
    'read:own_medical_records'
  ],
  'doctor': [
    'read:patient_profiles',
    'update:medical_records',
    'read:appointments',
    'update:appointments',
    'create:prescriptions'
  ],
  'admin': [
    'read:all_users',
    'update:all_users',
    'delete:users',
    'read:system_logs',
    'manage:billing'
  ]
};

const authorize = (requiredPermission) => {
  return (req, res, next) => {
    const userRole = req.user.role;
    const userPermissions = permissions[userRole] || [];
    
    if (!userPermissions.includes(requiredPermission)) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        required: requiredPermission,
        userRole
      });
    }
    
    next();
  };
};

// Usage examples
app.get('/api/patients/:id', 
  authenticateToken,
  authorize('read:patient_profiles'),
  patientController.getProfile
);

app.post('/api/prescriptions',
  authenticateToken,
  authorize('create:prescriptions'),
  prescriptionController.create
);
```

## 🚦 Rate Limiting and Throttling

### 1. Advanced Rate Limiting Strategy

```javascript
const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');

// Different rate limits for different endpoints
const createRateLimiter = (windowMs, max, message) => {
  return rateLimit({
    store: new RedisStore({
      client: redisClient,
      prefix: 'rl:'
    }),
    windowMs,
    max,
    message: { error: message },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      // Use user ID if authenticated, otherwise IP
      return req.user?.id || req.ip;
    }
  });
};

// Rate limiters for different API categories
const authLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  5, // 5 attempts
  'Too many authentication attempts'
);

const apiLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  100, // 100 requests
  'Too many API requests'
);

const paymentLimiter = createRateLimiter(
  60 * 60 * 1000, // 1 hour
  10, // 10 payment attempts
  'Too many payment attempts'
);

// Apply rate limiters
app.use('/api/auth', authLimiter);
app.use('/api', apiLimiter);
app.use('/api/payments', paymentLimiter);
```

### 2. Adaptive Rate Limiting

```javascript
// Adaptive rate limiting based on user behavior
class AdaptiveRateLimiter {
  static async checkRateLimit(userId, endpoint) {
    const key = `adaptive_rl:${userId}:${endpoint}`;
    const current = await redis.get(key);
    
    if (!current) {
      await redis.setex(key, 3600, 1); // 1 hour window
      return { allowed: true, remaining: 99 };
    }
    
    const count = parseInt(current);
    const userTrust = await this.getUserTrustScore(userId);
    const maxRequests = this.calculateMaxRequests(userTrust, endpoint);
    
    if (count >= maxRequests) {
      return { allowed: false, remaining: 0 };
    }
    
    await redis.incr(key);
    return { allowed: true, remaining: maxRequests - count - 1 };
  }
  
  static async getUserTrustScore(userId) {
    // Calculate trust score based on user history
    const user = await User.findById(userId);
    const accountAge = Date.now() - user.createdAt.getTime();
    const daysSinceCreation = accountAge / (1000 * 60 * 60 * 24);
    
    let trustScore = 0.5; // Base trust score
    
    // Increase trust for older accounts
    if (daysSinceCreation > 30) trustScore += 0.2;
    if (daysSinceCreation > 90) trustScore += 0.2;
    
    // Increase trust for verified accounts
    if (user.emailVerified) trustScore += 0.1;
    if (user.phoneVerified) trustScore += 0.1;
    
    return Math.min(trustScore, 1.0);
  }
  
  static calculateMaxRequests(trustScore, endpoint) {
    const baseLimit = {
      'general': 100,
      'sensitive': 20,
      'payment': 10
    };
    
    const endpointType = this.categorizeEndpoint(endpoint);
    return Math.floor(baseLimit[endpointType] * (0.5 + trustScore));
  }
}
```

## 🛡️ Input Validation and Output Filtering

### 1. Comprehensive API Validation

```javascript
const Joi = require('joi');

// Healthcare-specific validation schemas
const apiSchemas = {
  createAppointment: Joi.object({
    doctorId: Joi.string().uuid().required(),
    patientId: Joi.string().uuid().required(),
    appointmentDate: Joi.date().min('now').required(),
    duration: Joi.number().min(15).max(180).required(),
    type: Joi.string().valid('consultation', 'follow-up', 'emergency').required(),
    notes: Joi.string().max(1000).optional(),
    symptoms: Joi.array().items(Joi.string().max(100)).max(10).optional()
  }),
  
  updateMedicalRecord: Joi.object({
    patientId: Joi.string().uuid().required(),
    diagnosis: Joi.string().max(500).required(),
    treatment: Joi.string().max(1000).required(),
    medications: Joi.array().items(
      Joi.object({
        name: Joi.string().max(100).required(),
        dosage: Joi.string().max(50).required(),
        frequency: Joi.string().max(50).required(),
        duration: Joi.string().max(50).required()
      })
    ).max(20),
    followUpDate: Joi.date().min('now').optional()
  }),
  
  processPayment: Joi.object({
    amount: Joi.number().positive().precision(2).max(10000).required(),
    currency: Joi.string().valid('USD', 'EUR', 'GBP').required(),
    paymentMethodId: Joi.string().required(),
    appointmentId: Joi.string().uuid().required(),
    description: Joi.string().max(255).required()
  })
};

// Enhanced validation middleware with sanitization
const validateAndSanitize = (schema) => {
  return async (req, res, next) => {
    try {
      // Validate request body
      const { error, value } = schema.validate(req.body, {
        abortEarly: false,
        stripUnknown: true
      });
      
      if (error) {
        return res.status(400).json({
          error: 'Validation failed',
          details: error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
          }))
        });
      }
      
      // Sanitize string inputs
      const sanitizedValue = await sanitizeObject(value);
      req.validatedBody = sanitizedValue;
      
      next();
    } catch (err) {
      res.status(500).json({ error: 'Validation error' });
    }
  };
};

// Recursive sanitization function
const sanitizeObject = async (obj) => {
  if (typeof obj === 'string') {
    return DOMPurify.sanitize(obj.trim());
  }
  
  if (Array.isArray(obj)) {
    return Promise.all(obj.map(sanitizeObject));
  }
  
  if (obj && typeof obj === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[key] = await sanitizeObject(value);
    }
    return sanitized;
  }
  
  return obj;
};
```

### 2. Output Filtering and Data Masking

```javascript
// Data filtering for different user roles
class DataFilter {
  static filterPatientData(patient, requestingUserRole, requestingUserId) {
    const baseData = {
      id: patient.id,
      firstName: patient.firstName,
      lastName: patient.lastName,
      dateOfBirth: patient.dateOfBirth
    };
    
    switch (requestingUserRole) {
      case 'patient':
        // Patients can see their own full data
        if (patient.id === requestingUserId) {
          return {
            ...patient.toObject(),
            ssn: this.maskSSN(patient.ssn)
          };
        }
        // Other patients see limited data
        return baseData;
        
      case 'doctor':
        // Doctors see medical data but not financial
        return {
          ...patient.toObject(),
          ssn: this.maskSSN(patient.ssn),
          creditCard: undefined,
          bankAccount: undefined
        };
        
      case 'admin':
        // Admins see all data but sensitive fields are masked
        return {
          ...patient.toObject(),
          ssn: this.maskSSN(patient.ssn),
          creditCard: this.maskCreditCard(patient.creditCard)
        };
        
      default:
        return baseData;
    }
  }
  
  static maskSSN(ssn) {
    if (!ssn) return null;
    return `***-**-${ssn.slice(-4)}`;
  }
  
  static maskCreditCard(cardNumber) {
    if (!cardNumber) return null;
    return `****-****-****-${cardNumber.slice(-4)}`;
  }
  
  static filterMedicalRecord(record, requestingUserRole, requestingUserId) {
    const baseData = {
      id: record.id,
      patientId: record.patientId,
      date: record.date,
      type: record.type
    };
    
    if (requestingUserRole === 'patient' && record.patientId !== requestingUserId) {
      return null; // Patients can't see other patients' records
    }
    
    if (requestingUserRole === 'doctor' || 
        (requestingUserRole === 'patient' && record.patientId === requestingUserId)) {
      return record.toObject();
    }
    
    return baseData;
  }
}

// Response filtering middleware
const filterResponse = (req, res, next) => {
  const originalJson = res.json;
  
  res.json = function(data) {
    if (data && req.user) {
      const filteredData = applyDataFilters(data, req.user.role, req.user.id);
      return originalJson.call(this, filteredData);
    }
    return originalJson.call(this, data);
  };
  
  next();
};
```

## 🔍 API Security Testing

### 1. Automated Security Testing

```javascript
// Security test suite for healthcare API
describe('API Security Tests', () => {
  describe('Authentication', () => {
    test('should reject requests without authentication', async () => {
      const response = await request(app)
        .get('/api/patients/123')
        .expect(401);
      
      expect(response.body.error).toContain('Access token required');
    });
    
    test('should reject expired tokens', async () => {
      const expiredToken = jwt.sign(
        { userId: '123', exp: Math.floor(Date.now() / 1000) - 3600 },
        process.env.JWT_ACCESS_SECRET
      );
      
      const response = await request(app)
        .get('/api/patients/123')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(403);
    });
  });
  
  describe('Authorization', () => {
    test('patients should not access other patients data', async () => {
      const patientToken = await generateTestToken({ userId: '123', role: 'patient' });
      
      const response = await request(app)
        .get('/api/patients/456') // Different patient ID
        .set('Authorization', `Bearer ${patientToken}`)
        .expect(403);
    });
    
    test('should enforce role-based permissions', async () => {
      const patientToken = await generateTestToken({ userId: '123', role: 'patient' });
      
      const response = await request(app)
        .post('/api/prescriptions')
        .set('Authorization', `Bearer ${patientToken}`)
        .send({ patientId: '123', medication: 'Test' })
        .expect(403);
    });
  });
  
  describe('Input Validation', () => {
    test('should prevent SQL injection in API parameters', async () => {
      const doctorToken = await generateTestToken({ userId: '123', role: 'doctor' });
      
      const response = await request(app)
        .get('/api/patients/123; DROP TABLE patients; --')
        .set('Authorization', `Bearer ${doctorToken}`)
        .expect(400);
    });
    
    test('should validate appointment data', async () => {
      const patientToken = await generateTestToken({ userId: '123', role: 'patient' });
      
      const response = await request(app)
        .post('/api/appointments')
        .set('Authorization', `Bearer ${patientToken}`)
        .send({
          doctorId: 'invalid-uuid',
          appointmentDate: 'invalid-date',
          duration: -30
        })
        .expect(400);
      
      expect(response.body.details).toHaveLength(3);
    });
  });
  
  describe('Rate Limiting', () => {
    test('should enforce rate limits', async () => {
      const token = await generateTestToken({ userId: '123', role: 'patient' });
      
      // Make multiple requests quickly
      const requests = Array(6).fill().map(() =>
        request(app)
          .post('/api/auth/login')
          .send({ email: '<EMAIL>', password: 'wrong' })
      );
      
      const responses = await Promise.all(requests);
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });
});
```

### 2. API Fuzzing and Penetration Testing

```javascript
// Automated API fuzzing
const fuzzTestData = {
  strings: [
    '<script>alert("xss")</script>',
    "'; DROP TABLE users; --",
    '../../../etc/passwd',
    'A'.repeat(10000), // Long string
    '\x00\x01\x02', // Binary data
    '${7*7}', // Template injection
    '{{7*7}}' // Template injection
  ],
  numbers: [
    -1, 0, 1,
    Number.MAX_SAFE_INTEGER,
    Number.MIN_SAFE_INTEGER,
    Infinity, -Infinity, NaN
  ],
  objects: [
    null, undefined, {},
    { __proto__: { isAdmin: true } }, // Prototype pollution
    { constructor: { prototype: { isAdmin: true } } }
  ]
};

describe('API Fuzzing Tests', () => {
  const endpoints = [
    { method: 'POST', path: '/api/patients', schema: apiSchemas.createPatient },
    { method: 'POST', path: '/api/appointments', schema: apiSchemas.createAppointment },
    { method: 'POST', path: '/api/payments', schema: apiSchemas.processPayment }
  ];
  
  endpoints.forEach(endpoint => {
    describe(`Fuzzing ${endpoint.method} ${endpoint.path}`, () => {
      test('should handle malicious string inputs', async () => {
        const token = await generateTestToken({ userId: '123', role: 'doctor' });
        
        for (const maliciousString of fuzzTestData.strings) {
          const testData = generateTestDataWithString(maliciousString);
          
          const response = await request(app)
            [endpoint.method.toLowerCase()](endpoint.path)
            .set('Authorization', `Bearer ${token}`)
            .send(testData);
          
          // Should not crash (status 500) or succeed with malicious data
          expect(response.status).not.toBe(500);
          if (response.status === 200) {
            // If it succeeds, verify data was sanitized
            expect(response.body).not.toContain('<script>');
            expect(response.body).not.toContain('DROP TABLE');
          }
        }
      });
    });
  });
});
```

## 📊 API Monitoring and Logging

### 1. Comprehensive API Monitoring

```javascript
// API monitoring middleware
const apiMonitoring = (req, res, next) => {
  const startTime = Date.now();
  
  // Log request
  logger.info('API Request', {
    method: req.method,
    path: req.path,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    timestamp: new Date().toISOString()
  });
  
  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(data) {
    const duration = Date.now() - startTime;
    
    logger.info('API Response', {
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      duration,
      userId: req.user?.id,
      timestamp: new Date().toISOString()
    });
    
    // Track metrics
    apiMetrics.recordRequest(req.method, req.path, res.statusCode, duration);
    
    return originalJson.call(this, data);
  };
  
  next();
};

// API metrics collection
class APIMetrics {
  static recordRequest(method, path, statusCode, duration) {
    // Record to metrics system (e.g., Prometheus)
    const labels = { method, path: this.normalizePath(path), status: statusCode };
    
    httpRequestsTotal.inc(labels);
    httpRequestDuration.observe(labels, duration / 1000);
    
    // Alert on high error rates
    if (statusCode >= 500) {
      this.checkErrorRate(path);
    }
  }
  
  static async checkErrorRate(path) {
    const errorCount = await redis.incr(`errors:${path}:${Math.floor(Date.now() / 60000)}`);
    await redis.expire(`errors:${path}:${Math.floor(Date.now() / 60000)}`, 300);
    
    if (errorCount > 10) { // More than 10 errors per minute
      await sendAlert({
        type: 'HIGH_ERROR_RATE',
        path,
        errorCount,
        timestamp: new Date()
      });
    }
  }
}
```

### 2. Security Event Detection

```javascript
// Real-time security monitoring
class SecurityMonitor {
  static async detectAnomalies(req, res, next) {
    const userId = req.user?.id;
    const ip = req.ip;
    const path = req.path;
    
    // Check for suspicious patterns
    await Promise.all([
      this.checkRapidRequests(userId, ip),
      this.checkUnusualEndpoints(userId, path),
      this.checkGeolocation(userId, ip),
      this.checkUserAgent(req.get('User-Agent'))
    ]);
    
    next();
  }
  
  static async checkRapidRequests(userId, ip) {
    const key = `rapid_requests:${userId || ip}`;
    const count = await redis.incr(key);
    await redis.expire(key, 60); // 1 minute window
    
    if (count > 100) { // More than 100 requests per minute
      await this.triggerAlert('RAPID_REQUESTS', { userId, ip, count });
    }
  }
  
  static async checkUnusualEndpoints(userId, path) {
    if (!userId) return;
    
    const userEndpoints = await redis.smembers(`user_endpoints:${userId}`);
    const isNewEndpoint = !userEndpoints.includes(path);
    
    if (isNewEndpoint) {
      await redis.sadd(`user_endpoints:${userId}`, path);
      
      // Check if accessing sensitive endpoints for first time
      const sensitiveEndpoints = ['/api/admin', '/api/payments', '/api/medical-records'];
      if (sensitiveEndpoints.some(endpoint => path.startsWith(endpoint))) {
        await this.triggerAlert('SENSITIVE_ENDPOINT_ACCESS', { userId, path });
      }
    }
  }
  
  static async triggerAlert(type, data) {
    logger.warn('Security Alert', { type, ...data });
    
    // Send to security team
    await sendSecurityAlert({
      type,
      data,
      timestamp: new Date(),
      severity: this.getAlertSeverity(type)
    });
  }
}
```

## 📚 Best Practices Summary

### API Design Security
1. **Authentication**: Use strong authentication mechanisms (JWT, OAuth 2.0)
2. **Authorization**: Implement fine-grained access control
3. **Validation**: Validate all inputs and sanitize outputs
4. **Rate Limiting**: Implement adaptive rate limiting
5. **Versioning**: Use API versioning for security updates

### Implementation Security
1. **HTTPS Only**: Enforce HTTPS for all API communications
2. **Security Headers**: Implement comprehensive security headers
3. **Error Handling**: Don't expose sensitive information in errors
4. **Logging**: Log all security-relevant events
5. **Monitoring**: Implement real-time security monitoring

### Testing and Maintenance
1. **Automated Testing**: Include security tests in CI/CD
2. **Penetration Testing**: Regular API security assessments
3. **Dependency Updates**: Keep dependencies updated
4. **Security Reviews**: Regular code security reviews

## 🔗 Additional Resources

- [OWASP API Security Top 10](https://owasp.org/www-project-api-security/)
- [OAuth 2.0 Security Best Practices](https://tools.ietf.org/html/draft-ietf-oauth-security-topics)
- [JWT Security Best Practices](https://tools.ietf.org/html/rfc8725)
- [Healthcare API Security Guidelines](../23-healthcare-security.md)

---

**Next**: [Frontend Security](06-frontend-security.md) | **Previous**: [Web Application Security](04-web-app-security.md)

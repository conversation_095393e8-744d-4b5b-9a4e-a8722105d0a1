# Frontend Security

## Overview

Frontend security is crucial for healthcare platforms as client-side applications handle sensitive patient data and payment information. This guide covers comprehensive security measures for modern frontend applications, including React, Vue, Angular, and vanilla JavaScript implementations.

## 🎯 Learning Objectives

By the end of this module, you will understand:
- Client-side security vulnerabilities and mitigations
- Secure authentication and session management in SPAs
- Content Security Policy (CSP) implementation
- Secure data handling and storage in browsers
- Frontend security testing methodologies

## 🔐 Authentication and Session Management

### 1. Secure Token Storage and Management

```javascript
// Secure token storage utility for healthcare platform
class SecureTokenManager {
  constructor() {
    this.tokenKey = 'healthcare_access_token';
    this.refreshKey = 'healthcare_refresh_token';
  }

  // Store tokens securely
  storeTokens(accessToken, refreshToken) {
    // Store access token in memory (most secure)
    this.accessToken = accessToken;
    
    // Store refresh token in httpOnly cookie (server-side only)
    // This should be set by the server, not client-side
    document.cookie = `${this.refreshKey}=${refreshToken}; HttpOnly; Secure; SameSite=Strict; Path=/`;
    
    // Alternative: Use sessionStorage for access token (less secure but practical)
    sessionStorage.setItem(this.tokenKey, accessToken);
  }

  getAccessToken() {
    // Return from memory first, fallback to sessionStorage
    return this.accessToken || sessionStorage.getItem(this.tokenKey);
  }

  clearTokens() {
    // Clear from memory
    this.accessToken = null;
    
    // Clear from sessionStorage
    sessionStorage.removeItem(this.tokenKey);
    
    // Clear refresh token cookie (server should handle this)
    document.cookie = `${this.refreshKey}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  }

  // Automatic token refresh
  async refreshAccessToken() {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include', // Include httpOnly cookies
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const { accessToken } = await response.json();
        this.accessToken = accessToken;
        sessionStorage.setItem(this.tokenKey, accessToken);
        return accessToken;
      } else {
        this.clearTokens();
        window.location.href = '/login';
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.clearTokens();
      window.location.href = '/login';
    }
  }

  // Automatic logout on token expiration
  setupTokenExpiration(token) {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expirationTime = payload.exp * 1000;
      const currentTime = Date.now();
      const timeUntilExpiration = expirationTime - currentTime;

      if (timeUntilExpiration > 0) {
        setTimeout(() => {
          this.refreshAccessToken();
        }, timeUntilExpiration - 60000); // Refresh 1 minute before expiration
      }
    } catch (error) {
      console.error('Invalid token format:', error);
    }
  }
}

// HTTP interceptor for automatic token handling
class SecureHTTPClient {
  constructor() {
    this.tokenManager = new SecureTokenManager();
    this.baseURL = process.env.REACT_APP_API_URL;
  }

  async request(url, options = {}) {
    const token = this.tokenManager.getAccessToken();
    
    const config = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
        ...(token && { Authorization: `Bearer ${token}` })
      }
    };

    try {
      let response = await fetch(`${this.baseURL}${url}`, config);
      
      // Handle token expiration
      if (response.status === 401) {
        const newToken = await this.tokenManager.refreshAccessToken();
        if (newToken) {
          config.headers.Authorization = `Bearer ${newToken}`;
          response = await fetch(`${this.baseURL}${url}`, config);
        }
      }

      return response;
    } catch (error) {
      console.error('HTTP request failed:', error);
      throw error;
    }
  }

  get(url, options) {
    return this.request(url, { ...options, method: 'GET' });
  }

  post(url, data, options) {
    return this.request(url, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data)
    });
  }
}
```

### 2. React Authentication Hook

```jsx
// Custom React hook for secure authentication
import { useState, useEffect, useContext, createContext } from 'react';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const tokenManager = new SecureTokenManager();
  const httpClient = new SecureHTTPClient();

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    const token = tokenManager.getAccessToken();
    if (token) {
      try {
        const response = await httpClient.get('/api/auth/me');
        if (response.ok) {
          const userData = await response.json();
          setUser(userData);
          tokenManager.setupTokenExpiration(token);
        } else {
          tokenManager.clearTokens();
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        tokenManager.clearTokens();
      }
    }
    setLoading(false);
  };

  const login = async (credentials) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials),
        credentials: 'include'
      });

      if (response.ok) {
        const { user, accessToken } = await response.json();
        tokenManager.storeTokens(accessToken);
        tokenManager.setupTokenExpiration(accessToken);
        setUser(user);
        return { success: true };
      } else {
        const error = await response.json();
        return { success: false, error: error.message };
      }
    } catch (error) {
      return { success: false, error: 'Login failed' };
    }
  };

  const logout = async () => {
    try {
      await httpClient.post('/api/auth/logout');
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      tokenManager.clearTokens();
      setUser(null);
    }
  };

  const value = {
    user,
    login,
    logout,
    loading,
    isAuthenticated: !!user
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Protected Route component
export const ProtectedRoute = ({ children, requiredRole }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole && user.role !== requiredRole) {
    return <Navigate to="/unauthorized" replace />;
  }

  return children;
};
```

## 🛡️ Content Security Policy (CSP)

### 1. Comprehensive CSP Implementation

```html
<!-- CSP meta tag for healthcare platform -->
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' https://js.stripe.com https://maps.googleapis.com;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  font-src 'self' https://fonts.gstatic.com;
  img-src 'self' data: https: blob:;
  connect-src 'self' https://api.stripe.com https://api.healthcare-platform.com;
  frame-src 'self' https://js.stripe.com;
  object-src 'none';
  base-uri 'self';
  form-action 'self';
  upgrade-insecure-requests;
  block-all-mixed-content;
">
```

```javascript
// Dynamic CSP configuration for different environments
class CSPManager {
  static generateCSP(environment) {
    const basePolicy = {
      'default-src': ["'self'"],
      'script-src': ["'self'"],
      'style-src': ["'self'", "'unsafe-inline'"],
      'font-src': ["'self'"],
      'img-src': ["'self'", "data:", "https:"],
      'connect-src': ["'self'"],
      'frame-src': ["'none'"],
      'object-src': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"]
    };

    // Environment-specific additions
    if (environment === 'development') {
      basePolicy['script-src'].push("'unsafe-eval'"); // For dev tools
      basePolicy['connect-src'].push('ws://localhost:*'); // For hot reload
    }

    // Healthcare platform specific sources
    basePolicy['script-src'].push('https://js.stripe.com');
    basePolicy['connect-src'].push('https://api.stripe.com');
    basePolicy['frame-src'] = ['https://js.stripe.com'];

    // Convert to CSP string
    return Object.entries(basePolicy)
      .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
      .join('; ');
  }

  static reportCSPViolations() {
    // CSP violation reporting
    document.addEventListener('securitypolicyviolation', (event) => {
      const violation = {
        blockedURI: event.blockedURI,
        violatedDirective: event.violatedDirective,
        originalPolicy: event.originalPolicy,
        sourceFile: event.sourceFile,
        lineNumber: event.lineNumber,
        timestamp: new Date().toISOString()
      };

      // Send violation report to security team
      fetch('/api/security/csp-violation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(violation)
      }).catch(console.error);
    });
  }
}
```

### 2. Nonce-based Script Loading

```javascript
// Secure script loading with nonces
class SecureScriptLoader {
  constructor() {
    this.nonce = this.generateNonce();
    this.loadedScripts = new Set();
  }

  generateNonce() {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode.apply(null, array));
  }

  loadScript(src, integrity) {
    return new Promise((resolve, reject) => {
      if (this.loadedScripts.has(src)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = src;
      script.nonce = this.nonce;
      
      if (integrity) {
        script.integrity = integrity;
        script.crossOrigin = 'anonymous';
      }

      script.onload = () => {
        this.loadedScripts.add(src);
        resolve();
      };
      
      script.onerror = () => {
        reject(new Error(`Failed to load script: ${src}`));
      };

      document.head.appendChild(script);
    });
  }

  // Load Stripe.js securely
  async loadStripe() {
    await this.loadScript(
      'https://js.stripe.com/v3/',
      'sha384-...' // Add actual integrity hash
    );
    return window.Stripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY);
  }
}
```

## 🔒 Secure Data Handling

### 1. Client-Side Data Encryption

```javascript
// Client-side encryption for sensitive data
class ClientEncryption {
  constructor() {
    this.algorithm = 'AES-GCM';
    this.keyLength = 256;
  }

  // Generate encryption key from password
  async deriveKey(password, salt) {
    const encoder = new TextEncoder();
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      encoder.encode(password),
      'PBKDF2',
      false,
      ['deriveKey']
    );

    return crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: salt,
        iterations: 100000,
        hash: 'SHA-256'
      },
      keyMaterial,
      { name: this.algorithm, length: this.keyLength },
      false,
      ['encrypt', 'decrypt']
    );
  }

  // Encrypt sensitive data before storing
  async encryptData(data, password) {
    const encoder = new TextEncoder();
    const salt = crypto.getRandomValues(new Uint8Array(16));
    const iv = crypto.getRandomValues(new Uint8Array(12));
    
    const key = await this.deriveKey(password, salt);
    const encodedData = encoder.encode(JSON.stringify(data));

    const encryptedData = await crypto.subtle.encrypt(
      { name: this.algorithm, iv: iv },
      key,
      encodedData
    );

    // Combine salt, iv, and encrypted data
    const result = new Uint8Array(salt.length + iv.length + encryptedData.byteLength);
    result.set(salt, 0);
    result.set(iv, salt.length);
    result.set(new Uint8Array(encryptedData), salt.length + iv.length);

    return btoa(String.fromCharCode.apply(null, result));
  }

  // Decrypt sensitive data
  async decryptData(encryptedData, password) {
    const data = new Uint8Array(atob(encryptedData).split('').map(c => c.charCodeAt(0)));
    
    const salt = data.slice(0, 16);
    const iv = data.slice(16, 28);
    const encrypted = data.slice(28);

    const key = await this.deriveKey(password, salt);
    
    try {
      const decryptedData = await crypto.subtle.decrypt(
        { name: this.algorithm, iv: iv },
        key,
        encrypted
      );

      const decoder = new TextDecoder();
      return JSON.parse(decoder.decode(decryptedData));
    } catch (error) {
      throw new Error('Decryption failed - invalid password or corrupted data');
    }
  }
}

// Secure local storage wrapper
class SecureStorage {
  constructor() {
    this.encryption = new ClientEncryption();
    this.sessionKey = this.generateSessionKey();
  }

  generateSessionKey() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode.apply(null, array));
  }

  async setItem(key, value, encrypt = true) {
    try {
      let dataToStore = value;
      
      if (encrypt) {
        dataToStore = await this.encryption.encryptData(value, this.sessionKey);
      }
      
      sessionStorage.setItem(key, JSON.stringify({
        encrypted: encrypt,
        data: dataToStore,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.error('Failed to store data:', error);
    }
  }

  async getItem(key) {
    try {
      const stored = sessionStorage.getItem(key);
      if (!stored) return null;

      const { encrypted, data, timestamp } = JSON.parse(stored);
      
      // Check if data is too old (24 hours)
      if (Date.now() - timestamp > 24 * 60 * 60 * 1000) {
        this.removeItem(key);
        return null;
      }

      if (encrypted) {
        return await this.encryption.decryptData(data, this.sessionKey);
      }
      
      return data;
    } catch (error) {
      console.error('Failed to retrieve data:', error);
      this.removeItem(key);
      return null;
    }
  }

  removeItem(key) {
    sessionStorage.removeItem(key);
  }

  clear() {
    sessionStorage.clear();
  }
}
```

### 2. Secure Form Handling

```jsx
// Secure form component for healthcare data
import { useState, useCallback } from 'react';
import DOMPurify from 'dompurify';

const SecurePatientForm = () => {
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Input sanitization
  const sanitizeInput = useCallback((value, type) => {
    if (typeof value !== 'string') return value;
    
    switch (type) {
      case 'html':
        return DOMPurify.sanitize(value);
      case 'text':
        return value.replace(/<[^>]*>/g, '').trim();
      case 'number':
        return value.replace(/[^0-9.-]/g, '');
      case 'phone':
        return value.replace(/[^0-9+\-\s\(\)]/g, '');
      case 'email':
        return value.toLowerCase().trim();
      default:
        return DOMPurify.sanitize(value);
    }
  }, []);

  // Client-side validation
  const validateField = useCallback((name, value) => {
    const validators = {
      firstName: (val) => val.length >= 2 && val.length <= 50 && /^[a-zA-Z\s]+$/.test(val),
      lastName: (val) => val.length >= 2 && val.length <= 50 && /^[a-zA-Z\s]+$/.test(val),
      email: (val) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
      phone: (val) => /^\+?[\d\s\-\(\)]{10,}$/.test(val),
      ssn: (val) => /^\d{3}-\d{2}-\d{4}$/.test(val),
      dateOfBirth: (val) => {
        const date = new Date(val);
        const now = new Date();
        return date < now && date > new Date('1900-01-01');
      }
    };

    return validators[name] ? validators[name](value) : true;
  }, []);

  const handleInputChange = useCallback((e) => {
    const { name, value, type } = e.target;
    const sanitizedValue = sanitizeInput(value, type);
    
    setFormData(prev => ({
      ...prev,
      [name]: sanitizedValue
    }));

    // Real-time validation
    if (sanitizedValue) {
      const isValid = validateField(name, sanitizedValue);
      setErrors(prev => ({
        ...prev,
        [name]: isValid ? null : `Invalid ${name}`
      }));
    }
  }, [sanitizeInput, validateField]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Final validation
      const validationErrors = {};
      Object.entries(formData).forEach(([key, value]) => {
        if (!validateField(key, value)) {
          validationErrors[key] = `Invalid ${key}`;
        }
      });

      if (Object.keys(validationErrors).length > 0) {
        setErrors(validationErrors);
        return;
      }

      // Submit to API
      const response = await fetch('/api/patients', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest' // CSRF protection
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        // Success handling
        setFormData({});
        setErrors({});
      } else {
        const error = await response.json();
        setErrors({ submit: error.message });
      }
    } catch (error) {
      setErrors({ submit: 'Submission failed. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} noValidate>
      <div>
        <label htmlFor="firstName">First Name *</label>
        <input
          type="text"
          id="firstName"
          name="firstName"
          value={formData.firstName || ''}
          onChange={handleInputChange}
          required
          maxLength={50}
          pattern="[a-zA-Z\s]+"
          autoComplete="given-name"
        />
        {errors.firstName && <span className="error">{errors.firstName}</span>}
      </div>

      <div>
        <label htmlFor="email">Email *</label>
        <input
          type="email"
          id="email"
          name="email"
          value={formData.email || ''}
          onChange={handleInputChange}
          required
          autoComplete="email"
        />
        {errors.email && <span className="error">{errors.email}</span>}
      </div>

      <div>
        <label htmlFor="ssn">SSN *</label>
        <input
          type="text"
          id="ssn"
          name="ssn"
          value={formData.ssn || ''}
          onChange={handleInputChange}
          required
          pattern="\d{3}-\d{2}-\d{4}"
          placeholder="***********"
          autoComplete="off"
        />
        {errors.ssn && <span className="error">{errors.ssn}</span>}
      </div>

      {errors.submit && <div className="error">{errors.submit}</div>}

      <button 
        type="submit" 
        disabled={isSubmitting || Object.values(errors).some(Boolean)}
      >
        {isSubmitting ? 'Submitting...' : 'Submit'}
      </button>
    </form>
  );
};
```

## 🧪 Frontend Security Testing

### 1. Automated Security Testing

```javascript
// Jest tests for frontend security
describe('Frontend Security', () => {
  describe('Input Sanitization', () => {
    test('should sanitize XSS attempts', () => {
      const maliciousInput = '<script>alert("xss")</script>';
      const sanitized = DOMPurify.sanitize(maliciousInput);
      expect(sanitized).not.toContain('<script>');
    });

    test('should validate form inputs', () => {
      const form = new SecurePatientForm();
      expect(form.validateField('email', 'invalid-email')).toBe(false);
      expect(form.validateField('email', '<EMAIL>')).toBe(true);
    });
  });

  describe('Token Management', () => {
    test('should store tokens securely', () => {
      const tokenManager = new SecureTokenManager();
      const token = 'test-token';
      
      tokenManager.storeTokens(token);
      expect(tokenManager.getAccessToken()).toBe(token);
      
      // Should not be in localStorage
      expect(localStorage.getItem('healthcare_access_token')).toBeNull();
    });

    test('should clear tokens on logout', () => {
      const tokenManager = new SecureTokenManager();
      tokenManager.storeTokens('test-token');
      tokenManager.clearTokens();
      
      expect(tokenManager.getAccessToken()).toBeNull();
    });
  });

  describe('CSP Compliance', () => {
    test('should not execute inline scripts', () => {
      const script = document.createElement('script');
      script.innerHTML = 'window.malicious = true;';
      document.head.appendChild(script);
      
      expect(window.malicious).toBeUndefined();
    });
  });
});

// Cypress E2E security tests
describe('E2E Security Tests', () => {
  it('should prevent XSS in form inputs', () => {
    cy.visit('/patient-registration');
    
    const xssPayload = '<img src=x onerror=alert("xss")>';
    cy.get('[name="firstName"]').type(xssPayload);
    cy.get('form').submit();
    
    // Should not execute the XSS
    cy.window().should('not.have.property', 'alert');
  });

  it('should redirect unauthenticated users', () => {
    cy.visit('/dashboard');
    cy.url().should('include', '/login');
  });

  it('should enforce role-based access', () => {
    cy.loginAs('patient');
    cy.visit('/admin');
    cy.url().should('include', '/unauthorized');
  });
});
```

### 2. Security Audit Tools Integration

```javascript
// Webpack plugin for security auditing
const SecurityAuditPlugin = require('./security-audit-plugin');

module.exports = {
  plugins: [
    new SecurityAuditPlugin({
      // Check for known vulnerable dependencies
      auditDependencies: true,
      
      // Scan for potential XSS vulnerabilities
      scanForXSS: true,
      
      // Validate CSP compliance
      validateCSP: true,
      
      // Check for exposed secrets
      scanSecrets: true,
      
      // Output security report
      outputPath: './security-report.json'
    })
  ]
};

// Custom security audit plugin
class SecurityAuditPlugin {
  apply(compiler) {
    compiler.hooks.emit.tapAsync('SecurityAuditPlugin', (compilation, callback) => {
      const securityIssues = [];
      
      // Scan for potential security issues
      Object.keys(compilation.assets).forEach(filename => {
        const source = compilation.assets[filename].source();
        
        // Check for exposed API keys
        const apiKeyPattern = /(?:api[_-]?key|secret|token)[\s]*[:=][\s]*['"][a-zA-Z0-9]{20,}['"]/gi;
        if (apiKeyPattern.test(source)) {
          securityIssues.push({
            type: 'EXPOSED_SECRET',
            file: filename,
            message: 'Potential API key or secret found'
          });
        }
        
        // Check for eval usage
        if (source.includes('eval(')) {
          securityIssues.push({
            type: 'UNSAFE_EVAL',
            file: filename,
            message: 'Use of eval() detected'
          });
        }
      });
      
      if (securityIssues.length > 0) {
        console.warn('Security issues found:', securityIssues);
        
        // Optionally fail the build
        if (process.env.NODE_ENV === 'production') {
          callback(new Error('Security issues found in production build'));
          return;
        }
      }
      
      callback();
    });
  }
}
```

## 📚 Best Practices Summary

### Authentication & Authorization
1. **Token Storage**: Use memory/sessionStorage for access tokens, httpOnly cookies for refresh tokens
2. **Automatic Refresh**: Implement automatic token refresh before expiration
3. **Role-Based Access**: Implement client-side route protection with server-side validation
4. **Session Management**: Implement proper session timeout and cleanup

### Data Security
1. **Input Sanitization**: Sanitize all user inputs to prevent XSS
2. **Output Encoding**: Properly encode data when displaying to users
3. **Client-Side Encryption**: Encrypt sensitive data before local storage
4. **Secure Communication**: Use HTTPS for all communications

### Content Security
1. **CSP Implementation**: Implement strict Content Security Policy
2. **Script Integrity**: Use Subresource Integrity (SRI) for external scripts
3. **Nonce-based Loading**: Use nonces for dynamic script loading
4. **Regular Audits**: Perform regular security audits and dependency checks

## 🔗 Additional Resources

- [OWASP Frontend Security](https://owasp.org/www-project-top-ten/)
- [Content Security Policy Guide](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP)
- [Web Crypto API](https://developer.mozilla.org/en-US/docs/Web/API/Web_Crypto_API)
- [React Security Best Practices](https://snyk.io/blog/10-react-security-best-practices/)

---

**Next**: [Cloud Security](07-cloud-security.md) | **Previous**: [API Security](05-api-security.md)

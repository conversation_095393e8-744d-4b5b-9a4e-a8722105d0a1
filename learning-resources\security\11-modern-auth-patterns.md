# Modern Authentication Patterns

## 🔐 OAuth 2.0 Implementation

### OAuth 2.0 Flow Types

#### Authorization Code Flow (Recommended for Web Apps)
```python
import secrets
import hashlib
import base64
from urllib.parse import urlencode, parse_qs

class OAuth2AuthorizationCodeFlow:
    def __init__(self, client_id, client_secret, redirect_uri):
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri
        self.authorization_endpoint = "https://auth.healthcare-platform.com/oauth/authorize"
        self.token_endpoint = "https://auth.healthcare-platform.com/oauth/token"
    
    def generate_authorization_url(self, scopes, state=None):
        """Generate authorization URL with PKCE"""
        # Generate PKCE parameters
        code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode()).digest()
        ).decode('utf-8').rstrip('=')
        
        # Generate state parameter for CSRF protection
        if not state:
            state = secrets.token_urlsafe(32)
        
        params = {
            'response_type': 'code',
            'client_id': self.client_id,
            'redirect_uri': self.redirect_uri,
            'scope': ' '.join(scopes),
            'state': state,
            'code_challenge': code_challenge,
            'code_challenge_method': 'S256'
        }
        
        # Store code_verifier and state for later verification
        self.store_oauth_session(state, {
            'code_verifier': code_verifier,
            'scopes': scopes,
            'timestamp': datetime.now()
        })
        
        return f"{self.authorization_endpoint}?{urlencode(params)}"
    
    def exchange_code_for_tokens(self, authorization_code, state):
        """Exchange authorization code for access token"""
        # Verify state parameter
        oauth_session = self.get_oauth_session(state)
        if not oauth_session:
            raise ValueError("Invalid state parameter")
        
        token_data = {
            'grant_type': 'authorization_code',
            'code': authorization_code,
            'redirect_uri': self.redirect_uri,
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'code_verifier': oauth_session['code_verifier']
        }
        
        response = requests.post(self.token_endpoint, data=token_data)
        
        if response.status_code == 200:
            tokens = response.json()
            
            # Validate and store tokens
            self.validate_tokens(tokens)
            self.store_user_tokens(tokens)
            
            return tokens
        else:
            raise Exception(f"Token exchange failed: {response.text}")
```

#### Client Credentials Flow (for Service-to-Service)
```python
class OAuth2ClientCredentialsFlow:
    def __init__(self, client_id, client_secret, token_endpoint):
        self.client_id = client_id
        self.client_secret = client_secret
        self.token_endpoint = token_endpoint
    
    def get_access_token(self, scopes):
        """Get access token using client credentials"""
        token_data = {
            'grant_type': 'client_credentials',
            'scope': ' '.join(scopes),
            'client_id': self.client_id,
            'client_secret': self.client_secret
        }
        
        response = requests.post(
            self.token_endpoint,
            data=token_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )
        
        if response.status_code == 200:
            token_response = response.json()
            
            # Cache token with expiration
            self.cache_access_token(token_response)
            
            return token_response['access_token']
        else:
            raise Exception(f"Failed to get access token: {response.text}")
    
    def get_cached_token(self, scopes):
        """Get cached access token if still valid"""
        cache_key = f"client_credentials:{':'.join(sorted(scopes))}"
        cached_token = self.get_from_cache(cache_key)
        
        if cached_token and not self.is_token_expired(cached_token):
            return cached_token['access_token']
        
        return None
```

## 🎫 JSON Web Tokens (JWT)

### Secure JWT Implementation
```python
import jwt
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import serialization

class SecureJWTManager:
    def __init__(self, private_key_path, public_key_path):
        self.private_key = self.load_private_key(private_key_path)
        self.public_key = self.load_public_key(public_key_path)
        self.algorithm = 'RS256'
        self.issuer = 'healthcare-platform.com'
    
    def create_access_token(self, user_id, scopes, expires_in=3600):
        """Create secure access token"""
        now = datetime.utcnow()
        
        payload = {
            'iss': self.issuer,  # Issuer
            'sub': str(user_id),  # Subject (user ID)
            'aud': 'healthcare-api',  # Audience
            'iat': now,  # Issued at
            'exp': now + timedelta(seconds=expires_in),  # Expiration
            'nbf': now,  # Not before
            'jti': secrets.token_hex(16),  # JWT ID
            'scope': ' '.join(scopes),
            'token_type': 'access_token'
        }
        
        token = jwt.encode(
            payload,
            self.private_key,
            algorithm=self.algorithm,
            headers={'kid': self.get_key_id()}
        )
        
        return token
    
    def create_refresh_token(self, user_id, expires_in=2592000):  # 30 days
        """Create secure refresh token"""
        now = datetime.utcnow()
        
        payload = {
            'iss': self.issuer,
            'sub': str(user_id),
            'aud': 'healthcare-api',
            'iat': now,
            'exp': now + timedelta(seconds=expires_in),
            'jti': secrets.token_hex(16),
            'token_type': 'refresh_token'
        }
        
        token = jwt.encode(payload, self.private_key, algorithm=self.algorithm)
        
        # Store refresh token hash for revocation checking
        self.store_refresh_token_hash(payload['jti'], user_id)
        
        return token
    
    def validate_token(self, token, expected_audience='healthcare-api'):
        """Validate and decode JWT token"""
        try:
            payload = jwt.decode(
                token,
                self.public_key,
                algorithms=[self.algorithm],
                audience=expected_audience,
                issuer=self.issuer,
                options={
                    'verify_signature': True,
                    'verify_exp': True,
                    'verify_nbf': True,
                    'verify_iat': True,
                    'verify_aud': True,
                    'verify_iss': True
                }
            )
            
            # Check if token is revoked
            if self.is_token_revoked(payload.get('jti')):
                raise jwt.InvalidTokenError("Token has been revoked")
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise ValueError("Token has expired")
        except jwt.InvalidTokenError as e:
            raise ValueError(f"Invalid token: {str(e)}")
    
    def refresh_access_token(self, refresh_token):
        """Refresh access token using refresh token"""
        try:
            payload = self.validate_token(refresh_token)
            
            if payload.get('token_type') != 'refresh_token':
                raise ValueError("Invalid token type")
            
            user_id = payload['sub']
            
            # Get user's current scopes
            user_scopes = self.get_user_scopes(user_id)
            
            # Create new access token
            new_access_token = self.create_access_token(user_id, user_scopes)
            
            return {
                'access_token': new_access_token,
                'token_type': 'Bearer',
                'expires_in': 3600
            }
            
        except Exception as e:
            raise ValueError(f"Failed to refresh token: {str(e)}")
```

### JWT Security Best Practices
```python
class JWTSecurityValidator:
    def __init__(self):
        self.max_token_age = timedelta(hours=1)  # Access tokens
        self.max_refresh_age = timedelta(days=30)  # Refresh tokens
        self.revoked_tokens = set()  # In production, use Redis/database
    
    def validate_jwt_security(self, token):
        """Comprehensive JWT security validation"""
        try:
            # Decode without verification to check structure
            unverified_payload = jwt.decode(token, options={"verify_signature": False})
            
            security_checks = {
                'has_expiration': 'exp' in unverified_payload,
                'has_issued_at': 'iat' in unverified_payload,
                'has_not_before': 'nbf' in unverified_payload,
                'has_jwt_id': 'jti' in unverified_payload,
                'has_issuer': 'iss' in unverified_payload,
                'has_audience': 'aud' in unverified_payload,
                'reasonable_expiration': self.check_reasonable_expiration(unverified_payload),
                'not_revoked': unverified_payload.get('jti') not in self.revoked_tokens
            }
            
            return all(security_checks.values()), security_checks
            
        except Exception as e:
            return False, {'error': str(e)}
    
    def check_reasonable_expiration(self, payload):
        """Check if token expiration is reasonable"""
        if 'exp' not in payload or 'iat' not in payload:
            return False
        
        exp_time = datetime.fromtimestamp(payload['exp'])
        iat_time = datetime.fromtimestamp(payload['iat'])
        token_lifetime = exp_time - iat_time
        
        # Access tokens should not live longer than 1 hour
        # Refresh tokens should not live longer than 30 days
        token_type = payload.get('token_type', 'access_token')
        
        if token_type == 'access_token':
            return token_lifetime <= self.max_token_age
        elif token_type == 'refresh_token':
            return token_lifetime <= self.max_refresh_age
        
        return False
```

## 🔑 OpenID Connect (OIDC)

### OIDC Implementation
```python
class OpenIDConnectProvider:
    def __init__(self, issuer_url):
        self.issuer_url = issuer_url
        self.discovery_document = self.fetch_discovery_document()
        self.jwks = self.fetch_jwks()
    
    def fetch_discovery_document(self):
        """Fetch OIDC discovery document"""
        discovery_url = f"{self.issuer_url}/.well-known/openid-configuration"
        response = requests.get(discovery_url)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception("Failed to fetch OIDC discovery document")
    
    def create_id_token(self, user_info, client_id, nonce=None):
        """Create OIDC ID token"""
        now = datetime.utcnow()
        
        id_token_payload = {
            'iss': self.issuer_url,
            'sub': str(user_info['user_id']),
            'aud': client_id,
            'exp': now + timedelta(hours=1),
            'iat': now,
            'auth_time': user_info.get('auth_time', now),
            'nonce': nonce,
            
            # Standard claims
            'email': user_info.get('email'),
            'email_verified': user_info.get('email_verified', False),
            'name': user_info.get('name'),
            'given_name': user_info.get('given_name'),
            'family_name': user_info.get('family_name'),
            'picture': user_info.get('picture'),
            
            # Healthcare-specific claims
            'role': user_info.get('role'),
            'department': user_info.get('department'),
            'license_number': user_info.get('license_number'),
            'specialization': user_info.get('specialization')
        }
        
        # Remove None values
        id_token_payload = {k: v for k, v in id_token_payload.items() if v is not None}
        
        return jwt.encode(
            id_token_payload,
            self.private_key,
            algorithm='RS256',
            headers={'kid': self.get_key_id()}
        )
    
    def validate_id_token(self, id_token, client_id, nonce=None):
        """Validate OIDC ID token"""
        try:
            payload = jwt.decode(
                id_token,
                self.public_key,
                algorithms=['RS256'],
                audience=client_id,
                issuer=self.issuer_url
            )
            
            # Validate nonce if provided
            if nonce and payload.get('nonce') != nonce:
                raise ValueError("Invalid nonce")
            
            # Validate auth_time if max_age was specified
            if 'auth_time' in payload:
                auth_time = datetime.fromtimestamp(payload['auth_time'])
                if datetime.utcnow() - auth_time > timedelta(hours=24):
                    raise ValueError("Authentication too old")
            
            return payload
            
        except jwt.InvalidTokenError as e:
            raise ValueError(f"Invalid ID token: {str(e)}")
```

## 🛡️ Multi-Factor Authentication (MFA)

### TOTP Implementation
```python
import pyotp
import qrcode
from io import BytesIO
import base64

class TOTPManager:
    def __init__(self, issuer_name="Healthcare Platform"):
        self.issuer_name = issuer_name
        self.window = 1  # Allow 1 time step tolerance
    
    def generate_secret(self, user_email):
        """Generate TOTP secret for user"""
        secret = pyotp.random_base32()
        
        # Store encrypted secret in database
        encrypted_secret = self.encrypt_secret(secret)
        self.store_user_totp_secret(user_email, encrypted_secret)
        
        return secret
    
    def generate_qr_code(self, user_email, secret):
        """Generate QR code for TOTP setup"""
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user_email,
            issuer_name=self.issuer_name
        )
        
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to base64 for web display
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{img_str}"
    
    def verify_totp(self, user_email, token):
        """Verify TOTP token"""
        secret = self.get_user_totp_secret(user_email)
        if not secret:
            return False
        
        totp = pyotp.TOTP(secret)
        
        # Verify with time window tolerance
        return totp.verify(token, valid_window=self.window)
    
    def generate_backup_codes(self, user_email, count=10):
        """Generate backup codes for account recovery"""
        backup_codes = []
        
        for _ in range(count):
            code = secrets.token_hex(4).upper()
            backup_codes.append(code)
        
        # Hash and store backup codes
        hashed_codes = [
            hashlib.sha256(code.encode()).hexdigest() 
            for code in backup_codes
        ]
        
        self.store_backup_codes(user_email, hashed_codes)
        
        return backup_codes
```

### WebAuthn Implementation
```python
class WebAuthnManager:
    def __init__(self, rp_id, rp_name):
        self.rp_id = rp_id  # Relying Party ID (domain)
        self.rp_name = rp_name  # Relying Party Name
    
    def generate_registration_options(self, user_id, user_email, user_name):
        """Generate WebAuthn registration options"""
        user_handle = base64.urlsafe_b64encode(str(user_id).encode()).decode().rstrip('=')
        challenge = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode().rstrip('=')
        
        # Store challenge for verification
        self.store_challenge(user_id, challenge)
        
        registration_options = {
            'challenge': challenge,
            'rp': {
                'name': self.rp_name,
                'id': self.rp_id
            },
            'user': {
                'id': user_handle,
                'name': user_email,
                'displayName': user_name
            },
            'pubKeyCredParams': [
                {'alg': -7, 'type': 'public-key'},  # ES256
                {'alg': -257, 'type': 'public-key'}  # RS256
            ],
            'authenticatorSelection': {
                'authenticatorAttachment': 'platform',
                'userVerification': 'required',
                'residentKey': 'preferred'
            },
            'timeout': 60000,
            'attestation': 'direct'
        }
        
        return registration_options
    
    def verify_registration(self, user_id, credential_response):
        """Verify WebAuthn registration response"""
        stored_challenge = self.get_stored_challenge(user_id)
        
        if not stored_challenge:
            raise ValueError("No challenge found for user")
        
        # Verify challenge matches
        if credential_response['response']['clientDataJSON']['challenge'] != stored_challenge:
            raise ValueError("Challenge mismatch")
        
        # Verify origin
        client_data = json.loads(
            base64.urlsafe_b64decode(
                credential_response['response']['clientDataJSON']
            )
        )
        
        if client_data['origin'] != f"https://{self.rp_id}":
            raise ValueError("Origin mismatch")
        
        # Store credential for future authentication
        credential_data = {
            'credential_id': credential_response['id'],
            'public_key': credential_response['response']['publicKey'],
            'sign_count': credential_response['response']['signCount'],
            'user_id': user_id,
            'created_at': datetime.now()
        }
        
        self.store_credential(credential_data)
        
        return True
```

## 🔐 Session Management

### Secure Session Implementation
```python
import redis
from datetime import datetime, timedelta

class SecureSessionManager:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.session_timeout = timedelta(minutes=30)
        self.absolute_timeout = timedelta(hours=8)
    
    def create_session(self, user_id, user_agent, ip_address):
        """Create secure session"""
        session_id = secrets.token_urlsafe(32)
        
        session_data = {
            'user_id': user_id,
            'created_at': datetime.utcnow().isoformat(),
            'last_activity': datetime.utcnow().isoformat(),
            'ip_address': ip_address,
            'user_agent': user_agent,
            'csrf_token': secrets.token_urlsafe(32),
            'mfa_verified': False
        }
        
        # Store session with expiration
        self.redis.setex(
            f"session:{session_id}",
            self.session_timeout,
            json.dumps(session_data)
        )
        
        # Track active sessions for user
        self.redis.sadd(f"user_sessions:{user_id}", session_id)
        
        return session_id, session_data['csrf_token']
    
    def validate_session(self, session_id, ip_address, user_agent):
        """Validate and refresh session"""
        session_data = self.redis.get(f"session:{session_id}")
        
        if not session_data:
            return None
        
        session = json.loads(session_data)
        
        # Security checks
        if session['ip_address'] != ip_address:
            self.invalidate_session(session_id)
            raise SecurityError("IP address mismatch")
        
        if session['user_agent'] != user_agent:
            self.invalidate_session(session_id)
            raise SecurityError("User agent mismatch")
        
        # Check absolute timeout
        created_at = datetime.fromisoformat(session['created_at'])
        if datetime.utcnow() - created_at > self.absolute_timeout:
            self.invalidate_session(session_id)
            return None
        
        # Update last activity
        session['last_activity'] = datetime.utcnow().isoformat()
        
        # Refresh session expiration
        self.redis.setex(
            f"session:{session_id}",
            self.session_timeout,
            json.dumps(session)
        )
        
        return session
    
    def invalidate_session(self, session_id):
        """Invalidate specific session"""
        session_data = self.redis.get(f"session:{session_id}")
        
        if session_data:
            session = json.loads(session_data)
            user_id = session['user_id']
            
            # Remove from user's active sessions
            self.redis.srem(f"user_sessions:{user_id}", session_id)
        
        # Delete session
        self.redis.delete(f"session:{session_id}")
    
    def invalidate_all_user_sessions(self, user_id):
        """Invalidate all sessions for a user"""
        session_ids = self.redis.smembers(f"user_sessions:{user_id}")
        
        for session_id in session_ids:
            self.redis.delete(f"session:{session_id.decode()}")
        
        self.redis.delete(f"user_sessions:{user_id}")
```

## 📚 Next Steps

1. **Study**: Identity Management (12-identity-management.md)
2. **Implement**: OAuth 2.0 server with PKCE
3. **Practice**: JWT security validation
4. **Setup**: WebAuthn for passwordless authentication

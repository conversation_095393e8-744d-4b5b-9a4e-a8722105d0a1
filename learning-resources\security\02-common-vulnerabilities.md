# Common Vulnerabilities - OWASP Top 10 2021

## 🚨 OWASP Top 10 Web Application Security Risks

### A01:2021 – Broken Access Control
**Risk Level**: Critical | **Prevalence**: High

#### Description
Restrictions on authenticated users are not properly enforced, allowing attackers to access unauthorized functionality or data.

#### Common Scenarios
```javascript
// Vulnerable: Direct object reference
app.get('/api/users/:id/medical-records', (req, res) => {
    // No authorization check - any authenticated user can access any records
    const records = getMedicalRecords(req.params.id);
    res.json(records);
});

// Secure: Proper authorization
app.get('/api/users/:id/medical-records', authenticateUser, (req, res) => {
    // Check if user can access this specific record
    if (!canAccessMedicalRecord(req.user.id, req.params.id)) {
        return res.status(403).json({ error: 'Access denied' });
    }
    const records = getMedicalRecords(req.params.id);
    res.json(records);
});
```

#### Prevention
- Implement proper access control mechanisms
- Use attribute-based or role-based access control
- Deny by default
- Log access control failures

### A02:2021 – Cryptographic Failures
**Risk Level**: High | **Prevalence**: Medium

#### Description
Failures related to cryptography that often lead to exposure of sensitive data.

#### Common Issues
```python
# Vulnerable: Weak encryption
import base64

def encrypt_patient_data(data):
    # Base64 is encoding, not encryption!
    return base64.b64encode(data.encode()).decode()

# Secure: Strong encryption
from cryptography.fernet import Fernet
import os

def encrypt_patient_data(data):
    key = os.environ.get('ENCRYPTION_KEY')
    if not key:
        raise ValueError("Encryption key not found")
    
    f = Fernet(key.encode())
    encrypted_data = f.encrypt(data.encode())
    return encrypted_data

# Secure: Password hashing
import bcrypt

def hash_password(password):
    # Generate salt and hash password
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed

def verify_password(password, hashed):
    return bcrypt.checkpw(password.encode('utf-8'), hashed)
```

#### Prevention
- Use strong, up-to-date encryption algorithms
- Proper key management
- Encrypt data in transit and at rest
- Use secure random number generators

### A03:2021 – Injection
**Risk Level**: High | **Prevalence**: High

#### Description
Hostile data is sent to an interpreter as part of a command or query.

#### SQL Injection Example
```python
# Vulnerable
def get_patient_by_id(patient_id):
    query = f"SELECT * FROM patients WHERE id = {patient_id}"
    return execute_query(query)

# Secure: Parameterized queries
def get_patient_by_id(patient_id):
    query = "SELECT * FROM patients WHERE id = %s"
    return execute_query(query, (patient_id,))

# Using SQLAlchemy ORM (Recommended)
from sqlalchemy.orm import sessionmaker

def get_patient_by_id(patient_id):
    session = sessionmaker()
    return session.query(Patient).filter(Patient.id == patient_id).first()
```

#### NoSQL Injection Prevention
```javascript
// Vulnerable MongoDB query
app.post('/api/login', (req, res) => {
    const { username, password } = req.body;
    // Vulnerable to NoSQL injection
    User.findOne({ username: username, password: password });
});

// Secure MongoDB query
app.post('/api/login', (req, res) => {
    const { username, password } = req.body;
    
    // Validate input types
    if (typeof username !== 'string' || typeof password !== 'string') {
        return res.status(400).json({ error: 'Invalid input' });
    }
    
    // Use proper authentication with hashed passwords
    User.findOne({ username: username }).then(user => {
        if (user && bcrypt.compareSync(password, user.hashedPassword)) {
            // Authentication successful
        }
    });
});
```

### A04:2021 – Insecure Design
**Risk Level**: High | **Prevalence**: Medium

#### Description
Risks related to design and architectural flaws.

#### Secure Design Principles
```python
# Secure design example: Rate limiting for payment processing
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

@app.route('/api/process-payment', methods=['POST'])
@limiter.limit("5 per minute")  # Strict limit for payment processing
@require_authentication
def process_payment():
    # Additional validation for payment processing
    if not validate_payment_amount(request.json.get('amount')):
        return jsonify({'error': 'Invalid payment amount'}), 400
    
    # Implement transaction logging
    log_payment_attempt(request.user.id, request.json)
    
    # Process payment with proper error handling
    try:
        result = stripe.charge.create(
            amount=request.json['amount'],
            currency='usd',
            source=request.json['token']
        )
        return jsonify({'success': True, 'transaction_id': result.id})
    except stripe.error.CardError as e:
        log_payment_failure(request.user.id, str(e))
        return jsonify({'error': 'Payment failed'}), 400
```

### A05:2021 – Security Misconfiguration
**Risk Level**: High | **Prevalence**: Very High

#### Common Misconfigurations
```yaml
# Vulnerable Docker configuration
version: '3'
services:
  web:
    image: myapp:latest
    ports:
      - "80:80"
    environment:
      - DEBUG=true  # Never enable debug in production
      - SECRET_KEY=default123  # Hardcoded secrets

# Secure Docker configuration
version: '3'
services:
  web:
    image: myapp:latest
    ports:
      - "80:80"
    environment:
      - DEBUG=false
      - SECRET_KEY_FILE=/run/secrets/secret_key
    secrets:
      - secret_key
    user: "1000:1000"  # Non-root user
    read_only: true
    tmpfs:
      - /tmp

secrets:
  secret_key:
    external: true
```

#### Security Headers
```python
from flask import Flask
from flask_talisman import Talisman

app = Flask(__name__)

# Implement security headers
Talisman(app, {
    'force_https': True,
    'strict_transport_security': True,
    'strict_transport_security_max_age': 31536000,
    'content_security_policy': {
        'default-src': "'self'",
        'script-src': "'self' 'unsafe-inline'",
        'style-src': "'self' 'unsafe-inline'",
        'img-src': "'self' data: https:",
    }
})
```

### A06:2021 – Vulnerable and Outdated Components
**Risk Level**: High | **Prevalence**: Very High

#### Dependency Management
```json
// package.json with security considerations
{
  "name": "healthcare-platform",
  "scripts": {
    "audit": "npm audit",
    "audit-fix": "npm audit fix",
    "security-check": "npm audit --audit-level moderate"
  },
  "dependencies": {
    "express": "^4.18.2",
    "helmet": "^6.0.1",
    "bcrypt": "^5.1.0"
  },
  "devDependencies": {
    "snyk": "^1.1000.0"
  }
}
```

#### Automated Security Scanning
```yaml
# GitHub Actions security workflow
name: Security Scan
on: [push, pull_request]

jobs:
  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run Snyk to check for vulnerabilities
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      - name: Run npm audit
        run: npm audit --audit-level moderate
```

### A07:2021 – Identification and Authentication Failures
**Risk Level**: High | **Prevalence**: Medium

#### Secure Authentication Implementation
```python
from flask_jwt_extended import JWTManager, create_access_token, jwt_required
import bcrypt
import re

class AuthenticationService:
    def __init__(self):
        self.password_pattern = re.compile(
            r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{12,}$'
        )
    
    def validate_password_strength(self, password):
        """Enforce strong password policy"""
        if not self.password_pattern.match(password):
            raise ValueError(
                "Password must be at least 12 characters with uppercase, "
                "lowercase, number, and special character"
            )
    
    def hash_password(self, password):
        self.validate_password_strength(password)
        salt = bcrypt.gensalt(rounds=12)
        return bcrypt.hashpw(password.encode('utf-8'), salt)
    
    def verify_password(self, password, hashed):
        return bcrypt.checkpw(password.encode('utf-8'), hashed)
    
    def create_session(self, user_id, additional_claims=None):
        """Create JWT with proper expiration"""
        claims = additional_claims or {}
        claims['user_id'] = user_id
        claims['iat'] = datetime.utcnow()
        
        return create_access_token(
            identity=user_id,
            additional_claims=claims,
            expires_delta=timedelta(hours=1)  # Short-lived tokens
        )
```

### A08:2021 – Software and Data Integrity Failures
**Risk Level**: Medium | **Prevalence**: Medium

#### Secure CI/CD Pipeline
```yaml
# Secure deployment pipeline
name: Secure Deploy
on:
  push:
    branches: [main]

jobs:
  security-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Verify commit signatures
        run: git verify-commit HEAD
      
      - name: SAST Scan
        uses: github/super-linter@v4
        env:
          DEFAULT_BRANCH: main
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Container Image Scan
        run: |
          docker build -t myapp:${{ github.sha }} .
          trivy image myapp:${{ github.sha }}
      
      - name: Sign container image
        run: |
          cosign sign --key cosign.key myapp:${{ github.sha }}
```

### A09:2021 – Security Logging and Monitoring Failures
**Risk Level**: Medium | **Prevalence**: High

#### Comprehensive Logging
```python
import logging
import json
from datetime import datetime

class SecurityLogger:
    def __init__(self):
        self.logger = logging.getLogger('security')
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def log_authentication_attempt(self, username, success, ip_address):
        event = {
            'event_type': 'authentication_attempt',
            'username': username,
            'success': success,
            'ip_address': ip_address,
            'timestamp': datetime.utcnow().isoformat(),
            'user_agent': request.headers.get('User-Agent')
        }
        
        if success:
            self.logger.info(f"Authentication successful: {json.dumps(event)}")
        else:
            self.logger.warning(f"Authentication failed: {json.dumps(event)}")
    
    def log_data_access(self, user_id, resource_type, resource_id, action):
        event = {
            'event_type': 'data_access',
            'user_id': user_id,
            'resource_type': resource_type,
            'resource_id': resource_id,
            'action': action,
            'timestamp': datetime.utcnow().isoformat()
        }
        self.logger.info(f"Data access: {json.dumps(event)}")
```

### A10:2021 – Server-Side Request Forgery (SSRF)
**Risk Level**: Medium | **Prevalence**: Low

#### SSRF Prevention
```python
import ipaddress
import urllib.parse

class SSRFProtection:
    BLOCKED_NETWORKS = [
        ipaddress.ip_network('*********/8'),    # Localhost
        ipaddress.ip_network('10.0.0.0/8'),     # Private
        ipaddress.ip_network('**********/12'),  # Private
        ipaddress.ip_network('***********/16'), # Private
        ipaddress.ip_network('***********/16'), # Link-local
    ]
    
    @staticmethod
    def is_safe_url(url):
        try:
            parsed = urllib.parse.urlparse(url)
            
            # Only allow HTTP/HTTPS
            if parsed.scheme not in ['http', 'https']:
                return False
            
            # Resolve hostname to IP
            ip = ipaddress.ip_address(socket.gethostbyname(parsed.hostname))
            
            # Check against blocked networks
            for network in SSRFProtection.BLOCKED_NETWORKS:
                if ip in network:
                    return False
            
            return True
        except:
            return False

# Usage in webhook processing
@app.route('/api/webhook', methods=['POST'])
def process_webhook():
    callback_url = request.json.get('callback_url')
    
    if not SSRFProtection.is_safe_url(callback_url):
        return jsonify({'error': 'Invalid callback URL'}), 400
    
    # Safe to make request
    response = requests.post(callback_url, json=webhook_data, timeout=5)
```

## 🎯 Healthcare-Specific Vulnerabilities

### Medical Device Security
- **Unencrypted communications** between devices
- **Default credentials** on medical equipment
- **Lack of authentication** for device access
- **Insufficient logging** of device activities

### EHR System Vulnerabilities
- **Privilege escalation** in role-based systems
- **Data leakage** through improper access controls
- **Audit trail manipulation**
- **Integration security** with third-party systems

## 📊 Vulnerability Assessment Checklist

- [ ] Regular penetration testing
- [ ] Automated vulnerability scanning
- [ ] Code review for security issues
- [ ] Dependency vulnerability monitoring
- [ ] Configuration security assessment
- [ ] Access control testing
- [ ] Data encryption verification
- [ ] Logging and monitoring validation

## 📚 Next Steps

1. **Study**: Secure Coding Practices (03-secure-coding-practices.md)
2. **Practice**: Set up OWASP ZAP for vulnerability testing
3. **Implement**: Security testing in your CI/CD pipeline
4. **Monitor**: Set up vulnerability scanning for dependencies

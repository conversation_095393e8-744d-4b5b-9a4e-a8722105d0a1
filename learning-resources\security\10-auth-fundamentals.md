# Authentication Fundamentals

## Overview

Authentication is the cornerstone of security for healthcare platforms, ensuring that only authorized users can access sensitive patient data and payment systems. This guide covers fundamental authentication concepts, implementation strategies, and best practices for healthcare compliance.

## 🎯 Learning Objectives

By the end of this module, you will understand:
- Authentication vs. authorization concepts
- Multi-factor authentication (MFA) implementation
- Password security and management
- Session management best practices
- Biometric authentication for healthcare
- Authentication protocols and standards

## 🔐 Authentication Fundamentals

### 1. Authentication vs. Authorization

```javascript
// Clear distinction between authentication and authorization
class SecurityService {
  // Authentication: "Who are you?"
  async authenticate(credentials) {
    const { email, password, mfaToken } = credentials;
    
    // Step 1: Verify user identity
    const user = await this.verifyCredentials(email, password);
    if (!user) {
      throw new Error('Invalid credentials');
    }
    
    // Step 2: Verify MFA token
    const mfaValid = await this.verifyMFA(user.id, mfaToken);
    if (!mfaValid) {
      throw new Error('Invalid MFA token');
    }
    
    // Step 3: Generate authentication token
    const token = await this.generateJWT(user);
    
    return {
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role
      },
      token
    };
  }
  
  // Authorization: "What can you do?"
  async authorize(token, resource, action) {
    // Step 1: Verify token authenticity
    const payload = await this.verifyJWT(token);
    
    // Step 2: Check permissions
    const hasPermission = await this.checkPermission(
      payload.userId,
      payload.role,
      resource,
      action
    );
    
    return {
      authorized: hasPermission,
      userId: payload.userId,
      role: payload.role
    };
  }
  
  async verifyCredentials(email, password) {
    const user = await User.findOne({ email });
    if (!user) return null;
    
    const isValid = await bcrypt.compare(password, user.passwordHash);
    return isValid ? user : null;
  }
  
  async checkPermission(userId, role, resource, action) {
    // Role-based access control
    const permissions = await this.getRolePermissions(role);
    const resourcePermissions = permissions[resource] || [];
    
    // Check if action is allowed
    if (resourcePermissions.includes(action)) {
      return true;
    }
    
    // Check user-specific permissions
    const userPermissions = await this.getUserPermissions(userId);
    return userPermissions.some(p => 
      p.resource === resource && p.action === action
    );
  }
}
```

### 2. Password Security Implementation

```javascript
// Comprehensive password security for healthcare platform
class PasswordSecurity {
  constructor() {
    this.minLength = 12;
    this.maxLength = 128;
    this.saltRounds = 12;
    this.passwordHistory = 12; // Remember last 12 passwords
    this.maxAge = 90; // Days
  }
  
  // Password strength validation
  validatePassword(password, userInfo = {}) {
    const errors = [];
    
    // Length requirements
    if (password.length < this.minLength) {
      errors.push(`Password must be at least ${this.minLength} characters`);
    }
    
    if (password.length > this.maxLength) {
      errors.push(`Password must not exceed ${this.maxLength} characters`);
    }
    
    // Complexity requirements
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
    
    if (!hasUppercase) errors.push('Password must contain uppercase letters');
    if (!hasLowercase) errors.push('Password must contain lowercase letters');
    if (!hasNumbers) errors.push('Password must contain numbers');
    if (!hasSpecialChars) errors.push('Password must contain special characters');
    
    // Check against common passwords
    if (this.isCommonPassword(password)) {
      errors.push('Password is too common');
    }
    
    // Check against user information
    if (this.containsUserInfo(password, userInfo)) {
      errors.push('Password must not contain personal information');
    }
    
    // Calculate entropy
    const entropy = this.calculateEntropy(password);
    if (entropy < 50) {
      errors.push('Password is not complex enough');
    }
    
    return {
      valid: errors.length === 0,
      errors,
      strength: this.calculateStrength(password),
      entropy
    };
  }
  
  // Secure password hashing
  async hashPassword(password) {
    try {
      const salt = await bcrypt.genSalt(this.saltRounds);
      const hash = await bcrypt.hash(password, salt);
      
      return {
        hash,
        algorithm: 'bcrypt',
        rounds: this.saltRounds,
        createdAt: new Date()
      };
    } catch (error) {
      throw new Error('Password hashing failed');
    }
  }
  
  // Password verification with timing attack protection
  async verifyPassword(password, storedHash) {
    try {
      const startTime = process.hrtime.bigint();
      const isValid = await bcrypt.compare(password, storedHash);
      const endTime = process.hrtime.bigint();
      
      // Constant-time comparison to prevent timing attacks
      const minTime = 100000000n; // 100ms in nanoseconds
      const elapsed = endTime - startTime;
      
      if (elapsed < minTime) {
        await new Promise(resolve => 
          setTimeout(resolve, Number((minTime - elapsed) / 1000000n))
        );
      }
      
      return isValid;
    } catch (error) {
      // Always take the same amount of time even on error
      await new Promise(resolve => setTimeout(resolve, 100));
      return false;
    }
  }
  
  // Check password history
  async checkPasswordHistory(userId, newPassword) {
    const passwordHistory = await PasswordHistory.find({
      userId,
      createdAt: { $gte: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000) }
    }).sort({ createdAt: -1 }).limit(this.passwordHistory);
    
    for (const oldPassword of passwordHistory) {
      const isReused = await this.verifyPassword(newPassword, oldPassword.hash);
      if (isReused) {
        return false;
      }
    }
    
    return true;
  }
  
  calculateEntropy(password) {
    const charSets = [
      { regex: /[a-z]/, size: 26 },
      { regex: /[A-Z]/, size: 26 },
      { regex: /[0-9]/, size: 10 },
      { regex: /[^a-zA-Z0-9]/, size: 32 }
    ];
    
    let charSetSize = 0;
    charSets.forEach(set => {
      if (set.regex.test(password)) {
        charSetSize += set.size;
      }
    });
    
    return password.length * Math.log2(charSetSize);
  }
  
  calculateStrength(password) {
    const entropy = this.calculateEntropy(password);
    
    if (entropy < 30) return 'Very Weak';
    if (entropy < 40) return 'Weak';
    if (entropy < 50) return 'Fair';
    if (entropy < 60) return 'Good';
    if (entropy < 70) return 'Strong';
    return 'Very Strong';
  }
  
  isCommonPassword(password) {
    const commonPasswords = [
      'password', '123456', 'password123', 'admin', 'qwerty',
      'letmein', 'welcome', 'monkey', '**********', 'healthcare'
    ];
    
    return commonPasswords.includes(password.toLowerCase());
  }
  
  containsUserInfo(password, userInfo) {
    const { firstName, lastName, email, dateOfBirth } = userInfo;
    const lowerPassword = password.toLowerCase();
    
    if (firstName && lowerPassword.includes(firstName.toLowerCase())) return true;
    if (lastName && lowerPassword.includes(lastName.toLowerCase())) return true;
    if (email && lowerPassword.includes(email.split('@')[0].toLowerCase())) return true;
    if (dateOfBirth) {
      const year = new Date(dateOfBirth).getFullYear().toString();
      if (lowerPassword.includes(year)) return true;
    }
    
    return false;
  }
}
```

## 🔒 Multi-Factor Authentication (MFA)

### 1. TOTP (Time-based One-Time Password) Implementation

```javascript
// TOTP implementation for healthcare platform
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');

class TOTPService {
  constructor() {
    this.serviceName = 'Healthcare Platform';
    this.issuer = 'HealthcarePlatform';
    this.window = 2; // Allow 2 time steps tolerance
  }
  
  // Generate TOTP secret for user
  async generateSecret(user) {
    const secret = speakeasy.generateSecret({
      name: `${this.serviceName} (${user.email})`,
      issuer: this.issuer,
      length: 32
    });
    
    // Store secret in database (encrypted)
    await this.storeSecret(user.id, secret.base32);
    
    // Generate QR code for easy setup
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);
    
    return {
      secret: secret.base32,
      qrCode: qrCodeUrl,
      manualEntryKey: secret.base32,
      backupCodes: await this.generateBackupCodes(user.id)
    };
  }
  
  // Verify TOTP token
  async verifyToken(userId, token) {
    try {
      const secret = await this.getSecret(userId);
      if (!secret) {
        throw new Error('TOTP not configured for user');
      }
      
      const verified = speakeasy.totp.verify({
        secret: secret,
        encoding: 'base32',
        token: token,
        window: this.window
      });
      
      if (verified) {
        // Log successful MFA verification
        await this.logMFAEvent(userId, 'TOTP_SUCCESS', {
          timestamp: new Date(),
          method: 'TOTP'
        });
        
        return true;
      }
      
      // Check backup codes if TOTP fails
      return await this.verifyBackupCode(userId, token);
      
    } catch (error) {
      await this.logMFAEvent(userId, 'TOTP_FAILURE', {
        timestamp: new Date(),
        error: error.message
      });
      
      return false;
    }
  }
  
  // Generate backup codes
  async generateBackupCodes(userId, count = 10) {
    const codes = [];
    
    for (let i = 0; i < count; i++) {
      const code = this.generateRandomCode(8);
      codes.push(code);
      
      // Store hashed backup code
      const hashedCode = await bcrypt.hash(code, 10);
      await BackupCode.create({
        userId,
        code: hashedCode,
        used: false,
        createdAt: new Date()
      });
    }
    
    return codes;
  }
  
  // Verify backup code
  async verifyBackupCode(userId, code) {
    const backupCodes = await BackupCode.find({
      userId,
      used: false
    });
    
    for (const backupCode of backupCodes) {
      const isValid = await bcrypt.compare(code, backupCode.code);
      if (isValid) {
        // Mark backup code as used
        await BackupCode.updateOne(
          { _id: backupCode._id },
          { used: true, usedAt: new Date() }
        );
        
        await this.logMFAEvent(userId, 'BACKUP_CODE_SUCCESS', {
          timestamp: new Date(),
          method: 'BACKUP_CODE'
        });
        
        return true;
      }
    }
    
    return false;
  }
  
  generateRandomCode(length) {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }
  
  async storeSecret(userId, secret) {
    // Encrypt secret before storing
    const encrypted = await this.encrypt(secret);
    
    await UserMFA.updateOne(
      { userId },
      {
        userId,
        totpSecret: encrypted,
        enabled: true,
        createdAt: new Date()
      },
      { upsert: true }
    );
  }
  
  async getSecret(userId) {
    const mfa = await UserMFA.findOne({ userId, enabled: true });
    if (!mfa) return null;
    
    // Decrypt secret
    return await this.decrypt(mfa.totpSecret);
  }
  
  async encrypt(text) {
    // Implementation depends on your encryption library
    // Use AES-256-GCM or similar
    const cipher = crypto.createCipher('aes-256-gcm', process.env.MFA_ENCRYPTION_KEY);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }
  
  async decrypt(encryptedText) {
    const decipher = crypto.createDecipher('aes-256-gcm', process.env.MFA_ENCRYPTION_KEY);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
  
  async logMFAEvent(userId, event, details) {
    await MFALog.create({
      userId,
      event,
      details,
      timestamp: new Date(),
      ipAddress: details.ipAddress,
      userAgent: details.userAgent
    });
  }
}
```

### 2. SMS and Email MFA Implementation

```javascript
// SMS and Email MFA for healthcare platform
class AlternativeMFAService {
  constructor() {
    this.smsService = new SMSService();
    this.emailService = new EmailService();
    this.codeLength = 6;
    this.codeExpiry = 5 * 60 * 1000; // 5 minutes
    this.maxAttempts = 3;
  }
  
  // Send SMS verification code
  async sendSMSCode(userId, phoneNumber) {
    try {
      const code = this.generateNumericCode(this.codeLength);
      const expiresAt = new Date(Date.now() + this.codeExpiry);
      
      // Store code in database
      await VerificationCode.create({
        userId,
        code: await bcrypt.hash(code, 10),
        type: 'SMS',
        expiresAt,
        attempts: 0,
        phoneNumber: this.maskPhoneNumber(phoneNumber)
      });
      
      // Send SMS
      await this.smsService.send(phoneNumber, 
        `Your Healthcare Platform verification code is: ${code}. Valid for 5 minutes.`
      );
      
      return {
        success: true,
        maskedPhone: this.maskPhoneNumber(phoneNumber),
        expiresAt
      };
      
    } catch (error) {
      throw new Error('Failed to send SMS verification code');
    }
  }
  
  // Send email verification code
  async sendEmailCode(userId, email) {
    try {
      const code = this.generateNumericCode(this.codeLength);
      const expiresAt = new Date(Date.now() + this.codeExpiry);
      
      // Store code in database
      await VerificationCode.create({
        userId,
        code: await bcrypt.hash(code, 10),
        type: 'EMAIL',
        expiresAt,
        attempts: 0,
        email: this.maskEmail(email)
      });
      
      // Send email
      await this.emailService.send({
        to: email,
        subject: 'Healthcare Platform - Verification Code',
        template: 'mfa-verification',
        data: {
          code,
          expiresIn: '5 minutes',
          userAgent: 'Browser/Device info',
          timestamp: new Date().toLocaleString()
        }
      });
      
      return {
        success: true,
        maskedEmail: this.maskEmail(email),
        expiresAt
      };
      
    } catch (error) {
      throw new Error('Failed to send email verification code');
    }
  }
  
  // Verify SMS/Email code
  async verifyCode(userId, code, type) {
    try {
      const verificationRecord = await VerificationCode.findOne({
        userId,
        type,
        expiresAt: { $gt: new Date() }
      }).sort({ createdAt: -1 });
      
      if (!verificationRecord) {
        throw new Error('No valid verification code found');
      }
      
      // Check attempt limit
      if (verificationRecord.attempts >= this.maxAttempts) {
        throw new Error('Maximum verification attempts exceeded');
      }
      
      // Verify code
      const isValid = await bcrypt.compare(code, verificationRecord.code);
      
      // Increment attempts
      await VerificationCode.updateOne(
        { _id: verificationRecord._id },
        { $inc: { attempts: 1 } }
      );
      
      if (isValid) {
        // Mark as used
        await VerificationCode.updateOne(
          { _id: verificationRecord._id },
          { used: true, usedAt: new Date() }
        );
        
        await this.logMFAEvent(userId, `${type}_CODE_SUCCESS`, {
          timestamp: new Date(),
          method: type
        });
        
        return true;
      }
      
      await this.logMFAEvent(userId, `${type}_CODE_FAILURE`, {
        timestamp: new Date(),
        method: type,
        attemptsRemaining: this.maxAttempts - verificationRecord.attempts - 1
      });
      
      return false;
      
    } catch (error) {
      await this.logMFAEvent(userId, `${type}_CODE_ERROR`, {
        timestamp: new Date(),
        error: error.message
      });
      
      throw error;
    }
  }
  
  generateNumericCode(length) {
    let code = '';
    for (let i = 0; i < length; i++) {
      code += Math.floor(Math.random() * 10);
    }
    return code;
  }
  
  maskPhoneNumber(phone) {
    if (phone.length <= 4) return phone;
    return phone.slice(0, -4).replace(/\d/g, '*') + phone.slice(-4);
  }
  
  maskEmail(email) {
    const [username, domain] = email.split('@');
    if (username.length <= 2) return email;
    
    const maskedUsername = username[0] + '*'.repeat(username.length - 2) + username.slice(-1);
    return `${maskedUsername}@${domain}`;
  }
}
```

## 🔑 Session Management

### 1. Secure Session Implementation

```javascript
// Secure session management for healthcare platform
class SessionManager {
  constructor() {
    this.sessionTimeout = 30 * 60 * 1000; // 30 minutes
    this.absoluteTimeout = 8 * 60 * 60 * 1000; // 8 hours
    this.renewalThreshold = 5 * 60 * 1000; // 5 minutes
  }
  
  // Create new session
  async createSession(userId, deviceInfo, ipAddress) {
    const sessionId = this.generateSecureSessionId();
    const now = new Date();
    
    const session = {
      sessionId,
      userId,
      createdAt: now,
      lastActivity: now,
      expiresAt: new Date(now.getTime() + this.sessionTimeout),
      absoluteExpiresAt: new Date(now.getTime() + this.absoluteTimeout),
      ipAddress,
      userAgent: deviceInfo.userAgent,
      deviceFingerprint: await this.generateDeviceFingerprint(deviceInfo),
      isActive: true,
      renewalCount: 0
    };
    
    await Session.create(session);
    
    // Log session creation
    await this.logSessionEvent(userId, 'SESSION_CREATED', {
      sessionId,
      ipAddress,
      userAgent: deviceInfo.userAgent
    });
    
    return sessionId;
  }
  
  // Validate and refresh session
  async validateSession(sessionId, ipAddress, deviceInfo) {
    const session = await Session.findOne({
      sessionId,
      isActive: true,
      expiresAt: { $gt: new Date() },
      absoluteExpiresAt: { $gt: new Date() }
    });
    
    if (!session) {
      throw new Error('Invalid or expired session');
    }
    
    // Verify IP address (optional - can be disabled for mobile users)
    if (session.ipAddress !== ipAddress) {
      await this.logSessionEvent(session.userId, 'SESSION_IP_MISMATCH', {
        sessionId,
        originalIP: session.ipAddress,
        currentIP: ipAddress
      });
      
      // Optionally terminate session or require re-authentication
      // throw new Error('Session IP address mismatch');
    }
    
    // Verify device fingerprint
    const currentFingerprint = await this.generateDeviceFingerprint(deviceInfo);
    if (session.deviceFingerprint !== currentFingerprint) {
      await this.logSessionEvent(session.userId, 'SESSION_DEVICE_MISMATCH', {
        sessionId,
        originalFingerprint: session.deviceFingerprint,
        currentFingerprint
      });
    }
    
    // Check if session needs renewal
    const timeUntilExpiry = session.expiresAt.getTime() - Date.now();
    if (timeUntilExpiry < this.renewalThreshold) {
      await this.renewSession(sessionId);
    }
    
    // Update last activity
    await Session.updateOne(
      { sessionId },
      { 
        lastActivity: new Date(),
        ipAddress, // Update IP if changed
        userAgent: deviceInfo.userAgent
      }
    );
    
    return session;
  }
  
  // Renew session
  async renewSession(sessionId) {
    const session = await Session.findOne({ sessionId, isActive: true });
    if (!session) {
      throw new Error('Session not found');
    }
    
    const now = new Date();
    const newExpiresAt = new Date(now.getTime() + this.sessionTimeout);
    
    // Don't extend beyond absolute timeout
    if (newExpiresAt > session.absoluteExpiresAt) {
      throw new Error('Session has reached absolute timeout');
    }
    
    await Session.updateOne(
      { sessionId },
      {
        expiresAt: newExpiresAt,
        renewalCount: session.renewalCount + 1,
        lastRenewal: now
      }
    );
    
    await this.logSessionEvent(session.userId, 'SESSION_RENEWED', {
      sessionId,
      renewalCount: session.renewalCount + 1
    });
  }
  
  // Terminate session
  async terminateSession(sessionId, reason = 'USER_LOGOUT') {
    const session = await Session.findOne({ sessionId });
    if (!session) {
      return false;
    }
    
    await Session.updateOne(
      { sessionId },
      {
        isActive: false,
        terminatedAt: new Date(),
        terminationReason: reason
      }
    );
    
    await this.logSessionEvent(session.userId, 'SESSION_TERMINATED', {
      sessionId,
      reason
    });
    
    return true;
  }
  
  // Terminate all user sessions
  async terminateAllUserSessions(userId, excludeSessionId = null) {
    const filter = { userId, isActive: true };
    if (excludeSessionId) {
      filter.sessionId = { $ne: excludeSessionId };
    }
    
    const sessions = await Session.find(filter);
    
    await Session.updateMany(
      filter,
      {
        isActive: false,
        terminatedAt: new Date(),
        terminationReason: 'ALL_SESSIONS_TERMINATED'
      }
    );
    
    await this.logSessionEvent(userId, 'ALL_SESSIONS_TERMINATED', {
      terminatedCount: sessions.length,
      excludedSession: excludeSessionId
    });
    
    return sessions.length;
  }
  
  generateSecureSessionId() {
    return crypto.randomBytes(32).toString('hex');
  }
  
  async generateDeviceFingerprint(deviceInfo) {
    const fingerprint = {
      userAgent: deviceInfo.userAgent,
      screenResolution: deviceInfo.screenResolution,
      timezone: deviceInfo.timezone,
      language: deviceInfo.language,
      platform: deviceInfo.platform
    };
    
    return crypto
      .createHash('sha256')
      .update(JSON.stringify(fingerprint))
      .digest('hex');
  }
  
  async logSessionEvent(userId, event, details) {
    await SessionLog.create({
      userId,
      event,
      details,
      timestamp: new Date()
    });
  }
  
  // Cleanup expired sessions
  async cleanupExpiredSessions() {
    const result = await Session.deleteMany({
      $or: [
        { expiresAt: { $lt: new Date() } },
        { absoluteExpiresAt: { $lt: new Date() } }
      ]
    });
    
    console.log(`Cleaned up ${result.deletedCount} expired sessions`);
    return result.deletedCount;
  }
}
```

## 📚 Best Practices Summary

### Password Security
1. **Strong Requirements**: Enforce complex password requirements
2. **Secure Hashing**: Use bcrypt or Argon2 for password hashing
3. **Password History**: Prevent password reuse
4. **Regular Updates**: Enforce periodic password changes
5. **Breach Detection**: Monitor for compromised passwords

### Multi-Factor Authentication
1. **Mandatory MFA**: Require MFA for all healthcare users
2. **Multiple Options**: Provide TOTP, SMS, and email options
3. **Backup Codes**: Generate backup codes for recovery
4. **Device Trust**: Implement device trust mechanisms
5. **Risk-Based**: Use risk-based authentication

### Session Management
1. **Secure Sessions**: Use cryptographically secure session IDs
2. **Timeout Policies**: Implement appropriate timeout policies
3. **Device Tracking**: Track and validate device fingerprints
4. **Activity Monitoring**: Monitor session activity for anomalies
5. **Secure Termination**: Properly terminate sessions on logout

## 🔗 Additional Resources

- [OWASP Authentication Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Authentication_Cheat_Sheet.html)
- [NIST Digital Identity Guidelines](https://pages.nist.gov/800-63-3/)
- [FIDO Alliance Standards](https://fidoalliance.org/specifications/)
- [Healthcare Authentication Best Practices](../23-healthcare-security.md)

---

**Next**: [Modern Authentication Patterns](11-modern-auth-patterns.md) | **Previous**: [Network Security](09-network-security.md)

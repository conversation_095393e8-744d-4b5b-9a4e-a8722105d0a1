# Secure CI/CD

## Overview

Secure CI/CD is critical for healthcare platforms to ensure that security is integrated throughout the software development lifecycle. This guide covers comprehensive strategies for implementing security in Continuous Integration and Continuous Deployment pipelines, including secrets management, secure build processes, and deployment security for healthcare applications.

## 🎯 Learning Objectives

By the end of this module, you will understand:

- Secure CI/CD pipeline design and implementation
- Secrets management in CI/CD environments
- Container security in build and deployment processes
- Infrastructure as Code (IaC) security
- Compliance automation in CI/CD
- Security gates and approval processes

## 🔧 Secure CI/CD Pipeline Architecture

### 1. Healthcare CI/CD Security Framework

```yaml
# Comprehensive secure CI/CD pipeline for healthcare platform
name: Healthcare Platform Secure CI/CD

on:
  push:
    branches: [main, develop, release/*]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: healthcare-platform
  SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
  SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

jobs:
  # Security Gate 1: Code Quality and Security Analysis
  security-analysis:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: "npm"

      - name: Install dependencies
        run: npm ci --audit-level=high

      - name: Run security linting
        run: |
          npm install eslint-plugin-security --save-dev
          npx eslint . --ext .js,.ts --format sarif --output-file eslint-security.sarif

      - name: Upload ESLint results to GitHub Security
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: eslint-security.sarif

      - name: Run Semgrep SAST
        uses: returntocorp/semgrep-action@v1
        with:
          config: >-
            p/security-audit
            p/owasp-top-ten
            p/javascript
            r/javascript.express.security.audit
        env:
          SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}

      - name: Run Snyk Code Analysis
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

      - name: SonarQube Security Scan
        uses: sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          scanMetadataReportFile: target/sonar/report-task.txt

  # Security Gate 2: Dependency and License Scanning
  dependency-security:
    runs-on: ubuntu-latest
    needs: security-analysis

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: "npm"

      - name: Audit dependencies
        run: |
          npm audit --audit-level=high --production
          npm audit --json > npm-audit.json

      - name: License compliance check
        run: |
          npx license-checker --onlyAllow 'MIT;Apache-2.0;BSD-2-Clause;BSD-3-Clause;ISC' --production

      - name: Snyk dependency scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high --file=package.json

      - name: FOSSA license scan
        uses: fossas/fossa-action@main
        with:
          api-key: ${{ secrets.FOSSA_API_KEY }}

  # Security Gate 3: Secure Build and Container Scanning
  secure-build:
    runs-on: ubuntu-latest
    needs: [security-analysis, dependency-security]
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-

      - name: Build Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: false
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          load: true

      - name: Run Trivy container scan
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ steps.meta.outputs.tags }}
          format: "sarif"
          output: "trivy-results.sarif"
          severity: "CRITICAL,HIGH"
          exit-code: "1"

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: "trivy-results.sarif"

      - name: Run Snyk container scan
        uses: snyk/actions/docker@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          image: ${{ steps.meta.outputs.tags }}
          args: --severity-threshold=high

      - name: Sign container image
        if: github.event_name != 'pull_request'
        run: |
          echo "${{ secrets.COSIGN_PRIVATE_KEY }}" > cosign.key
          cosign sign --key cosign.key ${{ steps.meta.outputs.tags }}
        env:
          COSIGN_PASSWORD: ${{ secrets.COSIGN_PASSWORD }}

      - name: Push container image
        if: github.event_name != 'pull_request'
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  # Security Gate 4: Infrastructure as Code Security
  infrastructure-security:
    runs-on: ubuntu-latest
    needs: secure-build

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.5.0

      - name: Terraform format check
        run: terraform fmt -check -recursive

      - name: Run Checkov IaC scan
        uses: bridgecrewio/checkov-action@master
        with:
          directory: ./infrastructure
          framework: terraform
          output_format: sarif
          output_file_path: checkov-results.sarif
          check: CKV_AWS_*,CKV_AZURE_*
          skip_check: CKV_AWS_79 # Skip if S3 bucket logging not required

      - name: Upload Checkov results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: checkov-results.sarif

      - name: Run Terrascan
        uses: accurics/terrascan-action@main
        with:
          iac_type: "terraform"
          iac_version: "v14"
          policy_type: "aws"
          only_warn: true

      - name: Run TFSec
        uses: aquasecurity/tfsec-action@v1.0.0
        with:
          soft_fail: false

  # Security Gate 5: Deployment Security and Compliance
  secure-deployment:
    runs-on: ubuntu-latest
    needs: infrastructure-security
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Verify container signature
        run: |
          cosign verify --key cosign.pub ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:main

      - name: Deploy to staging
        run: |
          kubectl apply -f k8s/staging/ --dry-run=client
          kubectl apply -f k8s/staging/

      - name: Run DAST scan on staging
        uses: zaproxy/action-full-scan@v0.4.0
        with:
          target: "https://staging.healthcare-platform.com"
          rules_file_name: ".zap/rules.tsv"
          cmd_options: "-a"

      - name: Security approval gate
        uses: trstringer/manual-approval@v1
        with:
          secret: ${{ github.TOKEN }}
          approvers: security-team
          minimum-approvals: 2
          issue-title: "Production Deployment Security Approval"

      - name: Deploy to production
        run: |
          kubectl apply -f k8s/production/
```

### 2. Secrets Management in CI/CD

```javascript
// Secure secrets management for healthcare CI/CD
class SecureSecretsManager {
  constructor() {
    this.vaultClient = new VaultClient();
    this.awsSecretsManager = new AWS.SecretsManager();
    this.encryptionKey = process.env.SECRETS_ENCRYPTION_KEY;
  }

  // Retrieve secrets securely in CI/CD
  async getSecret(secretPath, environment) {
    try {
      // Use different secret stores based on environment
      switch (environment) {
        case "development":
          return await this.getFromVault(secretPath);
        case "staging":
          return await this.getFromAWSSecretsManager(secretPath);
        case "production":
          return await this.getFromAWSSecretsManager(secretPath);
        default:
          throw new Error("Invalid environment");
      }
    } catch (error) {
      console.error("Failed to retrieve secret:", error);
      throw new Error("Secret retrieval failed");
    }
  }

  async getFromVault(secretPath) {
    const secret = await this.vaultClient.read(secretPath);
    return this.decryptSecret(secret.data);
  }

  async getFromAWSSecretsManager(secretName) {
    const params = {
      SecretId: secretName,
      VersionStage: "AWSCURRENT",
    };

    const result = await this.awsSecretsManager
      .getSecretValue(params)
      .promise();
    return JSON.parse(result.SecretString);
  }

  // Encrypt secrets for storage
  async encryptSecret(plaintext) {
    const cipher = crypto.createCipher("aes-256-gcm", this.encryptionKey);
    let encrypted = cipher.update(plaintext, "utf8", "hex");
    encrypted += cipher.final("hex");

    const authTag = cipher.getAuthTag();

    return {
      encrypted,
      authTag: authTag.toString("hex"),
    };
  }

  // Decrypt secrets
  async decryptSecret(encryptedData) {
    const decipher = crypto.createDecipher("aes-256-gcm", this.encryptionKey);
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, "hex"));

    let decrypted = decipher.update(encryptedData.encrypted, "hex", "utf8");
    decrypted += decipher.final("utf8");

    return decrypted;
  }

  // Rotate secrets automatically
  async rotateSecrets(secretPaths) {
    const rotationResults = [];

    for (const secretPath of secretPaths) {
      try {
        const newSecret = this.generateSecureSecret();
        await this.updateSecret(secretPath, newSecret);

        rotationResults.push({
          path: secretPath,
          status: "success",
          rotatedAt: new Date(),
        });
      } catch (error) {
        rotationResults.push({
          path: secretPath,
          status: "failed",
          error: error.message,
        });
      }
    }

    return rotationResults;
  }

  generateSecureSecret(length = 32) {
    return crypto.randomBytes(length).toString("hex");
  }
}
```

### 3. Container Security in CI/CD

```dockerfile
# Multi-stage secure Dockerfile for healthcare platform
# Security-hardened base image
FROM node:18.17.0-alpine3.18 AS base

# Install security updates
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init && \
    rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S healthcare && \
    adduser -S healthcare -u 1001 -G healthcare

# Build stage
FROM base AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies with security audit
RUN npm ci --only=production --audit-level=high && \
    npm cache clean --force

# Copy source code
COPY . .

# Build application
RUN npm run build

# Security scanning stage
FROM builder AS security-scan

# Install security scanning tools
RUN npm install -g retire audit-ci

# Run security scans
RUN retire --path . --outputformat json --outputpath retire-report.json || true
RUN audit-ci --config audit-ci.json

# Production stage
FROM base AS production

WORKDIR /app

# Copy built application
COPY --from=builder --chown=healthcare:healthcare /app/dist ./dist
COPY --from=builder --chown=healthcare:healthcare /app/node_modules ./node_modules
COPY --from=builder --chown=healthcare:healthcare /app/package.json ./

# Security configurations
USER healthcare
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/server.js"]

# Security labels
LABEL security.scan="enabled" \
      compliance="HIPAA,PCI-DSS" \
      maintainer="<EMAIL>" \
      version="1.0.0"
```

## 🔐 Infrastructure as Code Security

### 1. Terraform Security Configuration

```hcl
# Secure Terraform configuration for healthcare infrastructure
terraform {
  required_version = ">= 1.5.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  # Secure backend configuration
  backend "s3" {
    bucket         = "healthcare-terraform-state"
    key            = "infrastructure/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    kms_key_id     = "arn:aws:kms:us-east-1:123456789012:key/12345678-1234-1234-1234-123456789012"
    dynamodb_table = "terraform-state-lock"
  }
}

# Security-focused provider configuration
provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Environment   = var.environment
      Project       = "healthcare-platform"
      Compliance    = "HIPAA,PCI-DSS"
      ManagedBy     = "terraform"
      SecurityScan  = "enabled"
    }
  }
}

# Secure VPC configuration
resource "aws_vpc" "healthcare_vpc" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name = "healthcare-vpc-${var.environment}"
  }
}

# Security groups with least privilege
resource "aws_security_group" "web_sg" {
  name_prefix = "healthcare-web-"
  vpc_id      = aws_vpc.healthcare_vpc.id

  # HTTPS only
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS inbound"
  }

  # No direct HTTP access
  egress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS outbound"
  }

  tags = {
    Name = "healthcare-web-sg-${var.environment}"
  }
}

# Encrypted S3 bucket for healthcare data
resource "aws_s3_bucket" "healthcare_data" {
  bucket = "healthcare-data-${var.environment}-${random_id.bucket_suffix.hex}"

  tags = {
    Name        = "healthcare-data-${var.environment}"
    DataClass   = "PHI"
    Compliance  = "HIPAA"
  }
}

resource "aws_s3_bucket_encryption_configuration" "healthcare_data_encryption" {
  bucket = aws_s3_bucket.healthcare_data.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.healthcare_data_key.arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket_versioning" "healthcare_data_versioning" {
  bucket = aws_s3_bucket.healthcare_data.id

  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_public_access_block" "healthcare_data_pab" {
  bucket = aws_s3_bucket.healthcare_data.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
```

### 2. Kubernetes Security Manifests

```yaml
# Secure Kubernetes deployment for healthcare platform
apiVersion: v1
kind: Namespace
metadata:
  name: healthcare-production
  labels:
    name: healthcare-production
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: healthcare-app-sa
  namespace: healthcare-production
automountServiceAccountToken: false

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: healthcare-app-netpol
  namespace: healthcare-production
spec:
  podSelector:
    matchLabels:
      app: healthcare-app
  policyTypes:
    - Ingress
    - Egress

  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
      ports:
        - protocol: TCP
          port: 3000

  egress:
    - to: []
      ports:
        - protocol: UDP
          port: 53
    - to:
        - podSelector:
            matchLabels:
              app: postgresql
      ports:
        - protocol: TCP
          port: 5432

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: healthcare-app
  namespace: healthcare-production
spec:
  replicas: 3
  selector:
    matchLabels:
      app: healthcare-app
  template:
    metadata:
      labels:
        app: healthcare-app
      annotations:
        container.apparmor.security.beta.kubernetes.io/healthcare-app: runtime/default
    spec:
      serviceAccountName: healthcare-app-sa
      automountServiceAccountToken: false

      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
        seccompProfile:
          type: RuntimeDefault

      containers:
        - name: healthcare-app
          image: ghcr.io/healthcare-platform:latest

          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1001
            runAsGroup: 1001
            capabilities:
              drop:
                - ALL

          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"

          env:
            - name: NODE_ENV
              value: "production"
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: healthcare-db-secret
                  key: password

          volumeMounts:
            - name: tmp-volume
              mountPath: /tmp
            - name: cache-volume
              mountPath: /app/cache

          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10

          readinessProbe:
            httpGet:
              path: /ready
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5

      volumes:
        - name: tmp-volume
          emptyDir: {}
        - name: cache-volume
          emptyDir: {}
```

## 📊 Compliance Automation

### 1. HIPAA Compliance Automation

```python
#!/usr/bin/env python3
# HIPAA compliance automation for CI/CD pipeline

import json
import boto3
import requests
from datetime import datetime

class HIPAAComplianceChecker:
    def __init__(self):
        self.compliance_rules = {
            'encryption_at_rest': {
                'description': 'All data must be encrypted at rest',
                'severity': 'CRITICAL',
                'check_function': self.check_encryption_at_rest
            },
            'encryption_in_transit': {
                'description': 'All data transmission must be encrypted',
                'severity': 'CRITICAL',
                'check_function': self.check_encryption_in_transit
            },
            'access_controls': {
                'description': 'Proper access controls must be implemented',
                'severity': 'HIGH',
                'check_function': self.check_access_controls
            },
            'audit_logging': {
                'description': 'Comprehensive audit logging must be enabled',
                'severity': 'HIGH',
                'check_function': self.check_audit_logging
            },
            'data_backup': {
                'description': 'Regular data backups must be configured',
                'severity': 'MEDIUM',
                'check_function': self.check_data_backup
            }
        }

    def run_compliance_check(self, infrastructure_config):
        """Run comprehensive HIPAA compliance check"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'compliance_status': 'UNKNOWN',
            'total_checks': len(self.compliance_rules),
            'passed_checks': 0,
            'failed_checks': 0,
            'violations': [],
            'recommendations': []
        }

        for rule_name, rule_config in self.compliance_rules.items():
            try:
                check_result = rule_config['check_function'](infrastructure_config)

                if check_result['compliant']:
                    results['passed_checks'] += 1
                else:
                    results['failed_checks'] += 1
                    results['violations'].append({
                        'rule': rule_name,
                        'description': rule_config['description'],
                        'severity': rule_config['severity'],
                        'details': check_result['details'],
                        'remediation': check_result.get('remediation', 'No remediation provided')
                    })

            except Exception as e:
                results['failed_checks'] += 1
                results['violations'].append({
                    'rule': rule_name,
                    'description': rule_config['description'],
                    'severity': 'ERROR',
                    'details': f'Check failed: {str(e)}',
                    'remediation': 'Fix the compliance check implementation'
                })

        # Determine overall compliance status
        if results['failed_checks'] == 0:
            results['compliance_status'] = 'COMPLIANT'
        elif any(v['severity'] == 'CRITICAL' for v in results['violations']):
            results['compliance_status'] = 'NON_COMPLIANT_CRITICAL'
        else:
            results['compliance_status'] = 'NON_COMPLIANT'

        return results

    def check_encryption_at_rest(self, config):
        """Check if all data storage is encrypted at rest"""
        violations = []

        # Check S3 buckets
        for bucket in config.get('s3_buckets', []):
            if not bucket.get('encryption_enabled'):
                violations.append(f"S3 bucket {bucket['name']} is not encrypted")

        # Check RDS instances
        for rds in config.get('rds_instances', []):
            if not rds.get('storage_encrypted'):
                violations.append(f"RDS instance {rds['identifier']} is not encrypted")

        # Check EBS volumes
        for volume in config.get('ebs_volumes', []):
            if not volume.get('encrypted'):
                violations.append(f"EBS volume {volume['id']} is not encrypted")

        return {
            'compliant': len(violations) == 0,
            'details': violations,
            'remediation': 'Enable encryption for all data storage resources'
        }

    def check_encryption_in_transit(self, config):
        """Check if all data transmission is encrypted"""
        violations = []

        # Check load balancers
        for lb in config.get('load_balancers', []):
            if not all(listener.get('ssl_enabled') for listener in lb.get('listeners', [])):
                violations.append(f"Load balancer {lb['name']} has unencrypted listeners")

        # Check API gateways
        for api in config.get('api_gateways', []):
            if not api.get('tls_enabled'):
                violations.append(f"API Gateway {api['name']} does not enforce TLS")

        return {
            'compliant': len(violations) == 0,
            'details': violations,
            'remediation': 'Enable TLS/SSL for all data transmission endpoints'
        }

    def check_access_controls(self, config):
        """Check if proper access controls are implemented"""
        violations = []

        # Check IAM policies
        for policy in config.get('iam_policies', []):
            if policy.get('effect') == 'Allow' and policy.get('resource') == '*':
                violations.append(f"IAM policy {policy['name']} grants overly broad permissions")

        # Check security groups
        for sg in config.get('security_groups', []):
            for rule in sg.get('ingress_rules', []):
                if rule.get('cidr_blocks') == ['0.0.0.0/0'] and rule.get('port') != 443:
                    violations.append(f"Security group {sg['name']} allows unrestricted access")

        return {
            'compliant': len(violations) == 0,
            'details': violations,
            'remediation': 'Implement principle of least privilege for all access controls'
        }

    def generate_compliance_report(self, results):
        """Generate detailed compliance report"""
        report = {
            'executive_summary': {
                'compliance_status': results['compliance_status'],
                'total_checks': results['total_checks'],
                'passed_checks': results['passed_checks'],
                'failed_checks': results['failed_checks'],
                'compliance_percentage': (results['passed_checks'] / results['total_checks']) * 100
            },
            'detailed_findings': results['violations'],
            'recommendations': self.generate_recommendations(results),
            'next_steps': self.generate_next_steps(results)
        }

        return report

if __name__ == "__main__":
    checker = HIPAAComplianceChecker()

    # Load infrastructure configuration
    with open('infrastructure-config.json', 'r') as f:
        config = json.load(f)

    # Run compliance check
    results = checker.run_compliance_check(config)

    # Generate report
    report = checker.generate_compliance_report(results)

    # Save report
    with open('hipaa-compliance-report.json', 'w') as f:
        json.dump(report, f, indent=2)

    # Exit with appropriate code
    if results['compliance_status'] == 'NON_COMPLIANT_CRITICAL':
        exit(1)
    elif results['compliance_status'] == 'NON_COMPLIANT':
        exit(2)
    else:
        exit(0)
```

## 📚 Best Practices Summary

### Pipeline Security

1. **Security Gates**: Implement multiple security gates throughout the pipeline
2. **Fail Fast**: Fail builds early when security issues are detected
3. **Automated Scanning**: Integrate automated security scanning tools
4. **Manual Approval**: Require manual approval for production deployments
5. **Audit Trail**: Maintain comprehensive audit trails for all pipeline activities

### Secrets Management

1. **External Storage**: Store secrets in dedicated secret management systems
2. **Encryption**: Encrypt secrets at rest and in transit
3. **Rotation**: Implement automatic secret rotation
4. **Least Privilege**: Grant minimal access to secrets
5. **Monitoring**: Monitor secret access and usage

### Container Security

1. **Base Images**: Use minimal, security-hardened base images
2. **Vulnerability Scanning**: Scan containers for vulnerabilities
3. **Image Signing**: Sign container images for integrity verification
4. **Runtime Security**: Implement runtime security monitoring
5. **Regular Updates**: Keep base images and dependencies updated

### Compliance Automation

1. **Continuous Compliance**: Implement continuous compliance checking
2. **Policy as Code**: Define compliance policies as code
3. **Automated Reporting**: Generate automated compliance reports
4. **Remediation**: Provide automated remediation where possible
5. **Documentation**: Maintain comprehensive compliance documentation

## 🔗 Additional Resources

- [NIST Secure Software Development Framework](https://csrc.nist.gov/Projects/ssdf)
- [OWASP DevSecOps Guideline](https://owasp.org/www-project-devsecops-guideline/)
- [CIS Controls for Secure DevOps](https://www.cisecurity.org/controls)
- [Healthcare DevSecOps Best Practices](../23-healthcare-security.md)

---

**Next**: [Compliance Frameworks](16-compliance-frameworks.md) | **Previous**: [Security Testing](14-security-testing.md)

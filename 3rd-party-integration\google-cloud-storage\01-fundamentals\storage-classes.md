# Google Cloud Storage Classes

## Overview

Google Cloud Storage offers four storage classes designed for different access patterns and cost requirements. Understanding these classes is crucial for optimizing both performance and costs in healthcare applications.

## Storage Classes Deep Dive

### 1. Standard Storage

**Best for**: Frequently accessed data (hot data)

#### Characteristics
- **Availability**: 99.95% (multi-region: 99.95%)
- **Durability**: 99.999999999% (11 9's)
- **Minimum Storage Duration**: None
- **Retrieval Fees**: None
- **Access Pattern**: Multiple times per month

#### Healthcare Use Cases
```
✅ Active patient records
✅ Current consultation files
✅ Frequently accessed medical images
✅ Real-time application data
✅ Website assets and media
```

#### Pricing Example (US regions)
```
Storage: $0.020 per GB/month
Operations: $0.05 per 1,000 Class A operations
Network: Standard egress rates
```

### 2. Nearline Storage

**Best for**: Data accessed less than once a month

#### Characteristics
- **Availability**: 99.95% (multi-region: 99.95%)
- **Durability**: 99.999999999% (11 9's)
- **Minimum Storage Duration**: 30 days
- **Retrieval Fees**: $0.01 per GB
- **Access Pattern**: Once per month or less

#### Healthcare Use Cases
```
✅ Monthly backup data
✅ Quarterly compliance reports
✅ Historical patient records (1-2 years old)
✅ Archived consultation recordings
✅ Seasonal analytics data
```

#### Cost Optimization Strategy
```javascript
// Lifecycle rule for automatic transition
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "SetStorageClass", "storageClass": "NEARLINE"},
        "condition": {"age": 30, "matchesStorageClass": ["STANDARD"]}
      }
    ]
  }
}
```

### 3. Coldline Storage

**Best for**: Data accessed less than once a quarter

#### Characteristics
- **Availability**: 99.95%
- **Durability**: 99.999999999% (11 9's)
- **Minimum Storage Duration**: 90 days
- **Retrieval Fees**: $0.02 per GB
- **Access Pattern**: Once per quarter or less

#### Healthcare Use Cases
```
✅ Annual compliance archives
✅ Long-term patient history (3+ years)
✅ Research data backups
✅ Legal document archives
✅ Disaster recovery copies
```

#### Implementation Example
```python
from google.cloud import storage

def move_to_coldline(bucket_name, blob_name):
    """Move object to Coldline storage"""
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    blob = bucket.blob(blob_name)
    
    # Change storage class
    blob.update_storage_class('COLDLINE')
    
    print(f"Moved {blob_name} to Coldline storage")
```

### 4. Archive Storage

**Best for**: Long-term archival and digital preservation

#### Characteristics
- **Availability**: 99.95%
- **Durability**: 99.999999999% (11 9's)
- **Minimum Storage Duration**: 365 days
- **Retrieval Fees**: $0.05 per GB
- **Access Pattern**: Less than once per year

#### Healthcare Use Cases
```
✅ 7+ year regulatory compliance data
✅ Closed patient case archives
✅ Historical research datasets
✅ Legal discovery materials
✅ Long-term backup retention
```

#### Compliance Configuration
```yaml
# Archive lifecycle policy
lifecycle:
  rules:
    - action:
        type: SetStorageClass
        storageClass: ARCHIVE
      condition:
        age: 2555  # 7 years in days
        matchesStorageClass: [COLDLINE]
```

## Multi-Regional vs Regional Storage

### Multi-Regional Storage
```
Locations: Multiple regions (e.g., US, EU, ASIA)
Availability: 99.95%
Use Case: Global applications, disaster recovery
Cost: Higher storage, lower egress within regions
```

### Regional Storage
```
Locations: Single region (e.g., us-central1)
Availability: 99.90%
Use Case: Regional applications, compute co-location
Cost: Lower storage, standard egress rates
```

### Dual-Regional Storage
```
Locations: Two specific regions
Availability: 99.95%
Use Case: Geo-redundancy with data residency control
Cost: Between regional and multi-regional
```

## Healthcare Data Classification Strategy

### Tier 1: Active Data (Standard)
```
- Current patient consultations
- Active treatment plans
- Real-time monitoring data
- Frequently accessed images
- Application databases
```

### Tier 2: Recent Data (Nearline)
```
- Completed consultations (30-90 days)
- Monthly reports and analytics
- Recent backup copies
- Seasonal patient data
```

### Tier 3: Historical Data (Coldline)
```
- Patient records (1-7 years)
- Compliance documentation
- Research datasets
- Annual audit materials
```

### Tier 4: Archive Data (Archive)
```
- Legal retention requirements (7+ years)
- Closed case documentation
- Historical research data
- Long-term compliance archives
```

## Cost Optimization Strategies

### 1. Lifecycle Management
```json
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "SetStorageClass", "storageClass": "NEARLINE"},
        "condition": {"age": 30}
      },
      {
        "action": {"type": "SetStorageClass", "storageClass": "COLDLINE"},
        "condition": {"age": 365}
      },
      {
        "action": {"type": "SetStorageClass", "storageClass": "ARCHIVE"},
        "condition": {"age": 2555}
      }
    ]
  }
}
```

### 2. Regional Strategy
```
Primary Region: us-central1 (lowest cost)
Backup Region: us-east1 (disaster recovery)
Global CDN: Cloud CDN for worldwide access
```

### 3. Access Pattern Analysis
```python
# Monitor access patterns
def analyze_access_patterns(bucket_name):
    """Analyze object access patterns for optimization"""
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    
    for blob in bucket.list_blobs():
        # Check last access time
        if blob.time_created < datetime.now() - timedelta(days=30):
            # Candidate for Nearline
            print(f"Consider moving {blob.name} to Nearline")
```

## Decision Matrix

| Data Type | Access Frequency | Retention Period | Recommended Class |
|-----------|------------------|------------------|-------------------|
| Active patient files | Daily | Current | Standard |
| Consultation records | Weekly | 1-3 months | Standard → Nearline |
| Medical images | Monthly | 1-2 years | Nearline → Coldline |
| Compliance docs | Quarterly | 7+ years | Coldline → Archive |
| Research data | Yearly | 10+ years | Archive |

## Implementation Checklist

- [ ] Analyze current data access patterns
- [ ] Classify data by access frequency
- [ ] Design lifecycle policies
- [ ] Set up monitoring for cost optimization
- [ ] Implement automated transitions
- [ ] Test retrieval processes
- [ ] Document data classification policies
- [ ] Train team on storage class selection

## Next Steps

1. **[Security Model](./security-model.md)** - Understand access control mechanisms
2. **[Pricing Structure](./pricing.md)** - Detailed cost analysis and optimization
3. **[Lifecycle Management](../02-gcs-deep-dive/lifecycle.md)** - Automated data management

---

*Choose the right storage class based on your data access patterns and compliance requirements to optimize both cost and performance.*

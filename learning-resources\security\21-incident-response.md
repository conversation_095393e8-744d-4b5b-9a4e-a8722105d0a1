# Security Incident Response

## 🚨 Incident Response Framework

### NIST Incident Response Lifecycle

#### 1. Preparation Phase
```python
class IncidentResponsePreparation:
    def __init__(self):
        self.incident_response_team = {
            'incident_commander': 'security_manager',
            'technical_lead': 'senior_security_engineer',
            'communications_lead': 'pr_manager',
            'legal_counsel': 'legal_team',
            'compliance_officer': 'compliance_manager'
        }
        
        self.contact_information = {
            'internal_escalation': {
                'security_team': '+1-555-SEC-TEAM',
                'management': '+1-555-MGMT',
                'legal': '+1-555-LEGAL'
            },
            'external_contacts': {
                'law_enforcement': '+1-555-FBI-CYBER',
                'cyber_insurance': '+1-555-INSURANC<PERSON>',
                'forensics_vendor': '+1-555-FORENSICS',
                'pr_agency': '+1-555-PR-HELP'
            }
        }
    
    def create_incident_response_plan(self):
        """Create comprehensive incident response plan"""
        plan = {
            'scope': 'All systems handling PHI and payment data',
            'objectives': [
                'Minimize business impact',
                'Preserve evidence',
                'Restore normal operations',
                'Prevent recurrence',
                'Meet compliance requirements'
            ],
            'roles_responsibilities': self.incident_response_team,
            'communication_plan': self.create_communication_plan(),
            'escalation_procedures': self.create_escalation_procedures(),
            'tools_resources': self.list_incident_tools()
        }
        
        return plan
    
    def setup_monitoring_alerts(self):
        """Setup security monitoring and alerting"""
        alert_rules = {
            'authentication_failures': {
                'threshold': 5,
                'timeframe': '5 minutes',
                'severity': 'medium'
            },
            'privilege_escalation': {
                'threshold': 1,
                'timeframe': '1 minute',
                'severity': 'high'
            },
            'data_exfiltration': {
                'threshold': '100MB',
                'timeframe': '10 minutes',
                'severity': 'critical'
            },
            'malware_detection': {
                'threshold': 1,
                'timeframe': 'immediate',
                'severity': 'critical'
            }
        }
        
        return alert_rules
```

#### 2. Detection and Analysis
```python
class IncidentDetectionAnalysis:
    def __init__(self):
        self.severity_levels = {
            'low': {'impact': 'minimal', 'urgency': 'low'},
            'medium': {'impact': 'moderate', 'urgency': 'medium'},
            'high': {'impact': 'significant', 'urgency': 'high'},
            'critical': {'impact': 'severe', 'urgency': 'immediate'}
        }
    
    def detect_security_incident(self, alert_data):
        """Automated incident detection"""
        incident_indicators = {
            'multiple_failed_logins': self.analyze_failed_logins(alert_data),
            'unusual_network_traffic': self.analyze_network_traffic(alert_data),
            'malware_signatures': self.analyze_malware_indicators(alert_data),
            'data_access_anomalies': self.analyze_data_access(alert_data),
            'system_modifications': self.analyze_system_changes(alert_data)
        }
        
        # Calculate incident score
        incident_score = sum(
            1 for indicator in incident_indicators.values() 
            if indicator['detected']
        )
        
        if incident_score >= 2:
            return self.create_incident_record(alert_data, incident_indicators)
        
        return None
    
    def create_incident_record(self, alert_data, indicators):
        """Create formal incident record"""
        incident = {
            'incident_id': f"INC-{datetime.now().strftime('%Y%m%d')}-{secrets.token_hex(4).upper()}",
            'detection_time': datetime.now(),
            'severity': self.calculate_severity(indicators),
            'category': self.categorize_incident(indicators),
            'affected_systems': alert_data.get('affected_systems', []),
            'indicators': indicators,
            'status': 'new',
            'assigned_to': None,
            'timeline': [
                {
                    'timestamp': datetime.now(),
                    'action': 'incident_created',
                    'details': 'Automated detection triggered incident creation'
                }
            ]
        }
        
        # Store incident record
        self.store_incident(incident)
        
        # Trigger initial response
        self.trigger_initial_response(incident)
        
        return incident
    
    def analyze_failed_logins(self, alert_data):
        """Analyze failed login patterns"""
        failed_logins = alert_data.get('failed_logins', [])
        
        analysis = {
            'detected': False,
            'confidence': 0,
            'details': {}
        }
        
        if len(failed_logins) > 10:
            # Check for brute force patterns
            ip_addresses = [login['ip'] for login in failed_logins]
            unique_ips = len(set(ip_addresses))
            
            if unique_ips == 1:
                analysis['detected'] = True
                analysis['confidence'] = 0.8
                analysis['details'] = {
                    'pattern': 'brute_force_single_ip',
                    'attempts': len(failed_logins),
                    'source_ip': ip_addresses[0]
                }
            elif unique_ips > 5:
                analysis['detected'] = True
                analysis['confidence'] = 0.9
                analysis['details'] = {
                    'pattern': 'distributed_brute_force',
                    'attempts': len(failed_logins),
                    'source_ips': unique_ips
                }
        
        return analysis
    
    def perform_initial_triage(self, incident_id):
        """Perform initial incident triage"""
        incident = self.get_incident(incident_id)
        
        triage_checklist = {
            'scope_assessment': self.assess_incident_scope(incident),
            'impact_analysis': self.analyze_business_impact(incident),
            'evidence_preservation': self.preserve_initial_evidence(incident),
            'stakeholder_notification': self.notify_stakeholders(incident),
            'containment_assessment': self.assess_containment_needs(incident)
        }
        
        # Update incident with triage results
        incident['triage_results'] = triage_checklist
        incident['status'] = 'triaged'
        
        self.update_incident(incident)
        
        return triage_checklist
```

#### 3. Containment, Eradication, and Recovery
```python
class IncidentContainmentRecovery:
    def __init__(self):
        self.containment_strategies = {
            'network_isolation': self.isolate_network_segment,
            'account_lockout': self.lockout_compromised_accounts,
            'system_shutdown': self.shutdown_affected_systems,
            'traffic_blocking': self.block_malicious_traffic,
            'service_degradation': self.degrade_service_performance
        }
    
    def implement_containment(self, incident_id, strategy):
        """Implement incident containment strategy"""
        incident = self.get_incident(incident_id)
        
        containment_action = {
            'timestamp': datetime.now(),
            'strategy': strategy,
            'implemented_by': session.get('user_id'),
            'status': 'in_progress',
            'details': {}
        }
        
        try:
            # Execute containment strategy
            result = self.containment_strategies[strategy](incident)
            
            containment_action['status'] = 'completed'
            containment_action['details'] = result
            
            # Update incident timeline
            incident['timeline'].append({
                'timestamp': datetime.now(),
                'action': 'containment_implemented',
                'details': f"Containment strategy '{strategy}' implemented successfully"
            })
            
        except Exception as e:
            containment_action['status'] = 'failed'
            containment_action['details'] = {'error': str(e)}
            
            # Log containment failure
            incident['timeline'].append({
                'timestamp': datetime.now(),
                'action': 'containment_failed',
                'details': f"Containment strategy '{strategy}' failed: {str(e)}"
            })
        
        # Store containment action
        incident['containment_actions'] = incident.get('containment_actions', [])
        incident['containment_actions'].append(containment_action)
        
        self.update_incident(incident)
        
        return containment_action
    
    def isolate_network_segment(self, incident):
        """Isolate affected network segment"""
        affected_systems = incident.get('affected_systems', [])
        
        isolation_results = []
        
        for system in affected_systems:
            try:
                # Implement network isolation
                firewall_rule = {
                    'action': 'deny',
                    'source': system['ip_address'],
                    'destination': 'any',
                    'protocol': 'any',
                    'ports': 'any'
                }
                
                # Apply firewall rule
                rule_id = self.apply_firewall_rule(firewall_rule)
                
                isolation_results.append({
                    'system': system['hostname'],
                    'ip_address': system['ip_address'],
                    'isolation_status': 'isolated',
                    'firewall_rule_id': rule_id
                })
                
            except Exception as e:
                isolation_results.append({
                    'system': system['hostname'],
                    'ip_address': system['ip_address'],
                    'isolation_status': 'failed',
                    'error': str(e)
                })
        
        return {
            'isolation_results': isolation_results,
            'total_systems': len(affected_systems),
            'successfully_isolated': len([r for r in isolation_results if r['isolation_status'] == 'isolated'])
        }
    
    def eradicate_threat(self, incident_id):
        """Eradicate identified threats"""
        incident = self.get_incident(incident_id)
        
        eradication_tasks = {
            'malware_removal': self.remove_malware(incident),
            'account_cleanup': self.cleanup_compromised_accounts(incident),
            'vulnerability_patching': self.patch_vulnerabilities(incident),
            'configuration_hardening': self.harden_configurations(incident),
            'credential_rotation': self.rotate_credentials(incident)
        }
        
        eradication_summary = {
            'timestamp': datetime.now(),
            'tasks_completed': 0,
            'tasks_failed': 0,
            'details': {}
        }
        
        for task_name, task_result in eradication_tasks.items():
            if task_result.get('success', False):
                eradication_summary['tasks_completed'] += 1
            else:
                eradication_summary['tasks_failed'] += 1
            
            eradication_summary['details'][task_name] = task_result
        
        # Update incident
        incident['eradication_summary'] = eradication_summary
        incident['timeline'].append({
            'timestamp': datetime.now(),
            'action': 'eradication_completed',
            'details': f"Eradication completed: {eradication_summary['tasks_completed']} successful, {eradication_summary['tasks_failed']} failed"
        })
        
        self.update_incident(incident)
        
        return eradication_summary
    
    def initiate_recovery(self, incident_id):
        """Initiate system recovery process"""
        incident = self.get_incident(incident_id)
        
        recovery_plan = {
            'phase_1_critical_systems': self.restore_critical_systems(incident),
            'phase_2_business_systems': self.restore_business_systems(incident),
            'phase_3_monitoring': self.enhance_monitoring(incident),
            'phase_4_validation': self.validate_recovery(incident)
        }
        
        recovery_status = {
            'start_time': datetime.now(),
            'current_phase': 'phase_1_critical_systems',
            'completion_percentage': 0,
            'estimated_completion': None
        }
        
        # Update incident status
        incident['status'] = 'recovery'
        incident['recovery_plan'] = recovery_plan
        incident['recovery_status'] = recovery_status
        
        self.update_incident(incident)
        
        return recovery_plan
```

#### 4. Post-Incident Activities
```python
class PostIncidentActivities:
    def __init__(self):
        self.lessons_learned_template = {
            'incident_summary': '',
            'timeline_of_events': [],
            'root_cause_analysis': '',
            'what_worked_well': [],
            'areas_for_improvement': [],
            'action_items': [],
            'preventive_measures': []
        }
    
    def conduct_post_incident_review(self, incident_id):
        """Conduct comprehensive post-incident review"""
        incident = self.get_incident(incident_id)
        
        review = {
            'incident_id': incident_id,
            'review_date': datetime.now(),
            'participants': self.get_review_participants(incident),
            'incident_metrics': self.calculate_incident_metrics(incident),
            'lessons_learned': self.extract_lessons_learned(incident),
            'improvement_recommendations': self.generate_recommendations(incident)
        }
        
        # Schedule follow-up actions
        self.schedule_improvement_actions(review['improvement_recommendations'])
        
        return review
    
    def calculate_incident_metrics(self, incident):
        """Calculate key incident response metrics"""
        timeline = incident.get('timeline', [])
        
        if len(timeline) < 2:
            return {}
        
        detection_time = datetime.fromisoformat(timeline[0]['timestamp'])
        
        # Find key milestone timestamps
        containment_time = None
        eradication_time = None
        recovery_time = None
        
        for event in timeline:
            event_time = datetime.fromisoformat(event['timestamp'])
            
            if 'containment' in event['action'] and not containment_time:
                containment_time = event_time
            elif 'eradication' in event['action'] and not eradication_time:
                eradication_time = event_time
            elif 'recovery' in event['action'] and not recovery_time:
                recovery_time = event_time
        
        metrics = {
            'mean_time_to_detect': 'N/A',  # Would need attack start time
            'mean_time_to_contain': (containment_time - detection_time).total_seconds() / 60 if containment_time else None,
            'mean_time_to_eradicate': (eradication_time - detection_time).total_seconds() / 60 if eradication_time else None,
            'mean_time_to_recover': (recovery_time - detection_time).total_seconds() / 60 if recovery_time else None,
            'total_incident_duration': (timeline[-1]['timestamp'] - timeline[0]['timestamp']).total_seconds() / 3600
        }
        
        return metrics
    
    def generate_incident_report(self, incident_id):
        """Generate comprehensive incident report"""
        incident = self.get_incident(incident_id)
        post_review = self.get_post_incident_review(incident_id)
        
        report = {
            'executive_summary': self.create_executive_summary(incident),
            'incident_details': {
                'incident_id': incident['incident_id'],
                'detection_time': incident['detection_time'],
                'severity': incident['severity'],
                'category': incident['category'],
                'affected_systems': incident.get('affected_systems', []),
                'business_impact': incident.get('business_impact', {})
            },
            'timeline_of_events': incident.get('timeline', []),
            'response_actions': {
                'containment': incident.get('containment_actions', []),
                'eradication': incident.get('eradication_summary', {}),
                'recovery': incident.get('recovery_plan', {})
            },
            'root_cause_analysis': post_review.get('lessons_learned', {}).get('root_cause_analysis', ''),
            'lessons_learned': post_review.get('lessons_learned', {}),
            'recommendations': post_review.get('improvement_recommendations', []),
            'compliance_considerations': self.assess_compliance_impact(incident)
        }
        
        return report
    
    def assess_compliance_impact(self, incident):
        """Assess compliance implications of incident"""
        compliance_assessment = {
            'hipaa_breach_assessment': self.assess_hipaa_breach(incident),
            'pci_incident_reporting': self.assess_pci_reporting(incident),
            'notification_requirements': self.determine_notification_requirements(incident),
            'regulatory_reporting': self.determine_regulatory_reporting(incident)
        }
        
        return compliance_assessment
    
    def assess_hipaa_breach(self, incident):
        """Assess if incident constitutes HIPAA breach"""
        phi_involved = incident.get('phi_involved', False)
        
        if not phi_involved:
            return {'breach_determination': 'no_breach', 'reason': 'No PHI involved'}
        
        # Assess risk of harm
        risk_factors = {
            'nature_of_phi': incident.get('phi_types', []),
            'amount_of_phi': incident.get('phi_records_affected', 0),
            'likelihood_of_compromise': incident.get('compromise_likelihood', 'unknown'),
            'likelihood_of_reidentification': incident.get('reidentification_risk', 'low')
        }
        
        # Determine if breach notification required
        if risk_factors['phi_records_affected'] > 0:
            return {
                'breach_determination': 'breach_likely',
                'notification_required': True,
                'notification_deadline': datetime.now() + timedelta(days=60),
                'risk_factors': risk_factors
            }
        
        return {
            'breach_determination': 'assessment_needed',
            'risk_factors': risk_factors
        }
```

## 🔧 Incident Response Tools and Automation

### Automated Response Actions
```python
class AutomatedIncidentResponse:
    def __init__(self):
        self.automation_rules = {
            'malware_detection': {
                'actions': ['isolate_system', 'scan_network', 'notify_team'],
                'auto_execute': True
            },
            'brute_force_attack': {
                'actions': ['block_ip', 'lockout_account', 'increase_monitoring'],
                'auto_execute': True
            },
            'data_exfiltration': {
                'actions': ['block_traffic', 'preserve_evidence', 'escalate_immediately'],
                'auto_execute': False  # Requires human approval
            }
        }
    
    def execute_automated_response(self, incident_type, incident_data):
        """Execute automated response based on incident type"""
        if incident_type not in self.automation_rules:
            return {'status': 'no_automation_available'}
        
        rule = self.automation_rules[incident_type]
        
        if not rule['auto_execute']:
            return {'status': 'human_approval_required', 'actions': rule['actions']}
        
        execution_results = []
        
        for action in rule['actions']:
            try:
                result = self.execute_response_action(action, incident_data)
                execution_results.append({
                    'action': action,
                    'status': 'success',
                    'result': result
                })
            except Exception as e:
                execution_results.append({
                    'action': action,
                    'status': 'failed',
                    'error': str(e)
                })
        
        return {
            'status': 'automated_response_executed',
            'results': execution_results
        }
    
    def execute_response_action(self, action, incident_data):
        """Execute specific response action"""
        action_handlers = {
            'isolate_system': self.isolate_system,
            'block_ip': self.block_ip_address,
            'lockout_account': self.lockout_user_account,
            'scan_network': self.initiate_network_scan,
            'notify_team': self.notify_incident_team,
            'preserve_evidence': self.preserve_digital_evidence
        }
        
        handler = action_handlers.get(action)
        if handler:
            return handler(incident_data)
        else:
            raise ValueError(f"Unknown action: {action}")

### Incident Communication System
```python
class IncidentCommunication:
    def __init__(self):
        self.communication_channels = {
            'email': self.send_email_notification,
            'sms': self.send_sms_notification,
            'slack': self.send_slack_notification,
            'phone': self.initiate_phone_call
        }
        
        self.stakeholder_groups = {
            'incident_team': {
                'members': ['security_team', 'it_team', 'management'],
                'channels': ['email', 'slack'],
                'urgency_threshold': 'medium'
            },
            'executive_team': {
                'members': ['ceo', 'cto', 'ciso'],
                'channels': ['email', 'phone'],
                'urgency_threshold': 'high'
            },
            'legal_compliance': {
                'members': ['legal_counsel', 'compliance_officer'],
                'channels': ['email'],
                'urgency_threshold': 'high'
            }
        }
    
    def send_incident_notification(self, incident, stakeholder_group):
        """Send incident notification to stakeholder group"""
        group_config = self.stakeholder_groups.get(stakeholder_group)
        
        if not group_config:
            raise ValueError(f"Unknown stakeholder group: {stakeholder_group}")
        
        # Check if incident meets urgency threshold
        if not self.meets_urgency_threshold(incident['severity'], group_config['urgency_threshold']):
            return {'status': 'notification_skipped', 'reason': 'below_urgency_threshold'}
        
        notification_results = []
        
        for channel in group_config['channels']:
            try:
                result = self.send_notification_via_channel(
                    channel, 
                    incident, 
                    group_config['members']
                )
                notification_results.append({
                    'channel': channel,
                    'status': 'sent',
                    'result': result
                })
            except Exception as e:
                notification_results.append({
                    'channel': channel,
                    'status': 'failed',
                    'error': str(e)
                })
        
        return {
            'status': 'notifications_sent',
            'results': notification_results
        }
```

## 📋 Incident Response Checklist

### Immediate Response (First 30 minutes)
- [ ] Confirm incident is legitimate
- [ ] Assess initial scope and impact
- [ ] Notify incident response team
- [ ] Begin evidence preservation
- [ ] Implement immediate containment if possible
- [ ] Document all actions taken

### Short-term Response (First 4 hours)
- [ ] Complete detailed impact assessment
- [ ] Implement comprehensive containment
- [ ] Notify stakeholders and management
- [ ] Begin forensic analysis
- [ ] Assess legal and compliance requirements
- [ ] Coordinate with external parties if needed

### Recovery Phase (24-72 hours)
- [ ] Eradicate threat from environment
- [ ] Restore systems from clean backups
- [ ] Implement additional monitoring
- [ ] Validate system integrity
- [ ] Gradually restore normal operations
- [ ] Monitor for signs of reinfection

### Post-Incident (1-2 weeks)
- [ ] Conduct lessons learned session
- [ ] Update incident response procedures
- [ ] Implement security improvements
- [ ] Complete compliance reporting
- [ ] Archive incident documentation
- [ ] Schedule follow-up reviews

## 📚 Next Steps

1. **Study**: Penetration Testing (22-penetration-testing.md)
2. **Practice**: Incident response tabletop exercises
3. **Implement**: Automated incident detection
4. **Training**: Incident response team certification

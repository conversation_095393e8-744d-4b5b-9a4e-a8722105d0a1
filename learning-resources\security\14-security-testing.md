# Security Testing

## Overview

Security testing is essential for healthcare platforms to identify vulnerabilities before they can be exploited. This guide covers comprehensive security testing methodologies including Static Application Security Testing (SAST), Dynamic Application Security Testing (DAST), Interactive Application Security Testing (IAST), and penetration testing specifically for healthcare applications.

## 🎯 Learning Objectives

By the end of this module, you will understand:
- Security testing methodologies and tools
- Static Application Security Testing (SAST) implementation
- Dynamic Application Security Testing (DAST) strategies
- Interactive Application Security Testing (IAST) approaches
- Penetration testing for healthcare applications
- Security test automation and CI/CD integration

## 🔍 Security Testing Fundamentals

### 1. Security Testing Strategy

```yaml
# Comprehensive security testing strategy for healthcare platform
security_testing_strategy:
  phases:
    planning:
      - threat_modeling
      - risk_assessment
      - test_scope_definition
      - compliance_requirements_mapping
    
    static_testing:
      - source_code_analysis
      - dependency_vulnerability_scanning
      - infrastructure_as_code_scanning
      - secrets_detection
    
    dynamic_testing:
      - web_application_scanning
      - api_security_testing
      - network_penetration_testing
      - social_engineering_assessment
    
    interactive_testing:
      - runtime_application_testing
      - behavioral_analysis
      - real_time_vulnerability_detection
    
    manual_testing:
      - penetration_testing
      - security_code_review
      - architecture_review
      - compliance_testing

  tools_matrix:
    sast_tools:
      - sonarqube
      - checkmarx
      - veracode
      - semgrep
    
    dast_tools:
      - owasp_zap
      - burp_suite
      - nessus
      - qualys
    
    iast_tools:
      - contrast_security
      - hdiv_security
      - seeker_security
    
    dependency_scanning:
      - snyk
      - whitesource
      - black_duck
      - npm_audit

  compliance_testing:
    hipaa:
      - access_controls
      - audit_logs
      - encryption_verification
      - data_integrity_checks
    
    pci_dss:
      - payment_data_protection
      - secure_transmission
      - access_restrictions
      - vulnerability_management
```

### 2. Automated Security Testing Framework

```javascript
// Comprehensive security testing framework for healthcare platform
class SecurityTestingFramework {
  constructor() {
    this.testSuites = new Map();
    this.vulnerabilityDatabase = new VulnerabilityDatabase();
    this.reportGenerator = new SecurityReportGenerator();
    this.complianceChecker = new ComplianceChecker();
  }

  // Initialize security test suites
  async initializeTestSuites() {
    // OWASP Top 10 test suite
    this.testSuites.set('owasp-top10', new OWASPTop10TestSuite());
    
    // Healthcare-specific test suite
    this.testSuites.set('healthcare-security', new HealthcareSecurityTestSuite());
    
    // API security test suite
    this.testSuites.set('api-security', new APISecurityTestSuite());
    
    // Authentication test suite
    this.testSuites.set('authentication', new AuthenticationTestSuite());
    
    // Data protection test suite
    this.testSuites.set('data-protection', new DataProtectionTestSuite());
  }

  // Execute comprehensive security testing
  async executeSecurityTests(target, options = {}) {
    const testResults = {
      target,
      timestamp: new Date(),
      testSuites: {},
      summary: {
        totalTests: 0,
        passed: 0,
        failed: 0,
        vulnerabilities: []
      }
    };

    try {
      // Execute each test suite
      for (const [suiteName, testSuite] of this.testSuites) {
        console.log(`Executing ${suiteName} test suite...`);
        
        const suiteResults = await testSuite.execute(target, options);
        testResults.testSuites[suiteName] = suiteResults;
        
        // Update summary
        testResults.summary.totalTests += suiteResults.totalTests;
        testResults.summary.passed += suiteResults.passed;
        testResults.summary.failed += suiteResults.failed;
        testResults.summary.vulnerabilities.push(...suiteResults.vulnerabilities);
      }

      // Generate compliance report
      const complianceResults = await this.complianceChecker.checkCompliance(
        testResults,
        ['HIPAA', 'PCI-DSS', 'OWASP-ASVS']
      );
      testResults.compliance = complianceResults;

      // Generate security report
      const report = await this.reportGenerator.generateReport(testResults);
      testResults.reportPath = report.path;

      return testResults;

    } catch (error) {
      console.error('Security testing failed:', error);
      throw new Error(`Security testing execution failed: ${error.message}`);
    }
  }
}

// OWASP Top 10 Test Suite
class OWASPTop10TestSuite {
  constructor() {
    this.testCases = [
      new InjectionTests(),
      new BrokenAuthenticationTests(),
      new SensitiveDataExposureTests(),
      new XXETests(),
      new BrokenAccessControlTests(),
      new SecurityMisconfigurationTests(),
      new XSSTests(),
      new InsecureDeserializationTests(),
      new ComponentVulnerabilityTests(),
      new InsufficientLoggingTests()
    ];
  }

  async execute(target, options) {
    const results = {
      suiteName: 'OWASP Top 10',
      totalTests: 0,
      passed: 0,
      failed: 0,
      vulnerabilities: [],
      testCases: {}
    };

    for (const testCase of this.testCases) {
      const caseResults = await testCase.run(target, options);
      results.testCases[testCase.name] = caseResults;
      
      results.totalTests += caseResults.totalTests;
      results.passed += caseResults.passed;
      results.failed += caseResults.failed;
      results.vulnerabilities.push(...caseResults.vulnerabilities);
    }

    return results;
  }
}

// SQL Injection Tests
class InjectionTests {
  constructor() {
    this.name = 'Injection Tests';
    this.payloads = [
      "' OR '1'='1",
      "'; DROP TABLE users; --",
      "' UNION SELECT * FROM users --",
      "1' AND (SELECT COUNT(*) FROM users) > 0 --",
      "'; WAITFOR DELAY '00:00:05' --"
    ];
  }

  async run(target, options) {
    const results = {
      totalTests: 0,
      passed: 0,
      failed: 0,
      vulnerabilities: []
    };

    // Test login endpoints
    const loginEndpoints = [
      `${target}/api/auth/login`,
      `${target}/api/users/authenticate`,
      `${target}/login`
    ];

    for (const endpoint of loginEndpoints) {
      for (const payload of this.payloads) {
        results.totalTests++;
        
        try {
          const response = await this.testSQLInjection(endpoint, payload);
          
          if (this.isVulnerable(response)) {
            results.failed++;
            results.vulnerabilities.push({
              type: 'SQL_INJECTION',
              severity: 'HIGH',
              endpoint,
              payload,
              description: 'SQL injection vulnerability detected',
              evidence: response.evidence
            });
          } else {
            results.passed++;
          }
        } catch (error) {
          results.failed++;
          results.vulnerabilities.push({
            type: 'TEST_ERROR',
            severity: 'MEDIUM',
            endpoint,
            error: error.message
          });
        }
      }
    }

    return results;
  }

  async testSQLInjection(endpoint, payload) {
    const testData = {
      email: `admin${payload}`,
      password: 'password'
    };

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testData)
    });

    const responseText = await response.text();
    const responseTime = response.headers.get('x-response-time') || 0;

    return {
      status: response.status,
      body: responseText,
      responseTime: parseInt(responseTime),
      headers: Object.fromEntries(response.headers.entries())
    };
  }

  isVulnerable(response) {
    // Check for SQL error messages
    const sqlErrors = [
      'sql syntax',
      'mysql_fetch',
      'ora-',
      'microsoft ole db',
      'postgresql',
      'sqlite'
    ];

    const bodyLower = response.body.toLowerCase();
    
    // Check for error messages
    if (sqlErrors.some(error => bodyLower.includes(error))) {
      return true;
    }

    // Check for unusual response times (time-based injection)
    if (response.responseTime > 5000) {
      return true;
    }

    // Check for successful authentication with malicious payload
    if (response.status === 200 && bodyLower.includes('token')) {
      return true;
    }

    return false;
  }
}

// XSS Tests
class XSSTests {
  constructor() {
    this.name = 'XSS Tests';
    this.payloads = [
      '<script>alert("XSS")</script>',
      '<img src=x onerror=alert("XSS")>',
      'javascript:alert("XSS")',
      '<svg onload=alert("XSS")>',
      '"><script>alert("XSS")</script>',
      "';alert('XSS');//",
      '<iframe src="javascript:alert(\'XSS\')"></iframe>'
    ];
  }

  async run(target, options) {
    const results = {
      totalTests: 0,
      passed: 0,
      failed: 0,
      vulnerabilities: []
    };

    // Test common input fields
    const testEndpoints = [
      { url: `${target}/api/patients/search`, param: 'query' },
      { url: `${target}/api/appointments/create`, param: 'notes' },
      { url: `${target}/api/users/profile`, param: 'bio' }
    ];

    for (const endpoint of testEndpoints) {
      for (const payload of this.payloads) {
        results.totalTests++;
        
        try {
          const response = await this.testXSS(endpoint, payload);
          
          if (this.isXSSVulnerable(response, payload)) {
            results.failed++;
            results.vulnerabilities.push({
              type: 'XSS',
              severity: 'HIGH',
              endpoint: endpoint.url,
              parameter: endpoint.param,
              payload,
              description: 'Cross-Site Scripting vulnerability detected',
              evidence: response.evidence
            });
          } else {
            results.passed++;
          }
        } catch (error) {
          results.failed++;
        }
      }
    }

    return results;
  }

  async testXSS(endpoint, payload) {
    const testData = {};
    testData[endpoint.param] = payload;

    const response = await fetch(endpoint.url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testData)
    });

    const responseText = await response.text();

    return {
      status: response.status,
      body: responseText,
      headers: Object.fromEntries(response.headers.entries())
    };
  }

  isXSSVulnerable(response, payload) {
    // Check if payload is reflected without encoding
    const unescapedPayload = payload.replace(/&lt;/g, '<').replace(/&gt;/g, '>');
    
    if (response.body.includes(unescapedPayload)) {
      return true;
    }

    // Check for script execution indicators
    if (response.body.includes('alert(') || response.body.includes('javascript:')) {
      return true;
    }

    return false;
  }
}
```

## 🔧 Static Application Security Testing (SAST)

### 1. SonarQube Integration

```yaml
# SonarQube configuration for healthcare platform security scanning
sonar-project.properties: |
  sonar.projectKey=healthcare-platform
  sonar.projectName=Healthcare Platform
  sonar.projectVersion=1.0
  sonar.sources=src
  sonar.tests=tests
  sonar.language=js,ts,java,python
  sonar.sourceEncoding=UTF-8
  
  # Security-focused quality gates
  sonar.qualitygate.wait=true
  sonar.security.hotspots.threshold=0
  sonar.security.vulnerabilities.threshold=0
  
  # Healthcare-specific rules
  sonar.javascript.lcov.reportPaths=coverage/lcov.info
  sonar.typescript.lcov.reportPaths=coverage/lcov.info
  
  # Exclude test files from security analysis
  sonar.exclusions=**/*test*/**,**/*spec*/**,**/node_modules/**
  
  # Include security-focused rules
  sonar.javascript.globals=window,document,console,require,module,exports
```

```javascript
// Custom SonarQube rules for healthcare security
class HealthcareSonarRules {
  static getRules() {
    return [
      {
        key: 'healthcare-sensitive-data-logging',
        name: 'Sensitive Healthcare Data in Logs',
        description: 'Detects potential logging of sensitive healthcare data',
        severity: 'CRITICAL',
        type: 'VULNERABILITY',
        pattern: /console\.log.*(?:ssn|social|medical|patient|diagnosis)/i
      },
      {
        key: 'healthcare-unencrypted-storage',
        name: 'Unencrypted Healthcare Data Storage',
        description: 'Detects potential unencrypted storage of healthcare data',
        severity: 'CRITICAL',
        type: 'VULNERABILITY',
        pattern: /localStorage\.setItem.*(?:patient|medical|health)/i
      },
      {
        key: 'healthcare-weak-crypto',
        name: 'Weak Cryptography for Healthcare Data',
        description: 'Detects use of weak cryptographic algorithms',
        severity: 'HIGH',
        type: 'VULNERABILITY',
        pattern: /(?:md5|sha1|des|rc4)/i
      },
      {
        key: 'healthcare-sql-injection',
        name: 'Potential SQL Injection in Healthcare Queries',
        description: 'Detects potential SQL injection vulnerabilities',
        severity: 'CRITICAL',
        type: 'VULNERABILITY',
        pattern: /query.*\+.*(?:req\.body|req\.params|req\.query)/
      }
    ];
  }

  static analyzeCode(sourceCode) {
    const violations = [];
    const rules = this.getRules();

    for (const rule of rules) {
      const matches = sourceCode.match(new RegExp(rule.pattern, 'g'));
      if (matches) {
        violations.push({
          rule: rule.key,
          severity: rule.severity,
          message: rule.description,
          matches: matches.length
        });
      }
    }

    return violations;
  }
}
```

### 2. Automated SAST Pipeline

```yaml
# GitHub Actions workflow for SAST
name: Security SAST Scan

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  sast-scan:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run ESLint Security Plugin
        run: |
          npm install eslint-plugin-security --save-dev
          npx eslint . --ext .js,.ts --format json --output-file eslint-security.json
        continue-on-error: true
      
      - name: Run Semgrep SAST
        uses: returntocorp/semgrep-action@v1
        with:
          config: >-
            p/security-audit
            p/owasp-top-ten
            p/javascript
            p/typescript
            r/javascript.express.security
        env:
          SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}
      
      - name: Run Snyk Code Analysis
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high --file=package.json
      
      - name: SonarQube Scan
        uses: sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          scanMetadataReportFile: target/sonar/report-task.txt
      
      - name: Upload SAST Results
        uses: actions/upload-artifact@v3
        with:
          name: sast-results
          path: |
            eslint-security.json
            semgrep-results.json
            snyk-results.json
            sonar-report.json
      
      - name: Security Gate Check
        run: |
          python scripts/security-gate-check.py \
            --eslint-report eslint-security.json \
            --semgrep-report semgrep-results.json \
            --snyk-report snyk-results.json \
            --fail-on-high-severity
```

## 🌐 Dynamic Application Security Testing (DAST)

### 1. OWASP ZAP Integration

```python
#!/usr/bin/env python3
# OWASP ZAP automated scanning for healthcare platform

import time
import json
from zapv2 import ZAPv2

class HealthcareZAPScanner:
    def __init__(self, target_url, zap_proxy='http://127.0.0.1:8080'):
        self.target_url = target_url
        self.zap = ZAPv2(proxies={'http': zap_proxy, 'https': zap_proxy})
        self.scan_results = {}
        
    def setup_authentication(self, username, password):
        """Setup authentication for healthcare platform"""
        # Configure form-based authentication
        auth_config = {
            'contextName': 'Healthcare Platform',
            'loginUrl': f'{self.target_url}/api/auth/login',
            'loginRequestData': f'email={username}&password={password}',
            'usernameParameter': 'email',
            'passwordParameter': 'password'
        }
        
        # Create authentication context
        context_id = self.zap.context.new_context(auth_config['contextName'])
        
        # Set authentication method
        self.zap.authentication.set_authentication_method(
            context_id,
            'formBasedAuthentication',
            f"loginUrl={auth_config['loginUrl']}&"
            f"loginRequestData={auth_config['loginRequestData']}"
        )
        
        # Add user for authentication
        user_id = self.zap.users.new_user(context_id, 'healthcare-user')
        self.zap.users.set_authentication_credentials(
            context_id, user_id,
            f"username={username}&password={password}"
        )
        
        self.zap.users.set_user_enabled(context_id, user_id, True)
        
        return context_id, user_id
    
    def configure_scan_policy(self):
        """Configure scan policy for healthcare security"""
        # Create custom scan policy
        policy_name = 'Healthcare Security Policy'
        
        # Enable all security tests
        scan_policy = {
            'SQL Injection': True,
            'Cross Site Scripting (Persistent)': True,
            'Cross Site Scripting (Reflected)': True,
            'Path Traversal': True,
            'Remote File Inclusion': True,
            'Server Side Include': True,
            'Script Active Scan Rules': True,
            'Server Side Code Injection': True,
            'Remote OS Command Injection': True,
            'External Redirect': True,
            'CRLF Injection': True,
            'Parameter Tampering': True,
            'Generic Padding Oracle': True,
            'Expression Language Injection': True,
            'Insecure HTTP Methods': True,
            'HTTP Parameter Override': True
        }
        
        # Configure healthcare-specific tests
        healthcare_tests = {
            'Sensitive Information Disclosure': True,
            'Session Management': True,
            'Authentication Bypass': True,
            'Authorization Bypass': True,
            'Weak Authentication Method': True
        }
        
        scan_policy.update(healthcare_tests)
        
        return policy_name
    
    def run_spider_scan(self, context_id, user_id):
        """Run spider scan to discover URLs"""
        print("Starting spider scan...")
        
        # Start spider scan
        scan_id = self.zap.spider.scan_as_user(
            context_id, user_id, self.target_url,
            maxchildren=10, recurse=True
        )
        
        # Wait for spider to complete
        while int(self.zap.spider.status(scan_id)) < 100:
            print(f"Spider progress: {self.zap.spider.status(scan_id)}%")
            time.sleep(5)
        
        print("Spider scan completed")
        
        # Get discovered URLs
        urls = self.zap.spider.results(scan_id)
        return urls
    
    def run_active_scan(self, context_id, user_id):
        """Run active security scan"""
        print("Starting active scan...")
        
        # Configure scan policy
        policy_name = self.configure_scan_policy()
        
        # Start active scan
        scan_id = self.zap.ascan.scan_as_user(
            self.target_url, context_id, user_id,
            recurse=True, scanpolicyname=policy_name
        )
        
        # Wait for scan to complete
        while int(self.zap.ascan.status(scan_id)) < 100:
            print(f"Active scan progress: {self.zap.ascan.status(scan_id)}%")
            time.sleep(10)
        
        print("Active scan completed")
        return scan_id
    
    def run_passive_scan(self):
        """Run passive scan"""
        print("Starting passive scan...")
        
        # Enable all passive scan rules
        self.zap.pscan.enable_all_scanners()
        
        # Wait for passive scan to complete
        while int(self.zap.pscan.records_to_scan) > 0:
            print(f"Passive scan records remaining: {self.zap.pscan.records_to_scan}")
            time.sleep(5)
        
        print("Passive scan completed")
    
    def generate_report(self):
        """Generate comprehensive security report"""
        # Get all alerts
        alerts = self.zap.core.alerts()
        
        # Categorize alerts by severity
        critical_alerts = []
        high_alerts = []
        medium_alerts = []
        low_alerts = []
        
        for alert in alerts:
            severity = alert['risk']
            if severity == 'High':
                high_alerts.append(alert)
            elif severity == 'Medium':
                medium_alerts.append(alert)
            elif severity == 'Low':
                low_alerts.append(alert)
        
        # Generate healthcare-specific report
        report = {
            'scan_target': self.target_url,
            'scan_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'summary': {
                'total_alerts': len(alerts),
                'high_risk': len(high_alerts),
                'medium_risk': len(medium_alerts),
                'low_risk': len(low_alerts)
            },
            'healthcare_compliance': self.check_healthcare_compliance(alerts),
            'alerts': {
                'high': high_alerts,
                'medium': medium_alerts,
                'low': low_alerts
            }
        }
        
        return report
    
    def check_healthcare_compliance(self, alerts):
        """Check alerts against healthcare compliance requirements"""
        compliance_issues = {
            'hipaa_violations': [],
            'pci_dss_violations': [],
            'data_exposure_risks': []
        }
        
        for alert in alerts:
            alert_name = alert['alert'].lower()
            
            # Check for HIPAA-related issues
            if any(keyword in alert_name for keyword in ['sql injection', 'xss', 'authentication']):
                compliance_issues['hipaa_violations'].append({
                    'alert': alert['alert'],
                    'description': alert['desc'],
                    'url': alert['url']
                })
            
            # Check for PCI DSS-related issues
            if any(keyword in alert_name for keyword in ['payment', 'card', 'ssl', 'tls']):
                compliance_issues['pci_dss_violations'].append({
                    'alert': alert['alert'],
                    'description': alert['desc'],
                    'url': alert['url']
                })
            
            # Check for data exposure risks
            if any(keyword in alert_name for keyword in ['information disclosure', 'directory browsing']):
                compliance_issues['data_exposure_risks'].append({
                    'alert': alert['alert'],
                    'description': alert['desc'],
                    'url': alert['url']
                })
        
        return compliance_issues
    
    def run_full_scan(self, username, password):
        """Run complete DAST scan"""
        try:
            # Setup authentication
            context_id, user_id = self.setup_authentication(username, password)
            
            # Run spider scan
            urls = self.run_spider_scan(context_id, user_id)
            
            # Run passive scan
            self.run_passive_scan()
            
            # Run active scan
            scan_id = self.run_active_scan(context_id, user_id)
            
            # Generate report
            report = self.generate_report()
            
            # Save report
            with open('healthcare-dast-report.json', 'w') as f:
                json.dump(report, f, indent=2)
            
            print("DAST scan completed. Report saved to healthcare-dast-report.json")
            return report
            
        except Exception as e:
            print(f"DAST scan failed: {str(e)}")
            raise

if __name__ == "__main__":
    scanner = HealthcareZAPScanner('https://healthcare-platform.com')
    report = scanner.run_full_scan('<EMAIL>', 'testpassword')
    
    # Print summary
    print(f"\nScan Summary:")
    print(f"Total Alerts: {report['summary']['total_alerts']}")
    print(f"High Risk: {report['summary']['high_risk']}")
    print(f"Medium Risk: {report['summary']['medium_risk']}")
    print(f"Low Risk: {report['summary']['low_risk']}")
```

## 📚 Best Practices Summary

### Security Testing Strategy
1. **Shift Left**: Integrate security testing early in development
2. **Comprehensive Coverage**: Use multiple testing methodologies (SAST, DAST, IAST)
3. **Automation**: Automate security tests in CI/CD pipelines
4. **Risk-Based**: Prioritize testing based on risk assessment
5. **Continuous**: Implement continuous security testing

### Tool Integration
1. **Multiple Tools**: Use multiple tools for comprehensive coverage
2. **False Positive Management**: Implement processes to manage false positives
3. **Custom Rules**: Create custom rules for healthcare-specific vulnerabilities
4. **Reporting**: Generate actionable security reports
5. **Metrics**: Track security testing metrics and trends

### Compliance Testing
1. **Regulatory Requirements**: Test against healthcare regulations (HIPAA, HITECH)
2. **Industry Standards**: Validate against industry standards (OWASP, NIST)
3. **Documentation**: Maintain comprehensive testing documentation
4. **Regular Updates**: Keep testing tools and rules updated
5. **Training**: Train development teams on security testing

## 🔗 Additional Resources

- [OWASP Testing Guide](https://owasp.org/www-project-web-security-testing-guide/)
- [NIST SP 800-115 Technical Guide to Information Security Testing](https://csrc.nist.gov/publications/detail/sp/800-115/final)
- [SANS Application Security Testing](https://www.sans.org/white-papers/application-security-testing/)
- [Healthcare Security Testing Guidelines](../23-healthcare-security.md)

---

**Next**: [Secure CI/CD](15-secure-cicd.md) | **Previous**: [DevSecOps Fundamentals](13-devsecops-fundamentals.md)

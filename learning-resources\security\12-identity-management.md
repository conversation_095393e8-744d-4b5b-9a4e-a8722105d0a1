# Identity Management

## Overview

Identity Management (IdM) is crucial for healthcare platforms to securely manage user identities, access rights, and compliance requirements. This guide covers comprehensive identity management strategies including Single Sign-On (SSO), identity providers, user lifecycle management, and healthcare-specific compliance considerations.

## 🎯 Learning Objectives

By the end of this module, you will understand:
- Identity and Access Management (IAM) fundamentals
- Single Sign-On (SSO) implementation
- Identity provider integration (SAML, OIDC)
- User lifecycle management
- Role-based and attribute-based access control
- Healthcare identity compliance (HIPAA, HITECH)

## 🆔 Identity Management Fundamentals

### 1. IAM Architecture for Healthcare

```javascript
// Comprehensive IAM system for healthcare platform
class HealthcareIAM {
  constructor() {
    this.identityProviders = new Map();
    this.roleManager = new RoleManager();
    this.attributeManager = new AttributeManager();
    this.auditLogger = new AuditLogger();
  }

  // User identity lifecycle management
  async createUser(userData, createdBy) {
    try {
      // Validate user data
      const validation = await this.validateUserData(userData);
      if (!validation.valid) {
        throw new Error(`Invalid user data: ${validation.errors.join(', ')}`);
      }

      // Create user identity
      const user = await User.create({
        id: this.generateUserId(),
        email: userData.email.toLowerCase(),
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: userData.role,
        department: userData.department,
        licenseNumber: userData.licenseNumber, // For healthcare professionals
        npi: userData.npi, // National Provider Identifier
        status: 'PENDING_ACTIVATION',
        createdAt: new Date(),
        createdBy,
        lastModified: new Date(),
        passwordChangeRequired: true,
        mfaRequired: true
      });

      // Assign default role permissions
      await this.assignDefaultPermissions(user.id, user.role);

      // Create audit trail
      await this.auditLogger.log({
        action: 'USER_CREATED',
        userId: user.id,
        performedBy: createdBy,
        details: {
          email: user.email,
          role: user.role,
          department: user.department
        },
        timestamp: new Date()
      });

      // Send activation email
      await this.sendActivationEmail(user);

      return {
        success: true,
        userId: user.id,
        activationRequired: true
      };

    } catch (error) {
      await this.auditLogger.log({
        action: 'USER_CREATION_FAILED',
        performedBy: createdBy,
        error: error.message,
        timestamp: new Date()
      });
      throw error;
    }
  }

  // User activation process
  async activateUser(activationToken, initialPassword) {
    try {
      const activation = await UserActivation.findOne({
        token: activationToken,
        expiresAt: { $gt: new Date() },
        used: false
      });

      if (!activation) {
        throw new Error('Invalid or expired activation token');
      }

      const user = await User.findById(activation.userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Validate initial password
      const passwordValidation = await this.validatePassword(initialPassword, user);
      if (!passwordValidation.valid) {
        throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
      }

      // Set password and activate user
      const passwordHash = await this.hashPassword(initialPassword);
      await User.updateOne(
        { _id: user._id },
        {
          passwordHash,
          status: 'ACTIVE',
          activatedAt: new Date(),
          passwordChangeRequired: false
        }
      );

      // Mark activation token as used
      await UserActivation.updateOne(
        { _id: activation._id },
        { used: true, usedAt: new Date() }
      );

      await this.auditLogger.log({
        action: 'USER_ACTIVATED',
        userId: user.id,
        timestamp: new Date()
      });

      return { success: true, userId: user.id };

    } catch (error) {
      await this.auditLogger.log({
        action: 'USER_ACTIVATION_FAILED',
        error: error.message,
        timestamp: new Date()
      });
      throw error;
    }
  }

  // Role and permission management
  async assignRole(userId, roleId, assignedBy) {
    try {
      const user = await User.findById(userId);
      const role = await Role.findById(roleId);

      if (!user || !role) {
        throw new Error('User or role not found');
      }

      // Check if assigner has permission to assign this role
      const canAssign = await this.canAssignRole(assignedBy, roleId);
      if (!canAssign) {
        throw new Error('Insufficient permissions to assign this role');
      }

      // Create role assignment
      await UserRole.create({
        userId,
        roleId,
        assignedBy,
        assignedAt: new Date(),
        status: 'ACTIVE'
      });

      // Update user's primary role if this is their first role
      if (!user.primaryRole) {
        await User.updateOne(
          { _id: userId },
          { primaryRole: roleId }
        );
      }

      await this.auditLogger.log({
        action: 'ROLE_ASSIGNED',
        userId,
        performedBy: assignedBy,
        details: { roleId, roleName: role.name },
        timestamp: new Date()
      });

      return { success: true };

    } catch (error) {
      await this.auditLogger.log({
        action: 'ROLE_ASSIGNMENT_FAILED',
        userId,
        performedBy: assignedBy,
        error: error.message,
        timestamp: new Date()
      });
      throw error;
    }
  }

  // Get user permissions
  async getUserPermissions(userId) {
    try {
      // Get user's roles
      const userRoles = await UserRole.find({
        userId,
        status: 'ACTIVE'
      }).populate('roleId');

      // Get permissions from roles
      const permissions = new Set();
      for (const userRole of userRoles) {
        const rolePermissions = await RolePermission.find({
          roleId: userRole.roleId._id,
          status: 'ACTIVE'
        }).populate('permissionId');

        rolePermissions.forEach(rp => {
          permissions.add({
            id: rp.permissionId._id,
            name: rp.permissionId.name,
            resource: rp.permissionId.resource,
            action: rp.permissionId.action,
            scope: rp.scope || 'global'
          });
        });
      }

      // Get direct user permissions
      const directPermissions = await UserPermission.find({
        userId,
        status: 'ACTIVE'
      }).populate('permissionId');

      directPermissions.forEach(up => {
        permissions.add({
          id: up.permissionId._id,
          name: up.permissionId.name,
          resource: up.permissionId.resource,
          action: up.permissionId.action,
          scope: up.scope || 'global'
        });
      });

      return Array.from(permissions);

    } catch (error) {
      throw new Error(`Failed to get user permissions: ${error.message}`);
    }
  }

  // User deactivation
  async deactivateUser(userId, deactivatedBy, reason) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Deactivate user
      await User.updateOne(
        { _id: userId },
        {
          status: 'DEACTIVATED',
          deactivatedAt: new Date(),
          deactivatedBy,
          deactivationReason: reason
        }
      );

      // Terminate all active sessions
      await this.terminateAllUserSessions(userId);

      // Deactivate all role assignments
      await UserRole.updateMany(
        { userId, status: 'ACTIVE' },
        { status: 'DEACTIVATED', deactivatedAt: new Date() }
      );

      await this.auditLogger.log({
        action: 'USER_DEACTIVATED',
        userId,
        performedBy: deactivatedBy,
        details: { reason },
        timestamp: new Date()
      });

      return { success: true };

    } catch (error) {
      await this.auditLogger.log({
        action: 'USER_DEACTIVATION_FAILED',
        userId,
        performedBy: deactivatedBy,
        error: error.message,
        timestamp: new Date()
      });
      throw error;
    }
  }

  generateUserId() {
    return `usr_${crypto.randomBytes(16).toString('hex')}`;
  }

  async validateUserData(userData) {
    const errors = [];

    // Email validation
    if (!userData.email || !this.isValidEmail(userData.email)) {
      errors.push('Valid email is required');
    }

    // Check email uniqueness
    const existingUser = await User.findOne({ email: userData.email.toLowerCase() });
    if (existingUser) {
      errors.push('Email already exists');
    }

    // Name validation
    if (!userData.firstName || userData.firstName.length < 2) {
      errors.push('First name must be at least 2 characters');
    }

    if (!userData.lastName || userData.lastName.length < 2) {
      errors.push('Last name must be at least 2 characters');
    }

    // Role validation
    const validRoles = await Role.find({ status: 'ACTIVE' });
    if (!validRoles.some(role => role.id === userData.role)) {
      errors.push('Invalid role specified');
    }

    // Healthcare-specific validations
    if (userData.role === 'DOCTOR' || userData.role === 'NURSE') {
      if (!userData.licenseNumber) {
        errors.push('License number is required for healthcare professionals');
      }
    }

    if (userData.role === 'DOCTOR' && !userData.npi) {
      errors.push('NPI is required for doctors');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
```

### 2. Single Sign-On (SSO) Implementation

```javascript
// SAML SSO implementation for healthcare platform
class SAMLSSOProvider {
  constructor() {
    this.saml = require('samlify');
    this.identityProvider = null;
    this.serviceProvider = null;
    this.initializeSAML();
  }

  initializeSAML() {
    // Configure Identity Provider
    this.identityProvider = this.saml.IdentityProvider({
      metadata: fs.readFileSync('./config/idp-metadata.xml'),
      isAssertionEncrypted: true,
      messageSigningOrder: 'encrypt-then-sign'
    });

    // Configure Service Provider (our healthcare platform)
    this.serviceProvider = this.saml.ServiceProvider({
      entityID: 'https://healthcare-platform.com',
      authnRequestsSigned: true,
      wantAssertionsSigned: true,
      wantMessageSigned: true,
      wantLogoutResponseSigned: true,
      wantLogoutRequestSigned: true,
      signatureConfig: {
        prefix: 'ds',
        location: { reference: '//*[local-name(.)="Issuer"]', action: 'after' }
      },
      assertionConsumerService: [{
        Binding: 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST',
        Location: 'https://healthcare-platform.com/sso/acs'
      }],
      singleLogoutService: [{
        Binding: 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect',
        Location: 'https://healthcare-platform.com/sso/sls'
      }]
    });
  }

  // Initiate SSO login
  async initiateLogin(req, res) {
    try {
      const { context } = this.serviceProvider.createLoginRequest(
        this.identityProvider,
        'redirect'
      );

      // Store relay state for post-login redirect
      const relayState = req.query.returnUrl || '/dashboard';
      req.session.relayState = relayState;

      return res.redirect(context);

    } catch (error) {
      console.error('SSO login initiation failed:', error);
      return res.status(500).json({ error: 'SSO login failed' });
    }
  }

  // Handle SSO response
  async handleSSOResponse(req, res) {
    try {
      const { extract } = await this.serviceProvider.parseLoginResponse(
        this.identityProvider,
        'post',
        req
      );

      // Extract user information from SAML assertion
      const userInfo = this.extractUserInfo(extract);

      // Find or create user
      let user = await User.findOne({ email: userInfo.email });
      if (!user) {
        user = await this.createUserFromSSO(userInfo);
      } else {
        // Update user information from SSO
        await this.updateUserFromSSO(user, userInfo);
      }

      // Create session
      const sessionId = await this.createSession(user.id, req);

      // Set session cookie
      res.cookie('sessionId', sessionId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 30 * 60 * 1000 // 30 minutes
      });

      // Redirect to original destination
      const returnUrl = req.session.relayState || '/dashboard';
      delete req.session.relayState;

      return res.redirect(returnUrl);

    } catch (error) {
      console.error('SSO response handling failed:', error);
      return res.status(400).json({ error: 'Invalid SSO response' });
    }
  }

  // Initiate SSO logout
  async initiateLogout(req, res) {
    try {
      const user = req.user;
      const sessionId = req.sessionId;

      // Terminate local session
      await this.terminateSession(sessionId);

      // Create logout request
      const { context } = this.serviceProvider.createLogoutRequest(
        this.identityProvider,
        'redirect',
        user
      );

      return res.redirect(context);

    } catch (error) {
      console.error('SSO logout initiation failed:', error);
      return res.status(500).json({ error: 'SSO logout failed' });
    }
  }

  extractUserInfo(samlResponse) {
    const attributes = samlResponse.attributes;
    
    return {
      email: attributes['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'],
      firstName: attributes['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname'],
      lastName: attributes['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname'],
      department: attributes['http://schemas.healthcare.com/claims/department'],
      role: attributes['http://schemas.healthcare.com/claims/role'],
      licenseNumber: attributes['http://schemas.healthcare.com/claims/license'],
      npi: attributes['http://schemas.healthcare.com/claims/npi']
    };
  }

  async createUserFromSSO(userInfo) {
    const user = await User.create({
      id: this.generateUserId(),
      email: userInfo.email.toLowerCase(),
      firstName: userInfo.firstName,
      lastName: userInfo.lastName,
      role: userInfo.role,
      department: userInfo.department,
      licenseNumber: userInfo.licenseNumber,
      npi: userInfo.npi,
      status: 'ACTIVE',
      authMethod: 'SSO',
      createdAt: new Date(),
      lastLogin: new Date()
    });

    // Assign default permissions based on role
    await this.assignDefaultPermissions(user.id, user.role);

    return user;
  }

  async updateUserFromSSO(user, userInfo) {
    await User.updateOne(
      { _id: user._id },
      {
        firstName: userInfo.firstName,
        lastName: userInfo.lastName,
        department: userInfo.department,
        licenseNumber: userInfo.licenseNumber,
        npi: userInfo.npi,
        lastLogin: new Date(),
        lastModified: new Date()
      }
    );
  }
}
```

### 3. OpenID Connect (OIDC) Implementation

```javascript
// OpenID Connect implementation for healthcare platform
class OIDCProvider {
  constructor() {
    this.issuer = 'https://healthcare-platform.com';
    this.clients = new Map();
    this.initializeOIDC();
  }

  initializeOIDC() {
    const { Provider } = require('oidc-provider');

    const configuration = {
      clients: [
        {
          client_id: 'healthcare-mobile-app',
          client_secret: process.env.MOBILE_APP_CLIENT_SECRET,
          redirect_uris: ['com.healthcare.app://callback'],
          grant_types: ['authorization_code', 'refresh_token'],
          response_types: ['code'],
          scope: 'openid profile email healthcare:read healthcare:write'
        },
        {
          client_id: 'healthcare-web-app',
          client_secret: process.env.WEB_APP_CLIENT_SECRET,
          redirect_uris: ['https://healthcare-platform.com/callback'],
          grant_types: ['authorization_code', 'refresh_token'],
          response_types: ['code'],
          scope: 'openid profile email healthcare:admin'
        }
      ],
      
      scopes: [
        'openid',
        'profile',
        'email',
        'healthcare:read',
        'healthcare:write',
        'healthcare:admin'
      ],

      claims: {
        openid: ['sub'],
        profile: ['name', 'family_name', 'given_name', 'role', 'department'],
        email: ['email', 'email_verified'],
        healthcare: ['license_number', 'npi', 'specialization']
      },

      features: {
        devInteractions: { enabled: false },
        deviceFlow: { enabled: true },
        introspection: { enabled: true },
        revocation: { enabled: true },
        pkce: { required: true }
      },

      ttl: {
        AccessToken: 60 * 60, // 1 hour
        AuthorizationCode: 10 * 60, // 10 minutes
        IdToken: 60 * 60, // 1 hour
        RefreshToken: 24 * 60 * 60 // 24 hours
      }
    };

    this.provider = new Provider(this.issuer, configuration);
    this.setupCallbacks();
  }

  setupCallbacks() {
    // Account lookup callback
    this.provider.Account.findAccount = async (ctx, sub) => {
      const user = await User.findById(sub);
      if (!user || user.status !== 'ACTIVE') {
        return undefined;
      }

      return {
        accountId: sub,
        async claims(use, scope) {
          const claims = { sub };

          if (scope.includes('profile')) {
            claims.name = `${user.firstName} ${user.lastName}`;
            claims.family_name = user.lastName;
            claims.given_name = user.firstName;
            claims.role = user.role;
            claims.department = user.department;
          }

          if (scope.includes('email')) {
            claims.email = user.email;
            claims.email_verified = user.emailVerified;
          }

          if (scope.includes('healthcare')) {
            claims.license_number = user.licenseNumber;
            claims.npi = user.npi;
            claims.specialization = user.specialization;
          }

          return claims;
        }
      };
    };

    // Authentication callback
    this.provider.on('authorization.accepted', async (ctx) => {
      const { oidc } = ctx;
      
      await this.auditLogger.log({
        action: 'OIDC_AUTHORIZATION_GRANTED',
        userId: oidc.session.accountId,
        clientId: oidc.client.clientId,
        scope: oidc.params.scope,
        timestamp: new Date()
      });
    });

    // Token issuance callback
    this.provider.on('access_token.issued', async (ctx, token) => {
      await this.auditLogger.log({
        action: 'ACCESS_TOKEN_ISSUED',
        userId: token.accountId,
        clientId: token.clientId,
        scope: token.scope,
        jti: token.jti,
        timestamp: new Date()
      });
    });
  }

  // Custom login interaction
  async handleLogin(req, res) {
    try {
      const { uid, prompt, params } = await this.provider.interactionDetails(req, res);
      
      if (prompt.name === 'login') {
        return res.render('login', {
          uid,
          details: prompt.details,
          params,
          title: 'Healthcare Platform Login'
        });
      }

      if (prompt.name === 'consent') {
        return res.render('consent', {
          uid,
          details: prompt.details,
          params,
          title: 'Authorize Application'
        });
      }

    } catch (error) {
      console.error('OIDC interaction error:', error);
      return res.status(500).json({ error: 'Authentication error' });
    }
  }

  // Process login submission
  async processLogin(req, res) {
    try {
      const { uid } = await this.provider.interactionDetails(req, res);
      const { email, password, mfaToken } = req.body;

      // Authenticate user
      const authResult = await this.authenticateUser(email, password, mfaToken);
      if (!authResult.success) {
        return res.render('login', {
          uid,
          error: 'Invalid credentials',
          email
        });
      }

      // Complete interaction
      const result = {
        login: {
          accountId: authResult.user.id,
          remember: false
        }
      };

      await this.provider.interactionFinished(req, res, result, {
        mergeWithLastSubmission: false
      });

    } catch (error) {
      console.error('OIDC login processing error:', error);
      return res.status(500).json({ error: 'Login processing failed' });
    }
  }

  async authenticateUser(email, password, mfaToken) {
    try {
      const user = await User.findOne({ 
        email: email.toLowerCase(),
        status: 'ACTIVE'
      });

      if (!user) {
        return { success: false, error: 'User not found' };
      }

      // Verify password
      const passwordValid = await bcrypt.compare(password, user.passwordHash);
      if (!passwordValid) {
        return { success: false, error: 'Invalid password' };
      }

      // Verify MFA if required
      if (user.mfaRequired) {
        const mfaValid = await this.verifyMFA(user.id, mfaToken);
        if (!mfaValid) {
          return { success: false, error: 'Invalid MFA token' };
        }
      }

      return { success: true, user };

    } catch (error) {
      return { success: false, error: 'Authentication failed' };
    }
  }
}
```

## 📚 Best Practices Summary

### Identity Lifecycle Management
1. **Automated Provisioning**: Implement automated user provisioning and deprovisioning
2. **Role-Based Access**: Use role-based access control with healthcare-specific roles
3. **Regular Reviews**: Conduct regular access reviews and certifications
4. **Audit Trails**: Maintain comprehensive audit trails for all identity operations
5. **Compliance**: Ensure compliance with healthcare regulations (HIPAA, HITECH)

### Single Sign-On (SSO)
1. **Standards Compliance**: Use standard protocols (SAML, OIDC)
2. **Secure Configuration**: Implement proper encryption and signing
3. **Session Management**: Implement secure session management across applications
4. **Fallback Mechanisms**: Provide fallback authentication methods
5. **Monitoring**: Monitor SSO activities for security anomalies

### Access Control
1. **Principle of Least Privilege**: Grant minimum necessary access
2. **Attribute-Based Control**: Implement attribute-based access control for complex scenarios
3. **Dynamic Authorization**: Use dynamic authorization based on context
4. **Regular Certification**: Regularly certify user access rights
5. **Emergency Access**: Implement emergency access procedures

## 🔗 Additional Resources

- [NIST Digital Identity Guidelines](https://pages.nist.gov/800-63-3/)
- [OWASP Identity Management Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Identity_Management_Cheat_Sheet.html)
- [SAML Security Best Practices](https://docs.oasis-open.org/security/saml/Post2.0/sstc-saml-tech-overview-2.0.html)
- [OpenID Connect Specification](https://openid.net/connect/)
- [Healthcare Identity Management Guidelines](../23-healthcare-security.md)

---

**Next**: [DevSecOps Fundamentals](13-devsecops-fundamentals.md) | **Previous**: [Modern Authentication Patterns](11-modern-auth-patterns.md)

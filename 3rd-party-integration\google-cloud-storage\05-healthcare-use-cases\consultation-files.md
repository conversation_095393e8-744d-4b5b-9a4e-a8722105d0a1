# Consultation Files Management

## Overview

This guide covers the implementation of file management for telemedicine consultations, including document uploads, secure sharing, and integration with your healthcare platform's consultation workflow.

## Consultation File Architecture

### File Organization Structure

```
consultation-files/
├── active/
│   ├── consultation-{id}/
│   │   ├── pre-consultation/
│   │   │   ├── patient-forms/
│   │   │   ├── medical-history/
│   │   │   └── insurance-docs/
│   │   ├── during-consultation/
│   │   │   ├── recordings/
│   │   │   ├── screenshots/
│   │   │   └── shared-docs/
│   │   └── post-consultation/
│   │       ├── prescriptions/
│   │       ├── follow-up-notes/
│   │       └── lab-orders/
├── completed/
│   └── {year}/{month}/consultation-{id}/
└── archived/
    └── {year}/consultation-{id}/
```

### Metadata Schema

```python
consultation_metadata = {
    'consultation_id': 'cons_12345',
    'patient_id': 'patient_67890',
    'doctor_id': 'dr_smith_001',
    'consultation_date': '2024-06-17T14:30:00Z',
    'consultation_type': 'follow_up',  # initial, follow_up, emergency
    'status': 'active',  # scheduled, active, completed, cancelled
    'file_category': 'pre_consultation',  # pre, during, post
    'file_type': 'patient_form',
    'hipaa_compliant': True,
    'retention_period': '7_years',
    'access_level': 'doctor_patient_only'
}
```

## Implementation

### 1. Consultation File Manager

```python
from google.cloud import storage
from datetime import datetime, timedelta
import json
import uuid

class ConsultationFileManager:
    def __init__(self, project_id, bucket_name):
        self.client = storage.Client(project=project_id)
        self.bucket = self.client.bucket(bucket_name)
        
        # File type configurations
        self.allowed_file_types = {
            'patient_forms': ['.pdf', '.doc', '.docx', '.jpg', '.png'],
            'medical_records': ['.pdf', '.doc', '.docx', '.jpg', '.png', '.dcm'],
            'prescriptions': ['.pdf', '.doc', '.docx'],
            'recordings': ['.mp4', '.webm', '.mp3', '.wav'],
            'images': ['.jpg', '.jpeg', '.png', '.gif', '.bmp'],
            'documents': ['.pdf', '.doc', '.docx', '.txt']
        }
        
        self.max_file_sizes = {
            'patient_forms': 10 * 1024 * 1024,  # 10MB
            'medical_records': 50 * 1024 * 1024,  # 50MB
            'prescriptions': 5 * 1024 * 1024,  # 5MB
            'recordings': 500 * 1024 * 1024,  # 500MB
            'images': 20 * 1024 * 1024,  # 20MB
            'documents': 25 * 1024 * 1024  # 25MB
        }

    def create_consultation_workspace(self, consultation_id, patient_id, doctor_id):
        """Create organized workspace for consultation files"""
        
        workspace_structure = [
            f"active/consultation-{consultation_id}/pre-consultation/patient-forms/",
            f"active/consultation-{consultation_id}/pre-consultation/medical-history/",
            f"active/consultation-{consultation_id}/pre-consultation/insurance-docs/",
            f"active/consultation-{consultation_id}/during-consultation/recordings/",
            f"active/consultation-{consultation_id}/during-consultation/screenshots/",
            f"active/consultation-{consultation_id}/during-consultation/shared-docs/",
            f"active/consultation-{consultation_id}/post-consultation/prescriptions/",
            f"active/consultation-{consultation_id}/post-consultation/follow-up-notes/",
            f"active/consultation-{consultation_id}/post-consultation/lab-orders/"
        ]
        
        # Create placeholder files to establish folder structure
        for folder_path in workspace_structure:
            placeholder_blob = self.bucket.blob(f"{folder_path}.placeholder")
            placeholder_blob.metadata = {
                'consultation_id': consultation_id,
                'patient_id': patient_id,
                'doctor_id': doctor_id,
                'created_at': datetime.now().isoformat(),
                'folder_type': folder_path.split('/')[-2]
            }
            placeholder_blob.upload_from_string("", content_type='text/plain')
        
        return {
            'success': True,
            'consultation_id': consultation_id,
            'workspace_created': True,
            'folders': workspace_structure
        }

    def upload_consultation_file(self, consultation_id, file_path, file_category, 
                               file_type, uploaded_by, local_file_path):
        """Upload file to consultation workspace with validation"""
        
        try:
            # Validate file
            validation_result = self._validate_consultation_file(
                local_file_path, file_type
            )
            if not validation_result['valid']:
                return validation_result
            
            # Generate blob path
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_extension = os.path.splitext(local_file_path)[1]
            blob_name = f"active/consultation-{consultation_id}/{file_category}/{file_type}/{timestamp}_{uuid.uuid4().hex[:8]}{file_extension}"
            
            blob = self.bucket.blob(blob_name)
            
            # Set metadata
            blob.metadata = {
                'consultation_id': consultation_id,
                'file_category': file_category,
                'file_type': file_type,
                'uploaded_by': uploaded_by,
                'upload_timestamp': datetime.now().isoformat(),
                'original_filename': os.path.basename(local_file_path),
                'file_size': str(os.path.getsize(local_file_path)),
                'hipaa_compliant': 'true',
                'access_level': 'consultation_participants'
            }
            
            # Upload file
            blob.upload_from_filename(local_file_path)
            
            # Log upload activity
            self._log_consultation_activity(
                consultation_id, 'file_uploaded', {
                    'blob_name': blob_name,
                    'uploaded_by': uploaded_by,
                    'file_type': file_type
                }
            )
            
            return {
                'success': True,
                'blob_name': blob_name,
                'file_url': None,  # Use signed URLs for access
                'upload_timestamp': blob.metadata['upload_timestamp']
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Upload failed: {str(e)}"
            }

    def _validate_consultation_file(self, file_path, file_type):
        """Validate consultation file before upload"""
        
        if not os.path.exists(file_path):
            return {'valid': False, 'error': 'File not found'}
        
        # Check file extension
        file_extension = os.path.splitext(file_path)[1].lower()
        if file_extension not in self.allowed_file_types.get(file_type, []):
            return {
                'valid': False, 
                'error': f'File type {file_extension} not allowed for {file_type}'
            }
        
        # Check file size
        file_size = os.path.getsize(file_path)
        max_size = self.max_file_sizes.get(file_type, 10 * 1024 * 1024)
        if file_size > max_size:
            return {
                'valid': False,
                'error': f'File size {file_size} exceeds maximum {max_size} bytes'
            }
        
        # Additional security checks
        if file_extension in ['.exe', '.bat', '.sh', '.ps1']:
            return {'valid': False, 'error': 'Executable files not allowed'}
        
        return {'valid': True}

    def generate_consultation_file_access(self, consultation_id, blob_name, 
                                        user_id, user_role, expiration_hours=2):
        """Generate secure access URL for consultation files"""
        
        try:
            blob = self.bucket.blob(blob_name)
            
            if not blob.exists():
                return {'success': False, 'error': 'File not found'}
            
            # Verify user has access to this consultation
            access_granted = self._verify_consultation_access(
                consultation_id, user_id, user_role
            )
            
            if not access_granted:
                return {'success': False, 'error': 'Access denied'}
            
            # Generate signed URL
            expiration = datetime.utcnow() + timedelta(hours=expiration_hours)
            signed_url = blob.generate_signed_url(
                version="v4",
                expiration=expiration,
                method="GET"
            )
            
            # Log access
            self._log_consultation_activity(
                consultation_id, 'file_accessed', {
                    'blob_name': blob_name,
                    'accessed_by': user_id,
                    'user_role': user_role
                }
            )
            
            return {
                'success': True,
                'signed_url': signed_url,
                'expires_at': expiration.isoformat(),
                'blob_name': blob_name
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Access generation failed: {str(e)}"
            }

    def _verify_consultation_access(self, consultation_id, user_id, user_role):
        """Verify user has access to consultation files"""
        
        # In a real implementation, this would check your database
        # For now, we'll implement basic role-based access
        
        allowed_roles = ['doctor', 'patient', 'nurse', 'admin']
        if user_role not in allowed_roles:
            return False
        
        # Additional checks could include:
        # - Is user the assigned doctor?
        # - Is user the patient?
        # - Is user part of the care team?
        # - Is consultation still active?
        
        return True

    def list_consultation_files(self, consultation_id, file_category=None, user_role='doctor'):
        """List files for a consultation with role-based filtering"""
        
        try:
            prefix = f"active/consultation-{consultation_id}/"
            if file_category:
                prefix += f"{file_category}/"
            
            blobs = self.bucket.list_blobs(prefix=prefix)
            
            files = []
            for blob in blobs:
                if blob.name.endswith('.placeholder'):
                    continue
                
                blob.reload()
                
                # Role-based filtering
                if not self._can_access_file(blob, user_role):
                    continue
                
                file_info = {
                    'name': blob.name,
                    'original_filename': blob.metadata.get('original_filename', ''),
                    'file_type': blob.metadata.get('file_type', ''),
                    'file_category': blob.metadata.get('file_category', ''),
                    'uploaded_by': blob.metadata.get('uploaded_by', ''),
                    'upload_timestamp': blob.metadata.get('upload_timestamp', ''),
                    'size': blob.size,
                    'content_type': blob.content_type
                }
                files.append(file_info)
            
            return {
                'success': True,
                'consultation_id': consultation_id,
                'total_files': len(files),
                'files': files
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"File listing failed: {str(e)}"
            }

    def _can_access_file(self, blob, user_role):
        """Determine if user role can access specific file"""
        
        file_type = blob.metadata.get('file_type', '') if blob.metadata else ''
        
        # Role-based access rules
        access_rules = {
            'doctor': ['*'],  # Doctors can access all files
            'patient': ['patient_forms', 'prescriptions', 'follow_up_notes'],
            'nurse': ['patient_forms', 'medical_records', 'follow_up_notes'],
            'admin': ['*']
        }
        
        allowed_types = access_rules.get(user_role, [])
        return '*' in allowed_types or file_type in allowed_types

    def complete_consultation(self, consultation_id):
        """Move consultation files to completed status"""
        
        try:
            # Get current date for archiving
            current_date = datetime.now()
            year_month = current_date.strftime("%Y/%m")
            
            # List all active consultation files
            active_prefix = f"active/consultation-{consultation_id}/"
            completed_prefix = f"completed/{year_month}/consultation-{consultation_id}/"
            
            blobs = list(self.bucket.list_blobs(prefix=active_prefix))
            moved_files = []
            
            for blob in blobs:
                # Create new blob in completed folder
                new_blob_name = blob.name.replace(active_prefix, completed_prefix)
                new_blob = self.bucket.blob(new_blob_name)
                
                # Copy blob
                new_blob.rewrite(blob)
                
                # Update metadata
                if blob.metadata:
                    updated_metadata = dict(blob.metadata)
                    updated_metadata['status'] = 'completed'
                    updated_metadata['completed_at'] = datetime.now().isoformat()
                    new_blob.metadata = updated_metadata
                    new_blob.patch()
                
                # Delete original
                blob.delete()
                
                moved_files.append({
                    'original': blob.name,
                    'new_location': new_blob_name
                })
            
            # Log completion
            self._log_consultation_activity(
                consultation_id, 'consultation_completed', {
                    'moved_files_count': len(moved_files),
                    'completion_date': datetime.now().isoformat()
                }
            )
            
            return {
                'success': True,
                'consultation_id': consultation_id,
                'moved_files': len(moved_files),
                'new_location': completed_prefix
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Consultation completion failed: {str(e)}"
            }

    def _log_consultation_activity(self, consultation_id, activity_type, details):
        """Log consultation file activities for audit"""
        
        from google.cloud import logging
        
        logging_client = logging.Client()
        logger = logging_client.logger('consultation-file-activity')
        
        logger.log_struct({
            'consultation_id': consultation_id,
            'activity_type': activity_type,
            'details': details,
            'timestamp': datetime.utcnow().isoformat()
        }, severity='INFO')

# Usage Example
consultation_manager = ConsultationFileManager("healthcare-project", "consultation-files")

# Create workspace for new consultation
workspace = consultation_manager.create_consultation_workspace(
    consultation_id="cons_12345",
    patient_id="patient_67890",
    doctor_id="dr_smith_001"
)

# Upload patient form before consultation
upload_result = consultation_manager.upload_consultation_file(
    consultation_id="cons_12345",
    file_path="pre-consultation/patient-forms",
    file_category="pre_consultation",
    file_type="patient_forms",
    uploaded_by="patient_67890",
    local_file_path="/tmp/intake_form.pdf"
)

# Generate access URL for doctor
access_url = consultation_manager.generate_consultation_file_access(
    consultation_id="cons_12345",
    blob_name=upload_result['blob_name'],
    user_id="dr_smith_001",
    user_role="doctor",
    expiration_hours=4
)
```

## Integration with Consultation Platform

### 2. Real-time File Sharing During Consultation

```python
class LiveConsultationFileSharing:
    def __init__(self, consultation_manager):
        self.consultation_manager = consultation_manager
        
    def share_file_during_consultation(self, consultation_id, file_path, 
                                     shared_by, participants):
        """Share file with all consultation participants in real-time"""
        
        # Upload file to during-consultation folder
        upload_result = self.consultation_manager.upload_consultation_file(
            consultation_id=consultation_id,
            file_path="during-consultation/shared-docs",
            file_category="during_consultation",
            file_type="documents",
            uploaded_by=shared_by,
            local_file_path=file_path
        )
        
        if not upload_result['success']:
            return upload_result
        
        # Generate access URLs for all participants
        participant_urls = {}
        for participant in participants:
            access_result = self.consultation_manager.generate_consultation_file_access(
                consultation_id=consultation_id,
                blob_name=upload_result['blob_name'],
                user_id=participant['user_id'],
                user_role=participant['role'],
                expiration_hours=2
            )
            
            if access_result['success']:
                participant_urls[participant['user_id']] = access_result['signed_url']
        
        return {
            'success': True,
            'file_shared': upload_result['blob_name'],
            'participant_urls': participant_urls,
            'shared_by': shared_by
        }

    def record_consultation_session(self, consultation_id, recording_data, 
                                  recording_type='video'):
        """Save consultation recording with proper metadata"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_extension = '.mp4' if recording_type == 'video' else '.mp3'
        
        blob_name = f"active/consultation-{consultation_id}/during-consultation/recordings/{timestamp}_session{file_extension}"
        blob = self.consultation_manager.bucket.blob(blob_name)
        
        # Set recording metadata
        blob.metadata = {
            'consultation_id': consultation_id,
            'recording_type': recording_type,
            'recorded_at': datetime.now().isoformat(),
            'file_category': 'during_consultation',
            'file_type': 'recordings',
            'retention_period': '7_years',
            'access_level': 'doctor_patient_only'
        }
        
        # Upload recording
        blob.upload_from_string(recording_data, content_type=f'{recording_type}/mp4')
        
        return {
            'success': True,
            'recording_saved': blob_name,
            'recording_type': recording_type
        }
```

### 3. Post-Consultation File Management

```python
def generate_consultation_summary(self, consultation_id, doctor_id):
    """Generate and store consultation summary with file references"""
    
    # Get all consultation files
    files_result = self.list_consultation_files(consultation_id)
    
    if not files_result['success']:
        return files_result
    
    # Create summary document
    summary = {
        'consultation_id': consultation_id,
        'summary_generated_at': datetime.now().isoformat(),
        'generated_by': doctor_id,
        'files_summary': {
            'total_files': files_result['total_files'],
            'pre_consultation_files': [],
            'during_consultation_files': [],
            'post_consultation_files': []
        }
    }
    
    # Categorize files
    for file_info in files_result['files']:
        category = file_info['file_category']
        if category == 'pre_consultation':
            summary['files_summary']['pre_consultation_files'].append(file_info)
        elif category == 'during_consultation':
            summary['files_summary']['during_consultation_files'].append(file_info)
        elif category == 'post_consultation':
            summary['files_summary']['post_consultation_files'].append(file_info)
    
    # Save summary
    summary_blob_name = f"active/consultation-{consultation_id}/post-consultation/consultation_summary.json"
    summary_blob = self.bucket.blob(summary_blob_name)
    summary_blob.metadata = {
        'consultation_id': consultation_id,
        'file_type': 'consultation_summary',
        'generated_by': doctor_id,
        'generated_at': datetime.now().isoformat()
    }
    
    summary_blob.upload_from_string(
        json.dumps(summary, indent=2),
        content_type='application/json'
    )
    
    return {
        'success': True,
        'summary_file': summary_blob_name,
        'summary': summary
    }

# Add to ConsultationFileManager class
ConsultationFileManager.generate_consultation_summary = generate_consultation_summary
```

## Security and Compliance Features

### File Access Audit Trail

```python
def generate_file_access_report(self, consultation_id, start_date, end_date):
    """Generate audit report for file access during consultation"""
    
    from google.cloud import logging
    
    logging_client = logging.Client()
    
    # Query audit logs
    filter_str = f'''
        jsonPayload.consultation_id="{consultation_id}"
        AND timestamp >= "{start_date.isoformat()}Z"
        AND timestamp <= "{end_date.isoformat()}Z"
    '''
    
    entries = logging_client.list_entries(filter_=filter_str)
    
    access_events = []
    for entry in entries:
        if hasattr(entry, 'json_payload'):
            access_events.append({
                'timestamp': entry.timestamp.isoformat(),
                'activity_type': entry.json_payload.get('activity_type'),
                'details': entry.json_payload.get('details', {}),
                'user': entry.json_payload.get('details', {}).get('accessed_by')
            })
    
    return {
        'consultation_id': consultation_id,
        'audit_period': {
            'start': start_date.isoformat(),
            'end': end_date.isoformat()
        },
        'total_events': len(access_events),
        'events': access_events
    }
```

## Next Steps

1. **[Medical Records](./medical-records.md)** - Electronic health record management
2. **[Medical Imaging](./medical-imaging.md)** - DICOM and imaging file handling
3. **[Integration Examples](../06-code-examples/integration-examples.md)** - Full platform integration

---

*This consultation file management system provides secure, organized, and compliant file handling for your telemedicine platform.*

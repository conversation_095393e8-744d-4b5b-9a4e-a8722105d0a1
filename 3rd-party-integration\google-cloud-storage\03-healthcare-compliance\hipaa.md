# HIPAA Compliance with Google Cloud Storage

## Overview

The Health Insurance Portability and Accountability Act (HIPAA) requires specific safeguards for Protected Health Information (PHI). Google Cloud Storage provides HIPAA-compliant infrastructure when properly configured and used with a Business Associate Agreement (BAA).

## HIPAA Requirements Overview

### Administrative Safeguards
- **Security Officer**: Designated security responsibility
- **Workforce Training**: HIPAA awareness and procedures
- **Access Management**: User access controls and reviews
- **Contingency Plan**: Data backup and disaster recovery

### Physical Safeguards
- **Facility Access**: Controlled physical access to systems
- **Workstation Use**: Secure workstation configurations
- **Device Controls**: Hardware and media safeguards

### Technical Safeguards
- **Access Control**: Unique user identification and authentication
- **Audit Controls**: Activity logging and monitoring
- **Integrity**: PHI alteration and destruction protection
- **Transmission Security**: End-to-end encryption

## Google Cloud HIPAA Compliance

### Business Associate Agreement (BAA)

Google Cloud offers a BAA that covers GCS usage for PHI.

```bash
# Enable HIPAA compliance for your organization
# 1. Sign BAA with Google Cloud
# 2. Configure HIPAA-compliant services
# 3. Implement required safeguards
```

### HIPAA-Eligible Services
```
✅ Cloud Storage
✅ Cloud SQL
✅ Compute Engine
✅ Cloud Functions
✅ Cloud Logging
✅ Cloud Monitoring
✅ Cloud IAM
```

### Non-HIPAA Services to Avoid
```
❌ Firebase (consumer version)
❌ Cloud Endpoints
❌ Cloud Debugger
❌ Some AI/ML services
```

## Technical Implementation

### 1. Encryption Requirements

#### Encryption at Rest (Required)
```python
from google.cloud import storage
from google.cloud import kms

def create_hipaa_compliant_bucket(project_id, bucket_name, kms_key_name):
    """Create HIPAA-compliant bucket with CMEK encryption"""
    
    client = storage.Client(project=project_id)
    bucket = client.bucket(bucket_name)
    
    # Set location to HIPAA-compliant region
    bucket.location = "us-central1"
    
    # Enable uniform bucket-level access
    bucket.iam_configuration.uniform_bucket_level_access_enabled = True
    
    # Set default encryption key
    bucket.default_kms_key_name = kms_key_name
    
    # Create bucket
    bucket.create()
    
    # Set lifecycle policy for compliance
    bucket.lifecycle_rules = [
        {
            'action': {'type': 'SetStorageClass', 'storageClass': 'NEARLINE'},
            'condition': {'age': 90}
        },
        {
            'action': {'type': 'SetStorageClass', 'storageClass': 'COLDLINE'},
            'condition': {'age': 365}
        },
        {
            'action': {'type': 'SetStorageClass', 'storageClass': 'ARCHIVE'},
            'condition': {'age': 2555}  # 7 years for HIPAA retention
        }
    ]
    
    bucket.patch()
    
    print(f"Created HIPAA-compliant bucket: {bucket_name}")
    return bucket

# Usage
kms_key = "projects/PROJECT_ID/locations/us-central1/keyRings/hipaa-ring/cryptoKeys/phi-key"
bucket = create_hipaa_compliant_bucket("my-project", "phi-storage", kms_key)
```

#### Encryption in Transit (Required)
```python
# All GCS operations use HTTPS by default
# Additional verification for HIPAA compliance

import ssl
import requests

def verify_tls_encryption():
    """Verify TLS encryption for HIPAA compliance"""
    
    # Check TLS version
    context = ssl.create_default_context()
    context.minimum_version = ssl.TLSVersion.TLSv1_2
    
    # Verify certificate
    response = requests.get(
        'https://storage.googleapis.com',
        verify=True,
        timeout=10
    )
    
    print(f"TLS Version: {response.raw.version}")
    print(f"Cipher: {response.raw.cipher}")
    
    return response.status_code == 200

# Verify encryption
verify_tls_encryption()
```

### 2. Access Control Implementation

#### Role-Based Access Control (RBAC)
```python
def setup_hipaa_access_control(project_id, bucket_name):
    """Configure HIPAA-compliant access controls"""
    
    client = storage.Client(project=project_id)
    bucket = client.bucket(bucket_name)
    
    # Healthcare team roles
    healthcare_roles = {
        'doctors': 'roles/storage.objectAdmin',      # Full access to patient files
        'nurses': 'roles/storage.objectViewer',      # Read-only access
        'admins': 'roles/storage.admin',             # Bucket management
        'auditors': 'roles/storage.legacyBucketReader'  # Audit access only
    }
    
    # Configure IAM policies
    policy = bucket.get_iam_policy(requested_policy_version=3)
    
    for role_type, gcp_role in healthcare_roles.items():
        # Add conditional access based on time and location
        policy.bindings.append({
            'role': gcp_role,
            'members': [f'group:{role_type}@healthcare-clinic.com'],
            'condition': {
                'title': f'{role_type.title()} Access Control',
                'description': f'Time and location-based access for {role_type}',
                'expression': '''
                    request.time.getHours() >= 6 && 
                    request.time.getHours() <= 22 &&
                    origin.ip in ["10.0.0.0/8", "***********/16"]
                '''
            }
        })
    
    bucket.set_iam_policy(policy)
    print(f"Configured HIPAA access controls for {bucket_name}")

# Usage
setup_hipaa_access_control("my-project", "phi-storage")
```

#### Minimum Necessary Access
```python
def create_patient_specific_access(bucket_name, patient_id, healthcare_provider_email):
    """Create patient-specific access following minimum necessary principle"""
    
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    
    # Create patient-specific prefix
    patient_prefix = f"patients/{patient_id}/"
    
    # Custom role for patient-specific access
    custom_role = {
        'title': f'Patient {patient_id} Access',
        'description': f'Access only to patient {patient_id} files',
        'stage': 'GA',
        'includedPermissions': [
            'storage.objects.get',
            'storage.objects.list',
            'storage.objects.create',
            'storage.objects.update'
        ]
    }
    
    # Apply conditional IAM policy
    policy = bucket.get_iam_policy(requested_policy_version=3)
    policy.bindings.append({
        'role': 'projects/PROJECT_ID/roles/patientSpecificAccess',
        'members': [f'user:{healthcare_provider_email}'],
        'condition': {
            'title': f'Patient {patient_id} File Access',
            'description': 'Access limited to specific patient files',
            'expression': f'resource.name.startsWith("projects/_/buckets/{bucket_name}/objects/{patient_prefix}")'
        }
    })
    
    bucket.set_iam_policy(policy)
    print(f"Created patient-specific access for {patient_id}")

# Usage
create_patient_specific_access("phi-storage", "patient-12345", "<EMAIL>")
```

### 3. Audit Logging Configuration

#### Comprehensive Audit Logging
```yaml
# Cloud Logging configuration for HIPAA audit requirements
auditConfigs:
  - service: storage.googleapis.com
    auditLogConfigs:
      - logType: ADMIN_READ
        exemptedMembers: []
      - logType: DATA_READ
        exemptedMembers: []
      - logType: DATA_WRITE
        exemptedMembers: []
```

#### Audit Log Analysis
```python
from google.cloud import logging

def analyze_phi_access_logs(project_id, bucket_name, days=30):
    """Analyze PHI access logs for HIPAA compliance"""
    
    client = logging.Client(project=project_id)
    
    # Query for storage access logs
    filter_str = f'''
        protoPayload.serviceName="storage.googleapis.com"
        AND protoPayload.resourceName:"{bucket_name}"
        AND timestamp >= "{(datetime.now() - timedelta(days=days)).isoformat()}Z"
    '''
    
    entries = client.list_entries(filter_=filter_str)
    
    access_summary = {
        'total_accesses': 0,
        'unique_users': set(),
        'access_patterns': {},
        'potential_violations': []
    }
    
    for entry in entries:
        access_summary['total_accesses'] += 1
        
        # Extract user information
        if hasattr(entry.payload, 'authenticationInfo'):
            user = entry.payload.authenticationInfo.principalEmail
            access_summary['unique_users'].add(user)
            
            # Track access patterns
            hour = entry.timestamp.hour
            if hour not in access_summary['access_patterns']:
                access_summary['access_patterns'][hour] = 0
            access_summary['access_patterns'][hour] += 1
            
            # Check for after-hours access (potential violation)
            if hour < 6 or hour > 22:
                access_summary['potential_violations'].append({
                    'user': user,
                    'timestamp': entry.timestamp,
                    'resource': entry.payload.resourceName,
                    'method': entry.payload.methodName
                })
    
    return access_summary

# Usage
audit_results = analyze_phi_access_logs("my-project", "phi-storage")
print(f"Total PHI accesses: {audit_results['total_accesses']}")
print(f"Unique users: {len(audit_results['unique_users'])}")
print(f"Potential violations: {len(audit_results['potential_violations'])}")
```

### 4. Data Integrity and Backup

#### Integrity Verification
```python
import hashlib
import json

def verify_phi_integrity(bucket_name, object_name):
    """Verify PHI data integrity for HIPAA compliance"""
    
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    blob = bucket.blob(object_name)
    
    # Get current object metadata
    blob.reload()
    
    # Verify checksums
    integrity_check = {
        'object_name': object_name,
        'size': blob.size,
        'md5_hash': blob.md5_hash,
        'crc32c': blob.crc32c,
        'etag': blob.etag,
        'generation': blob.generation,
        'time_created': blob.time_created.isoformat(),
        'updated': blob.updated.isoformat()
    }
    
    # Store integrity metadata
    metadata_blob = bucket.blob(f"{object_name}.integrity")
    metadata_blob.upload_from_string(
        json.dumps(integrity_check, indent=2),
        content_type='application/json'
    )
    
    print(f"Integrity verified for {object_name}")
    return integrity_check

def create_phi_backup(source_bucket, backup_bucket, retention_years=7):
    """Create HIPAA-compliant backup with retention policy"""
    
    client = storage.Client()
    source = client.bucket(source_bucket)
    backup = client.bucket(backup_bucket)
    
    # Set backup bucket location (different region for DR)
    backup.location = "us-east1"
    
    # Set retention policy (7 years for HIPAA)
    retention_period = retention_years * 365 * 24 * 3600  # seconds
    backup.retention_policy_effective_time = None
    backup.retention_period = retention_period
    backup.patch()
    
    # Copy all objects to backup
    for blob in source.list_blobs():
        backup_blob = backup.blob(blob.name)
        backup_blob.rewrite(blob)
        
        # Verify backup integrity
        verify_phi_integrity(backup_bucket, blob.name)
    
    print(f"Created HIPAA-compliant backup in {backup_bucket}")

# Usage
verify_phi_integrity("phi-storage", "patients/12345/record.pdf")
create_phi_backup("phi-storage", "phi-backup-storage")
```

## Compliance Monitoring

### Automated Compliance Checks
```python
def run_hipaa_compliance_check(project_id, bucket_name):
    """Run automated HIPAA compliance checks"""
    
    client = storage.Client(project=project_id)
    bucket = client.bucket(bucket_name)
    
    compliance_report = {
        'bucket_name': bucket_name,
        'timestamp': datetime.now().isoformat(),
        'checks': {}
    }
    
    # Check 1: Encryption at rest
    bucket.reload()
    compliance_report['checks']['encryption_at_rest'] = {
        'status': 'PASS' if bucket.default_kms_key_name else 'FAIL',
        'details': f"KMS Key: {bucket.default_kms_key_name}"
    }
    
    # Check 2: Uniform bucket-level access
    compliance_report['checks']['uniform_access'] = {
        'status': 'PASS' if bucket.iam_configuration.uniform_bucket_level_access_enabled else 'FAIL',
        'details': 'Uniform bucket-level access enabled'
    }
    
    # Check 3: Lifecycle policy for retention
    compliance_report['checks']['lifecycle_policy'] = {
        'status': 'PASS' if bucket.lifecycle_rules else 'FAIL',
        'details': f"Lifecycle rules: {len(bucket.lifecycle_rules or [])}"
    }
    
    # Check 4: Audit logging
    logging_client = logging.Client(project=project_id)
    try:
        # Check if audit logs are being generated
        filter_str = f'protoPayload.serviceName="storage.googleapis.com" AND protoPayload.resourceName:"{bucket_name}"'
        entries = list(logging_client.list_entries(filter_=filter_str, max_results=1))
        
        compliance_report['checks']['audit_logging'] = {
            'status': 'PASS' if entries else 'FAIL',
            'details': 'Audit logs are being generated'
        }
    except Exception as e:
        compliance_report['checks']['audit_logging'] = {
            'status': 'FAIL',
            'details': f"Error checking audit logs: {str(e)}"
        }
    
    # Check 5: Regional compliance
    compliance_report['checks']['regional_compliance'] = {
        'status': 'PASS' if bucket.location in ['us-central1', 'us-east1', 'us-west1'] else 'WARN',
        'details': f"Bucket location: {bucket.location}"
    }
    
    return compliance_report

# Usage
compliance = run_hipaa_compliance_check("my-project", "phi-storage")
print(json.dumps(compliance, indent=2))
```

### Compliance Reporting
```python
def generate_hipaa_compliance_report(project_id, bucket_names, output_file):
    """Generate comprehensive HIPAA compliance report"""
    
    report = {
        'report_date': datetime.now().isoformat(),
        'project_id': project_id,
        'buckets': [],
        'summary': {
            'total_buckets': len(bucket_names),
            'compliant_buckets': 0,
            'non_compliant_buckets': 0,
            'warnings': 0
        }
    }
    
    for bucket_name in bucket_names:
        bucket_compliance = run_hipaa_compliance_check(project_id, bucket_name)
        report['buckets'].append(bucket_compliance)
        
        # Count compliance status
        failed_checks = sum(1 for check in bucket_compliance['checks'].values() 
                          if check['status'] == 'FAIL')
        warning_checks = sum(1 for check in bucket_compliance['checks'].values() 
                           if check['status'] == 'WARN')
        
        if failed_checks == 0:
            report['summary']['compliant_buckets'] += 1
        else:
            report['summary']['non_compliant_buckets'] += 1
        
        report['summary']['warnings'] += warning_checks
    
    # Save report
    with open(output_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"HIPAA compliance report saved to {output_file}")
    return report

# Usage
buckets = ["phi-storage", "phi-backup-storage", "phi-archive-storage"]
report = generate_hipaa_compliance_report("my-project", buckets, "hipaa_compliance_report.json")
```

## HIPAA Compliance Checklist

### Administrative Safeguards
- [ ] Business Associate Agreement (BAA) signed with Google Cloud
- [ ] Security Officer designated and trained
- [ ] Workforce HIPAA training completed
- [ ] Access management procedures documented
- [ ] Incident response plan created
- [ ] Regular access reviews scheduled

### Technical Safeguards
- [ ] Customer-managed encryption keys (CMEK) implemented
- [ ] Uniform bucket-level access enabled
- [ ] IAM policies follow minimum necessary principle
- [ ] Audit logging enabled for all operations
- [ ] Data integrity verification implemented
- [ ] Backup and recovery procedures tested

### Physical Safeguards
- [ ] Google Cloud data center compliance verified
- [ ] Workstation security policies implemented
- [ ] Device access controls configured

### Ongoing Compliance
- [ ] Regular compliance audits scheduled
- [ ] Automated compliance monitoring implemented
- [ ] Staff training program established
- [ ] Incident response procedures tested
- [ ] Documentation maintained and updated

## Common HIPAA Violations to Avoid

### 1. Insufficient Access Controls
```python
# ❌ WRONG: Overly broad permissions
bucket.iam.policy.bindings.append({
    'role': 'roles/storage.admin',
    'members': ['allUsers']  # HIPAA violation!
})

# ✅ CORRECT: Specific, limited access
bucket.iam.policy.bindings.append({
    'role': 'roles/storage.objectViewer',
    'members': ['user:<EMAIL>'],
    'condition': {
        'expression': 'resource.name.startsWith("patients/12345/")'
    }
})
```

### 2. Inadequate Audit Logging
```python
# ❌ WRONG: Disabled audit logging
# No audit configuration

# ✅ CORRECT: Comprehensive audit logging
audit_config = {
    'service': 'storage.googleapis.com',
    'auditLogConfigs': [
        {'logType': 'ADMIN_READ'},
        {'logType': 'DATA_READ'},
        {'logType': 'DATA_WRITE'}
    ]
}
```

### 3. Unencrypted Data
```python
# ❌ WRONG: Default encryption only
bucket.create()

# ✅ CORRECT: Customer-managed encryption
bucket.default_kms_key_name = kms_key_name
bucket.create()
```

## Next Steps

1. **[Data Encryption](./encryption.md)** - Advanced encryption strategies
2. **[Audit Logging](./audit-logging.md)** - Comprehensive logging implementation
3. **[Data Residency](./data-residency.md)** - Geographic compliance controls

---

*Ensure your healthcare platform meets all HIPAA requirements by implementing these technical and administrative safeguards with Google Cloud Storage.*

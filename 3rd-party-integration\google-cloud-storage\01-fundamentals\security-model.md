# Google Cloud Storage Security Model

## Overview

Google Cloud Storage provides multiple layers of security controls essential for healthcare applications. This guide covers authentication, authorization, encryption, and compliance features required for HIPAA and other healthcare regulations.

## Authentication Methods

### 1. Service Accounts (Recommended for Applications)

Service accounts provide secure, programmatic access to GCS without user credentials.

#### Creating a Service Account
```bash
# Create service account
gcloud iam service-accounts create healthcare-storage-sa \
    --description="Healthcare platform storage access" \
    --display-name="Healthcare Storage Service Account"

# Generate key file
gcloud iam service-accounts keys create ~/healthcare-storage-key.json \
    --iam-account=healthcare-storage-sa@PROJECT_ID.iam.gserviceaccount.com
```

#### Application Integration
```python
from google.cloud import storage
import os

# Set environment variable
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = '/path/to/healthcare-storage-key.json'

# Initialize client
client = storage.Client()
```

### 2. OAuth 2.0 (User Authentication)

For user-facing applications requiring individual user access.

```javascript
// Frontend OAuth setup
const { GoogleAuth } = require('google-auth-library');

const auth = new GoogleAuth({
  scopes: ['https://www.googleapis.com/auth/cloud-platform'],
  credentials: {
    client_id: process.env.GOOGLE_CLIENT_ID,
    client_secret: process.env.GOOGLE_CLIENT_SECRET,
  }
});
```

### 3. Workload Identity (GKE Integration)

For applications running on Google Kubernetes Engine.

```yaml
# Kubernetes service account binding
apiVersion: v1
kind: ServiceAccount
metadata:
  name: healthcare-app-sa
  annotations:
    iam.gke.io/gcp-service-account: healthcare-storage-sa@PROJECT_ID.iam.gserviceaccount.com
```

## Authorization Models

### 1. Identity and Access Management (IAM)

IAM provides fine-grained access control at the project, bucket, and object levels.

#### Key Roles for Healthcare Applications

```bash
# Storage Admin (full access)
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:healthcare-storage-sa@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.admin"

# Storage Object Admin (object-level access)
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:healthcare-storage-sa@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.objectAdmin"

# Storage Object Viewer (read-only)
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:healthcare-storage-sa@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.objectViewer"
```

#### Custom Roles for Healthcare
```json
{
  "title": "Healthcare Storage Role",
  "description": "Custom role for healthcare file operations",
  "stage": "GA",
  "includedPermissions": [
    "storage.objects.create",
    "storage.objects.get",
    "storage.objects.list",
    "storage.objects.update",
    "storage.buckets.get"
  ]
}
```

### 2. Access Control Lists (ACLs)

Object-level permissions for granular access control.

```python
from google.cloud import storage

def set_object_acl(bucket_name, blob_name, user_email, role):
    """Set ACL for specific object"""
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    blob = bucket.blob(blob_name)
    
    # Add user with specific role
    blob.acl.user(user_email).grant(role)
    blob.acl.save()
    
    print(f"Granted {role} access to {user_email} for {blob_name}")

# Usage
set_object_acl('patient-records', 'patient-123/record.pdf', '<EMAIL>', 'READER')
```

### 3. Uniform Bucket-Level Access

Simplifies permissions by using only IAM (recommended for new buckets).

```python
def enable_uniform_bucket_access(bucket_name):
    """Enable uniform bucket-level access"""
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    
    bucket.iam_configuration.uniform_bucket_level_access_enabled = True
    bucket.patch()
    
    print(f"Enabled uniform bucket-level access for {bucket_name}")
```

## Encryption

### 1. Encryption at Rest

All data in GCS is automatically encrypted at rest using AES-256.

#### Google-Managed Encryption Keys (Default)
```python
# No additional configuration needed
# All objects are automatically encrypted
client = storage.Client()
bucket = client.bucket('healthcare-data')
blob = bucket.blob('patient-record.pdf')
blob.upload_from_filename('local-file.pdf')
# File is automatically encrypted
```

#### Customer-Managed Encryption Keys (CMEK)
```python
from google.cloud import storage

def upload_with_cmek(bucket_name, source_file, destination_name, kms_key_name):
    """Upload file with customer-managed encryption key"""
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    blob = bucket.blob(destination_name)
    
    # Set encryption key
    blob.kms_key_name = kms_key_name
    
    blob.upload_from_filename(source_file)
    print(f"Uploaded {destination_name} with CMEK encryption")

# Usage
kms_key = "projects/PROJECT_ID/locations/us-central1/keyRings/healthcare-ring/cryptoKeys/patient-data-key"
upload_with_cmek('patient-records', 'record.pdf', 'patient-123/record.pdf', kms_key)
```

#### Customer-Supplied Encryption Keys (CSEK)
```python
import base64
import hashlib

def upload_with_csek(bucket_name, source_file, destination_name, encryption_key):
    """Upload file with customer-supplied encryption key"""
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    blob = bucket.blob(destination_name)
    
    # Prepare encryption key
    key_bytes = encryption_key.encode('utf-8')
    key_hash = hashlib.sha256(key_bytes).digest()
    
    blob.upload_from_filename(
        source_file,
        encryption_key=base64.b64encode(key_bytes),
        encryption_key_sha256=base64.b64encode(key_hash)
    )
    
    print(f"Uploaded {destination_name} with CSEK encryption")
```

### 2. Encryption in Transit

All communication with GCS is encrypted using TLS 1.2+.

```python
# HTTPS is enforced by default
# All API calls use TLS encryption
client = storage.Client()
# All operations are automatically encrypted in transit
```

## Signed URLs for Secure Access

Provide temporary, secure access to objects without exposing credentials.

### Creating Signed URLs
```python
from google.cloud import storage
from datetime import datetime, timedelta

def generate_signed_url(bucket_name, blob_name, expiration_hours=1):
    """Generate a signed URL for temporary access"""
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    blob = bucket.blob(blob_name)
    
    # Set expiration time
    expiration = datetime.utcnow() + timedelta(hours=expiration_hours)
    
    # Generate signed URL
    signed_url = blob.generate_signed_url(
        version="v4",
        expiration=expiration,
        method="GET"
    )
    
    return signed_url

# Usage for patient file access
url = generate_signed_url('patient-records', 'patient-123/xray.jpg', 2)
print(f"Temporary access URL: {url}")
```

### Upload Signed URLs
```python
def generate_upload_signed_url(bucket_name, blob_name, content_type):
    """Generate signed URL for file uploads"""
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    blob = bucket.blob(blob_name)
    
    signed_url = blob.generate_signed_url(
        version="v4",
        expiration=datetime.utcnow() + timedelta(hours=1),
        method="PUT",
        content_type=content_type
    )
    
    return signed_url

# Usage for patient file upload
upload_url = generate_upload_signed_url(
    'patient-uploads', 
    'patient-123/lab-result.pdf', 
    'application/pdf'
)
```

## Healthcare-Specific Security Patterns

### 1. Patient Data Isolation
```python
def create_patient_bucket_structure(patient_id):
    """Create isolated storage structure for patient data"""
    client = storage.Client()
    
    # Use patient ID in bucket naming or object prefixes
    bucket_name = f"patient-data-{patient_id}"
    
    # Create bucket with healthcare-specific settings
    bucket = client.bucket(bucket_name)
    bucket.location = "us-central1"  # HIPAA-compliant region
    bucket.storage_class = "STANDARD"
    
    # Enable uniform bucket-level access
    bucket.iam_configuration.uniform_bucket_level_access_enabled = True
    
    # Set lifecycle policy
    bucket.lifecycle_rules = [{
        'action': {'type': 'SetStorageClass', 'storageClass': 'NEARLINE'},
        'condition': {'age': 90}
    }]
    
    bucket.create()
    return bucket
```

### 2. Role-Based Access for Healthcare Teams
```python
def setup_healthcare_team_access(bucket_name, team_config):
    """Configure role-based access for healthcare teams"""
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    
    # Doctor access (read/write patient files)
    for doctor_email in team_config['doctors']:
        bucket.iam.policy.bindings.append({
            'role': 'roles/storage.objectAdmin',
            'members': [f'user:{doctor_email}']
        })
    
    # Nurse access (read patient files, write notes)
    for nurse_email in team_config['nurses']:
        bucket.iam.policy.bindings.append({
            'role': 'roles/storage.objectViewer',
            'members': [f'user:{nurse_email}']
        })
    
    # Admin access (full bucket management)
    for admin_email in team_config['admins']:
        bucket.iam.policy.bindings.append({
            'role': 'roles/storage.admin',
            'members': [f'user:{admin_email}']
        })
    
    bucket.iam.policy.save()
```

### 3. Audit Logging Configuration
```yaml
# Cloud Logging configuration for HIPAA compliance
auditConfigs:
  - service: storage.googleapis.com
    auditLogConfigs:
      - logType: ADMIN_READ
      - logType: DATA_READ
      - logType: DATA_WRITE
```

## Security Checklist

### Authentication & Authorization
- [ ] Service accounts configured with minimal permissions
- [ ] IAM roles follow principle of least privilege
- [ ] Uniform bucket-level access enabled
- [ ] Regular access review process established

### Encryption
- [ ] Customer-managed encryption keys (CMEK) implemented
- [ ] Key rotation policy established
- [ ] Encryption key access logged and monitored

### Access Control
- [ ] Signed URLs used for temporary access
- [ ] Object-level permissions configured appropriately
- [ ] Cross-origin resource sharing (CORS) configured securely

### Monitoring & Compliance
- [ ] Audit logging enabled for all operations
- [ ] Access patterns monitored for anomalies
- [ ] Compliance reports automated
- [ ] Incident response procedures documented

## Next Steps

1. **[Pricing Structure](./pricing.md)** - Understand cost implications of security features
2. **[HIPAA Requirements](../03-healthcare-compliance/hipaa.md)** - Healthcare-specific compliance
3. **[Access Control Deep Dive](../02-gcs-deep-dive/access-control.md)** - Advanced permission patterns

---

*Implement these security measures to ensure your healthcare platform meets regulatory requirements and protects patient data.*

# Web Application Security

## Overview

Web application security encompasses the practices, technologies, and methodologies used to protect web applications from various threats and vulnerabilities. This guide covers comprehensive security measures for modern web applications, particularly focusing on healthcare platforms with payment processing capabilities.

## 🎯 Learning Objectives

By the end of this module, you will understand:
- Common web application vulnerabilities and attack vectors
- Security controls and defensive mechanisms
- Secure development practices for web applications
- Security testing methodologies
- Incident response for web application breaches

## 🔍 Common Web Application Vulnerabilities

### 1. Injection Attacks

#### SQL Injection
**Description**: Malicious SQL code inserted into application queries.

**Example - Vulnerable Code**:
```javascript
// ❌ Vulnerable to SQL injection
const query = `SELECT * FROM users WHERE email = '${userEmail}' AND password = '${password}'`;
db.query(query);
```

**Example - Secure Code**:
```javascript
// ✅ Using parameterized queries
const query = 'SELECT * FROM users WHERE email = ? AND password = ?';
db.query(query, [userEmail, hashedPassword]);
```

**Prevention**:
- Use parameterized queries/prepared statements
- Input validation and sanitization
- Least privilege database access
- Regular security testing

#### NoSQL Injection
**Example - Vulnerable MongoDB Query**:
```javascript
// ❌ Vulnerable
db.users.find({username: req.body.username, password: req.body.password});

// ✅ Secure
db.users.find({
  username: {$eq: req.body.username},
  password: {$eq: hashedPassword}
});
```

### 2. Cross-Site Scripting (XSS)

#### Stored XSS
**Example - Healthcare Platform Comment System**:
```javascript
// ❌ Vulnerable - storing unsanitized user input
const comment = req.body.comment;
await db.comments.create({
  patientId: req.user.id,
  content: comment // Dangerous if contains <script>
});

// ✅ Secure - sanitize and validate
const DOMPurify = require('dompurify');
const comment = DOMPurify.sanitize(req.body.comment);
await db.comments.create({
  patientId: req.user.id,
  content: comment
});
```

#### Reflected XSS
**Example - Search Functionality**:
```html
<!-- ❌ Vulnerable -->
<p>Search results for: <%= searchTerm %></p>

<!-- ✅ Secure -->
<p>Search results for: <%= escapeHtml(searchTerm) %></p>
```

**Prevention Strategies**:
- Content Security Policy (CSP)
- Input validation and output encoding
- Use security-focused templating engines
- Regular security scanning

### 3. Cross-Site Request Forgery (CSRF)

**Example - Payment Processing Vulnerability**:
```html
<!-- ❌ Vulnerable form -->
<form action="/api/payments/process" method="POST">
  <input name="amount" value="1000">
  <input name="recipient" value="doctor123">
  <button type="submit">Pay</button>
</form>
```

**Secure Implementation**:
```html
<!-- ✅ CSRF protection -->
<form action="/api/payments/process" method="POST">
  <input type="hidden" name="_token" value="<%= csrfToken %>">
  <input name="amount" value="1000">
  <input name="recipient" value="doctor123">
  <button type="submit">Pay</button>
</form>
```

```javascript
// Server-side CSRF validation
app.use(csrf({
  cookie: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  }
}));
```

## 🛡️ Security Controls and Defensive Mechanisms

### 1. Authentication Security

#### Multi-Factor Authentication (MFA)
```javascript
// Example MFA implementation for healthcare platform
const speakeasy = require('speakeasy');

class MFAService {
  static generateSecret(userEmail) {
    return speakeasy.generateSecret({
      name: `HealthPlatform (${userEmail})`,
      issuer: 'HealthPlatform'
    });
  }

  static verifyToken(secret, token) {
    return speakeasy.totp.verify({
      secret: secret,
      encoding: 'base32',
      token: token,
      window: 2 // Allow 2 time steps tolerance
    });
  }
}

// Usage in login endpoint
app.post('/api/auth/verify-mfa', async (req, res) => {
  const { userId, mfaToken } = req.body;
  
  const user = await User.findById(userId);
  const isValid = MFAService.verifyToken(user.mfaSecret, mfaToken);
  
  if (isValid) {
    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );
    res.json({ token });
  } else {
    res.status(401).json({ error: 'Invalid MFA token' });
  }
});
```

### 2. Session Management

#### Secure Session Configuration
```javascript
// Express session configuration for healthcare platform
app.use(session({
  name: 'healthplatform.sid', // Don't use default session name
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    httpOnly: true, // Prevent XSS access to cookies
    maxAge: 30 * 60 * 1000, // 30 minutes
    sameSite: 'strict' // CSRF protection
  },
  store: new RedisStore({
    client: redisClient,
    prefix: 'sess:'
  })
}));

// Session timeout middleware
const sessionTimeout = (req, res, next) => {
  if (req.session.lastActivity) {
    const timeSinceLastActivity = Date.now() - req.session.lastActivity;
    if (timeSinceLastActivity > 30 * 60 * 1000) { // 30 minutes
      req.session.destroy();
      return res.status(401).json({ error: 'Session expired' });
    }
  }
  req.session.lastActivity = Date.now();
  next();
};
```

### 3. Input Validation and Sanitization

#### Comprehensive Validation Framework
```javascript
const Joi = require('joi');
const DOMPurify = require('dompurify');

// Healthcare-specific validation schemas
const schemas = {
  patientRegistration: Joi.object({
    firstName: Joi.string().min(2).max(50).pattern(/^[a-zA-Z\s]+$/).required(),
    lastName: Joi.string().min(2).max(50).pattern(/^[a-zA-Z\s]+$/).required(),
    email: Joi.string().email().required(),
    phone: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).required(),
    dateOfBirth: Joi.date().max('now').required(),
    medicalHistory: Joi.string().max(5000).optional()
  }),
  
  paymentInfo: Joi.object({
    amount: Joi.number().positive().precision(2).max(10000).required(),
    currency: Joi.string().valid('USD', 'EUR', 'GBP').required(),
    description: Joi.string().max(255).required()
  })
};

// Validation middleware
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details.map(d => d.message)
      });
    }
    
    // Sanitize string inputs
    Object.keys(value).forEach(key => {
      if (typeof value[key] === 'string') {
        value[key] = DOMPurify.sanitize(value[key]);
      }
    });
    
    req.validatedBody = value;
    next();
  };
};

// Usage
app.post('/api/patients/register', 
  validate(schemas.patientRegistration),
  async (req, res) => {
    // req.validatedBody contains clean, validated data
    const patient = await Patient.create(req.validatedBody);
    res.json({ patient });
  }
);
```

## 🔒 Security Headers and Configuration

### Essential Security Headers
```javascript
const helmet = require('helmet');

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.stripe.com"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: []
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// Additional security headers
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  next();
});
```

## 🧪 Security Testing

### 1. Automated Security Testing
```javascript
// Jest security test examples
describe('Authentication Security', () => {
  test('should prevent SQL injection in login', async () => {
    const maliciousInput = "admin'; DROP TABLE users; --";
    const response = await request(app)
      .post('/api/auth/login')
      .send({
        email: maliciousInput,
        password: 'password'
      });
    
    expect(response.status).toBe(400);
    expect(response.body.error).toContain('Validation failed');
  });

  test('should enforce rate limiting', async () => {
    const requests = Array(11).fill().map(() => 
      request(app)
        .post('/api/auth/login')
        .send({ email: '<EMAIL>', password: 'wrong' })
    );
    
    const responses = await Promise.all(requests);
    const lastResponse = responses[responses.length - 1];
    expect(lastResponse.status).toBe(429);
  });
});
```

### 2. Security Scanning Integration
```yaml
# .github/workflows/security-scan.yml
name: Security Scan
on: [push, pull_request]

jobs:
  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Run OWASP ZAP Scan
        uses: zaproxy/action-full-scan@v0.4.0
        with:
          target: 'http://localhost:3000'
          
      - name: Run Snyk Security Scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
```

## 📊 Security Monitoring and Logging

### Comprehensive Logging Strategy
```javascript
const winston = require('winston');

// Security-focused logger configuration
const securityLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'healthcare-platform' },
  transports: [
    new winston.transports.File({ filename: 'logs/security.log' }),
    new winston.transports.Console()
  ]
});

// Security event logging middleware
const logSecurityEvent = (eventType) => {
  return (req, res, next) => {
    securityLogger.info('Security Event', {
      type: eventType,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      timestamp: new Date().toISOString(),
      path: req.path,
      method: req.method
    });
    next();
  };
};

// Usage
app.post('/api/auth/login', logSecurityEvent('LOGIN_ATTEMPT'), authController.login);
app.post('/api/payments/process', logSecurityEvent('PAYMENT_ATTEMPT'), paymentController.process);
```

## 🚨 Incident Response

### Security Incident Detection
```javascript
// Real-time security monitoring
const detectSuspiciousActivity = async (req, res, next) => {
  const clientIP = req.ip;
  const userId = req.user?.id;
  
  // Check for multiple failed login attempts
  const failedAttempts = await redis.get(`failed_attempts:${clientIP}`);
  if (failedAttempts && parseInt(failedAttempts) > 5) {
    securityLogger.warn('Suspicious Activity Detected', {
      type: 'MULTIPLE_FAILED_LOGINS',
      ip: clientIP,
      attempts: failedAttempts
    });
    
    // Trigger security alert
    await sendSecurityAlert({
      type: 'BRUTE_FORCE_ATTEMPT',
      ip: clientIP,
      timestamp: new Date()
    });
    
    return res.status(429).json({ error: 'Account temporarily locked' });
  }
  
  next();
};
```

## 📚 Best Practices Summary

### Development Phase
1. **Secure by Design**: Implement security from the beginning
2. **Input Validation**: Validate and sanitize all user inputs
3. **Authentication**: Implement strong authentication mechanisms
4. **Authorization**: Follow principle of least privilege
5. **Error Handling**: Don't expose sensitive information in errors

### Testing Phase
1. **Automated Testing**: Include security tests in CI/CD pipeline
2. **Penetration Testing**: Regular security assessments
3. **Code Review**: Security-focused code reviews
4. **Dependency Scanning**: Regular vulnerability scans

### Production Phase
1. **Monitoring**: Continuous security monitoring
2. **Logging**: Comprehensive security logging
3. **Updates**: Regular security updates and patches
4. **Incident Response**: Prepared incident response plan

## 🔗 Additional Resources

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [OWASP Application Security Verification Standard](https://owasp.org/www-project-application-security-verification-standard/)
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
- [Healthcare Security Best Practices](../23-healthcare-security.md)
- [Payment Security Guidelines](../24-payment-security.md)

---

**Next**: [API Security](05-api-security.md) | **Previous**: [Secure Coding Practices](03-secure-coding-practices.md)

# Network Security

## Overview

Network security is fundamental for healthcare platforms that transmit sensitive patient data and payment information across networks. This guide covers comprehensive network security measures including firewalls, intrusion detection, VPNs, and network monitoring for healthcare compliance.

## 🎯 Learning Objectives

By the end of this module, you will understand:
- Network architecture security principles
- Firewall configuration and management
- Intrusion detection and prevention systems
- VPN and secure remote access
- Network monitoring and threat detection
- Zero Trust network architecture

## 🏗️ Secure Network Architecture

### 1. Healthcare Network Segmentation

```yaml
# Network segmentation design for healthcare platform
network_architecture:
  dmz_zone:
    purpose: "Public-facing services"
    components:
      - web_application_firewall
      - load_balancers
      - reverse_proxies
    security_level: "high"
    
  application_zone:
    purpose: "Application servers"
    components:
      - web_servers
      - api_servers
      - application_logic
    security_level: "high"
    
  database_zone:
    purpose: "Data storage"
    components:
      - database_servers
      - backup_systems
      - file_storage
    security_level: "critical"
    
  management_zone:
    purpose: "Administrative access"
    components:
      - monitoring_systems
      - logging_servers
      - admin_workstations
    security_level: "critical"
    
  payment_zone:
    purpose: "PCI DSS compliant payment processing"
    components:
      - payment_processors
      - tokenization_services
      - hsm_devices
    security_level: "critical"
    compliance: "PCI_DSS"
```

### 2. Network Security Zones Implementation

```bash
#!/bin/bash
# Network security zones configuration script

# Configure firewall zones
configure_firewall_zones() {
    # DMZ Zone (********/24)
    iptables -N DMZ_ZONE
    iptables -A DMZ_ZONE -s 0.0.0.0/0 -p tcp --dport 80 -j ACCEPT
    iptables -A DMZ_ZONE -s 0.0.0.0/0 -p tcp --dport 443 -j ACCEPT
    iptables -A DMZ_ZONE -j LOG --log-prefix "DMZ_DENIED: "
    iptables -A DMZ_ZONE -j DROP
    
    # Application Zone (********/24)
    iptables -N APP_ZONE
    iptables -A APP_ZONE -s ********/24 -p tcp --dport 8080 -j ACCEPT
    iptables -A APP_ZONE -s ********/24 -p tcp --dport 8080 -j ACCEPT  # Management
    iptables -A APP_ZONE -j LOG --log-prefix "APP_DENIED: "
    iptables -A APP_ZONE -j DROP
    
    # Database Zone (********/24)
    iptables -N DB_ZONE
    iptables -A DB_ZONE -s ********/24 -p tcp --dport 5432 -j ACCEPT
    iptables -A DB_ZONE -s ********/24 -p tcp --dport 5432 -j ACCEPT   # Management
    iptables -A DB_ZONE -j LOG --log-prefix "DB_DENIED: "
    iptables -A DB_ZONE -j DROP
    
    # Payment Zone (********/24) - Isolated
    iptables -N PAYMENT_ZONE
    iptables -A PAYMENT_ZONE -s ********/24 -p tcp --dport 8443 -j ACCEPT
    iptables -A PAYMENT_ZONE -d 0.0.0.0/0 -p tcp --dport 443 -j ACCEPT  # Outbound to payment APIs
    iptables -A PAYMENT_ZONE -j LOG --log-prefix "PAYMENT_DENIED: "
    iptables -A PAYMENT_ZONE -j DROP
}

# Apply zone rules to interfaces
apply_zone_rules() {
    iptables -A FORWARD -i dmz0 -j DMZ_ZONE
    iptables -A FORWARD -i app0 -j APP_ZONE
    iptables -A FORWARD -i db0 -j DB_ZONE
    iptables -A FORWARD -i payment0 -j PAYMENT_ZONE
}

# Configure logging
configure_logging() {
    # Log all denied connections
    iptables -A INPUT -j LOG --log-prefix "INPUT_DENIED: " --log-level 4
    iptables -A FORWARD -j LOG --log-prefix "FORWARD_DENIED: " --log-level 4
    
    # Configure rsyslog for security events
    cat >> /etc/rsyslog.conf << EOF
# Security logging
:msg,contains,"_DENIED:" /var/log/security/firewall.log
& stop
EOF
    
    systemctl restart rsyslog
}

configure_firewall_zones
apply_zone_rules
configure_logging
```

## 🔥 Advanced Firewall Configuration

### 1. pfSense Configuration for Healthcare

```php
<?php
// pfSense firewall rules configuration for healthcare platform

$firewall_rules = [
    // WAN Rules
    'wan' => [
        [
            'type' => 'pass',
            'interface' => 'wan',
            'protocol' => 'tcp',
            'source' => 'any',
            'destination' => 'wan_address',
            'destination_port' => '443',
            'description' => 'HTTPS to healthcare platform',
            'log' => true
        ],
        [
            'type' => 'block',
            'interface' => 'wan',
            'protocol' => 'any',
            'source' => 'any',
            'destination' => 'any',
            'description' => 'Block all other WAN traffic',
            'log' => true
        ]
    ],
    
    // DMZ Rules
    'dmz' => [
        [
            'type' => 'pass',
            'interface' => 'dmz',
            'protocol' => 'tcp',
            'source' => 'dmz_net',
            'destination' => 'app_net',
            'destination_port' => '8080',
            'description' => 'DMZ to Application servers',
            'log' => true
        ],
        [
            'type' => 'block',
            'interface' => 'dmz',
            'protocol' => 'any',
            'source' => 'dmz_net',
            'destination' => 'db_net',
            'description' => 'Block DMZ to Database',
            'log' => true
        ]
    ],
    
    // Application Rules
    'app' => [
        [
            'type' => 'pass',
            'interface' => 'app',
            'protocol' => 'tcp',
            'source' => 'app_net',
            'destination' => 'db_net',
            'destination_port' => '5432',
            'description' => 'App to Database',
            'log' => true
        ],
        [
            'type' => 'pass',
            'interface' => 'app',
            'protocol' => 'tcp',
            'source' => 'app_net',
            'destination' => 'payment_net',
            'destination_port' => '8443',
            'description' => 'App to Payment processing',
            'log' => true
        ]
    ],
    
    // Database Rules (most restrictive)
    'db' => [
        [
            'type' => 'pass',
            'interface' => 'db',
            'protocol' => 'tcp',
            'source' => 'app_net',
            'destination' => 'db_net',
            'destination_port' => '5432',
            'description' => 'Database access from App only',
            'log' => true
        ],
        [
            'type' => 'block',
            'interface' => 'db',
            'protocol' => 'any',
            'source' => 'any',
            'destination' => 'any',
            'description' => 'Block all other database traffic',
            'log' => true
        ]
    ]
];

// Apply firewall rules
function apply_firewall_rules($rules) {
    foreach ($rules as $interface => $interface_rules) {
        foreach ($interface_rules as $rule) {
            $command = build_firewall_command($rule);
            exec($command);
            log_firewall_change($rule);
        }
    }
}

function build_firewall_command($rule) {
    return sprintf(
        "pfctl -a healthcare/%s -f - << EOF\n%s %s from %s to %s port %s\nEOF",
        $rule['interface'],
        $rule['type'],
        $rule['protocol'],
        $rule['source'],
        $rule['destination'],
        $rule['destination_port'] ?? 'any'
    );
}
?>
```

### 2. Web Application Firewall (WAF) Rules

```yaml
# ModSecurity rules for healthcare application
SecRuleEngine On
SecRequestBodyAccess On
SecResponseBodyAccess On

# Healthcare-specific rules
SecRule REQUEST_FILENAME "@contains /api/patients" \
    "id:1001,\
     phase:1,\
     block,\
     msg:'Potential patient data access attempt',\
     logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
     severity:'CRITICAL',\
     tag:'healthcare',\
     tag:'patient-data'"

# Block SQL injection attempts on healthcare endpoints
SecRule ARGS "@detectSQLi" \
    "id:1002,\
     phase:2,\
     block,\
     msg:'SQL Injection Attack on Healthcare API',\
     logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
     severity:'CRITICAL',\
     tag:'healthcare',\
     tag:'sqli'"

# Rate limiting for authentication endpoints
SecRule REQUEST_URI "@beginsWith /api/auth" \
    "id:1003,\
     phase:1,\
     pass,\
     setvar:'ip.auth_attempts=+1',\
     expirevar:'ip.auth_attempts=300',\
     nolog"

SecRule IP:AUTH_ATTEMPTS "@gt 5" \
    "id:1004,\
     phase:1,\
     block,\
     msg:'Too many authentication attempts',\
     logdata:'IP: %{REMOTE_ADDR} has made %{ip.auth_attempts} attempts',\
     severity:'WARNING',\
     tag:'healthcare',\
     tag:'brute-force'"

# Block access to sensitive files
SecRule REQUEST_FILENAME "@contains .env" \
    "id:1005,\
     phase:1,\
     block,\
     msg:'Attempt to access sensitive configuration file',\
     severity:'CRITICAL',\
     tag:'healthcare',\
     tag:'sensitive-files'"
```

## 🛡️ Intrusion Detection and Prevention

### 1. Suricata IDS/IPS Configuration

```yaml
# Suricata configuration for healthcare network monitoring
vars:
  address-groups:
    HOME_NET: "[10.0.0.0/8,***********/16,**********/12]"
    EXTERNAL_NET: "!$HOME_NET"
    HTTP_SERVERS: "[********/24,********/24]"
    SMTP_SERVERS: "$HOME_NET"
    SQL_SERVERS: "********/24"
    DNS_SERVERS: "$HOME_NET"
    TELNET_SERVERS: "$HOME_NET"
    AIM_SERVERS: "$EXTERNAL_NET"
    DC_SERVERS: "$HOME_NET"
    DNP3_SERVER: "$HOME_NET"
    DNP3_CLIENT: "$HOME_NET"
    MODBUS_CLIENT: "$HOME_NET"
    MODBUS_SERVER: "$HOME_NET"
    ENIP_CLIENT: "$HOME_NET"
    ENIP_SERVER: "$HOME_NET"

  port-groups:
    HTTP_PORTS: "80"
    SHELLCODE_PORTS: "!80"
    ORACLE_PORTS: 1521
    SSH_PORTS: 22
    DNP3_PORTS: 20000
    MODBUS_PORTS: 502
    FILE_DATA_PORTS: "[$HTTP_PORTS,110,143]"
    FTP_PORTS: 21
    GENEVE_PORTS: 6081
    VXLAN_PORTS: 4789
    TEREDO_PORTS: 3544

# Custom rules for healthcare
rule-files:
  - healthcare-custom.rules
  - emerging-threats.rules
  - classification.config
  - reference.config

# Healthcare-specific custom rules
healthcare-custom.rules: |
  # Detect potential HIPAA violations
  alert tcp $HOME_NET any -> $EXTERNAL_NET any (msg:"Potential PHI data exfiltration"; content:"ssn"; content:"dob"; sid:2001001; rev:1;)
  
  # Monitor database access patterns
  alert tcp any any -> $SQL_SERVERS 5432 (msg:"Unusual database access pattern"; threshold:type both, track by_src, count 100, seconds 60; sid:2001002; rev:1;)
  
  # Detect payment card data
  alert tcp $HOME_NET any -> $EXTERNAL_NET any (msg:"Potential credit card data transmission"; pcre:"/\b(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|3[0-9]{13}|6(?:011|5[0-9]{2})[0-9]{12})\b/"; sid:2001003; rev:1;)
  
  # Monitor for unauthorized access to patient records
  alert http $EXTERNAL_NET any -> $HTTP_SERVERS any (msg:"Unauthorized patient record access attempt"; content:"GET"; http_uri; content:"/api/patients/"; sid:2001004; rev:1;)
  
  # Detect potential ransomware activity
  alert tcp $HOME_NET any -> $EXTERNAL_NET any (msg:"Potential ransomware communication"; content:".onion"; sid:2001005; rev:1;)
```

### 2. Network Monitoring with Security Onion

```bash
#!/bin/bash
# Security Onion deployment script for healthcare network monitoring

# Install Security Onion
install_security_onion() {
    # Download and verify Security Onion ISO
    wget https://github.com/Security-Onion-Solutions/securityonion/releases/download/v2.3.100/securityonion-2.3.100.iso
    
    # Verify checksum
    sha256sum -c securityonion-2.3.100.iso.sha256
    
    # Create VM and install (manual process)
    echo "Install Security Onion on dedicated hardware or VM"
}

# Configure network monitoring
configure_monitoring() {
    # Configure network interfaces for monitoring
    cat > /etc/network/interfaces << EOF
# Management interface
auto eth0
iface eth0 inet static
    address **********
    netmask *************
    gateway ********
    dns-nameservers ******* *******

# Monitoring interface (promiscuous mode)
auto eth1
iface eth1 inet manual
    up ip link set \$IFACE up
    up ip link set \$IFACE promisc on
    down ip link set \$IFACE promisc off
    down ip link set \$IFACE down
EOF

    # Configure Suricata for healthcare monitoring
    cat > /etc/suricata/suricata.yaml << EOF
vars:
  address-groups:
    HOME_NET: "[10.0.0.0/8]"
    EXTERNAL_NET: "!$HOME_NET"
    HEALTHCARE_SERVERS: "[********/24,********/24,********/24]"
    PAYMENT_SERVERS: "********/24"

af-packet:
  - interface: eth1
    cluster-id: 99
    cluster-type: cluster_flow
    defrag: yes

outputs:
  - eve-log:
      enabled: yes
      filetype: regular
      filename: eve.json
      types:
        - alert
        - http
        - dns
        - tls
        - files
        - smtp
        - ssh
        - flow

rule-files:
  - /etc/suricata/rules/healthcare-custom.rules
  - /etc/suricata/rules/emerging-threats.rules
EOF
}

# Configure Elasticsearch for log storage
configure_elasticsearch() {
    cat > /etc/elasticsearch/elasticsearch.yml << EOF
cluster.name: healthcare-security
node.name: security-node-1
path.data: /var/lib/elasticsearch
path.logs: /var/log/elasticsearch
network.host: **********
http.port: 9200
discovery.type: single-node

# Security settings
xpack.security.enabled: true
xpack.security.transport.ssl.enabled: true
xpack.security.http.ssl.enabled: true
EOF
}

# Configure Kibana dashboards
configure_kibana() {
    cat > /etc/kibana/kibana.yml << EOF
server.port: 5601
server.host: "**********"
elasticsearch.hosts: ["https://**********:9200"]
elasticsearch.username: "kibana_system"
elasticsearch.password: "healthcare_kibana_password"

# Healthcare-specific dashboard settings
kibana.defaultAppId: "dashboard"
server.name: "healthcare-security-dashboard"
EOF
}

install_security_onion
configure_monitoring
configure_elasticsearch
configure_kibana
```

## 🔒 VPN and Secure Remote Access

### 1. OpenVPN Configuration for Healthcare

```bash
#!/bin/bash
# OpenVPN server configuration for healthcare remote access

# Generate CA and server certificates
generate_certificates() {
    cd /etc/openvpn/easy-rsa/
    
    # Initialize PKI
    ./easyrsa init-pki
    
    # Build CA
    ./easyrsa build-ca nopass
    
    # Generate server certificate
    ./easyrsa build-server-full healthcare-vpn nopass
    
    # Generate Diffie-Hellman parameters
    ./easyrsa gen-dh
    
    # Generate TLS auth key
    openvpn --genkey --secret /etc/openvpn/ta.key
}

# Configure OpenVPN server
configure_openvpn_server() {
    cat > /etc/openvpn/server.conf << EOF
# Healthcare VPN Server Configuration
port 1194
proto udp
dev tun

# Certificates and keys
ca /etc/openvpn/easy-rsa/pki/ca.crt
cert /etc/openvpn/easy-rsa/pki/issued/healthcare-vpn.crt
key /etc/openvpn/easy-rsa/pki/private/healthcare-vpn.key
dh /etc/openvpn/easy-rsa/pki/dh.pem
tls-auth /etc/openvpn/ta.key 0

# Network configuration
server ******** *************
ifconfig-pool-persist ipp.txt

# Push routes to healthcare networks
push "route 10.0.0.0 *********"
push "dhcp-option DNS *********"
push "dhcp-option DOMAIN healthcare.local"

# Security settings
cipher AES-256-GCM
auth SHA256
tls-version-min 1.2
tls-cipher TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384

# Client restrictions
client-to-client
duplicate-cn
max-clients 50

# Logging
log /var/log/openvpn/server.log
status /var/log/openvpn/status.log
verb 3
mute 20

# Security hardening
user nobody
group nogroup
persist-key
persist-tun

# Client certificate verification
verify-client-cert require
remote-cert-tls client

# Disconnect clients after inactivity
inactive 3600
ping 10
ping-restart 120
EOF
}

# Configure client certificate generation
generate_client_cert() {
    local client_name=$1
    
    cd /etc/openvpn/easy-rsa/
    ./easyrsa build-client-full "$client_name" nopass
    
    # Generate client configuration
    cat > "/etc/openvpn/clients/${client_name}.ovpn" << EOF
client
dev tun
proto udp
remote healthcare-vpn.company.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
cipher AES-256-GCM
auth SHA256
verb 3

# Embedded certificates
<ca>
$(cat /etc/openvpn/easy-rsa/pki/ca.crt)
</ca>

<cert>
$(cat /etc/openvpn/easy-rsa/pki/issued/${client_name}.crt)
</cert>

<key>
$(cat /etc/openvpn/easy-rsa/pki/private/${client_name}.key)
</key>

<tls-auth>
$(cat /etc/openvpn/ta.key)
</tls-auth>
key-direction 1
EOF
}

generate_certificates
configure_openvpn_server
```

### 2. Zero Trust Network Access (ZTNA)

```yaml
# Zero Trust architecture configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: zero-trust-config
data:
  policy.yaml: |
    # Zero Trust policies for healthcare platform
    policies:
      - name: "healthcare-admin-access"
        description: "Administrative access to healthcare systems"
        subjects:
          - "group:healthcare-admins"
        resources:
          - "healthcare.local/admin/*"
        conditions:
          - device_trust_level: "high"
          - location: "corporate_network"
          - mfa_verified: true
          - certificate_valid: true
        actions:
          - "allow"
        
      - name: "doctor-patient-data-access"
        description: "Doctor access to patient data"
        subjects:
          - "group:doctors"
        resources:
          - "healthcare.local/api/patients/*"
        conditions:
          - device_trust_level: "medium"
          - role_verified: true
          - session_timeout: "4h"
          - audit_logging: true
        actions:
          - "allow"
        
      - name: "payment-processing-access"
        description: "Access to payment processing systems"
        subjects:
          - "group:billing-staff"
        resources:
          - "healthcare.local/payments/*"
        conditions:
          - device_trust_level: "high"
          - pci_compliant_device: true
          - dedicated_network: true
          - enhanced_monitoring: true
        actions:
          - "allow"
```

## 📊 Network Monitoring and Analytics

### 1. Network Traffic Analysis

```python
#!/usr/bin/env python3
# Network traffic analysis for healthcare security

import scapy.all as scapy
import json
import logging
from datetime import datetime
import re

class HealthcareNetworkMonitor:
    def __init__(self):
        self.suspicious_patterns = [
            r'\b\d{3}-\d{2}-\d{4}\b',  # SSN pattern
            r'\b4[0-9]{12}(?:[0-9]{3})?\b',  # Visa card pattern
            r'\b5[1-5][0-9]{14}\b',  # MasterCard pattern
        ]
        self.setup_logging()
    
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('/var/log/healthcare-network-monitor.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def analyze_packet(self, packet):
        """Analyze individual network packets for security threats"""
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'src_ip': None,
            'dst_ip': None,
            'protocol': None,
            'payload': None,
            'alerts': []
        }
        
        if packet.haslayer(scapy.IP):
            analysis['src_ip'] = packet[scapy.IP].src
            analysis['dst_ip'] = packet[scapy.IP].dst
            analysis['protocol'] = packet[scapy.IP].proto
        
        # Check for sensitive data in payload
        if packet.haslayer(scapy.Raw):
            payload = packet[scapy.Raw].load.decode('utf-8', errors='ignore')
            analysis['payload'] = payload[:100]  # First 100 chars
            
            # Check for suspicious patterns
            for pattern in self.suspicious_patterns:
                if re.search(pattern, payload):
                    analysis['alerts'].append({
                        'type': 'SENSITIVE_DATA_DETECTED',
                        'pattern': pattern,
                        'severity': 'HIGH'
                    })
        
        # Check for unusual traffic patterns
        self.check_traffic_anomalies(analysis)
        
        if analysis['alerts']:
            self.log_security_event(analysis)
        
        return analysis
    
    def check_traffic_anomalies(self, analysis):
        """Check for unusual traffic patterns"""
        src_ip = analysis['src_ip']
        dst_ip = analysis['dst_ip']
        
        # Check for internal to external data transfer
        if (src_ip and dst_ip and 
            src_ip.startswith('10.0.') and 
            not dst_ip.startswith('10.0.')):
            analysis['alerts'].append({
                'type': 'INTERNAL_TO_EXTERNAL_TRANSFER',
                'severity': 'MEDIUM'
            })
        
        # Check for database access from DMZ
        if (src_ip and dst_ip and 
            src_ip.startswith('10.0.1.') and  # DMZ
            dst_ip.startswith('10.0.3.')):    # Database zone
            analysis['alerts'].append({
                'type': 'UNAUTHORIZED_DATABASE_ACCESS',
                'severity': 'CRITICAL'
            })
    
    def log_security_event(self, analysis):
        """Log security events for SIEM integration"""
        event = {
            'event_type': 'NETWORK_SECURITY_ALERT',
            'timestamp': analysis['timestamp'],
            'source_ip': analysis['src_ip'],
            'destination_ip': analysis['dst_ip'],
            'alerts': analysis['alerts'],
            'compliance_impact': 'HIPAA_VIOLATION_RISK'
        }
        
        self.logger.warning(f"Security Alert: {json.dumps(event)}")
        
        # Send to SIEM system
        self.send_to_siem(event)
    
    def send_to_siem(self, event):
        """Send security events to SIEM system"""
        # Implementation for SIEM integration
        pass
    
    def start_monitoring(self, interface='eth0'):
        """Start network monitoring"""
        self.logger.info(f"Starting network monitoring on interface {interface}")
        scapy.sniff(iface=interface, prn=self.analyze_packet, store=0)

if __name__ == "__main__":
    monitor = HealthcareNetworkMonitor()
    monitor.start_monitoring()
```

## 📚 Best Practices Summary

### Network Architecture
1. **Network Segmentation**: Implement proper network zones and VLANs
2. **Defense in Depth**: Use multiple layers of security controls
3. **Least Privilege**: Apply principle of least privilege to network access
4. **Zero Trust**: Implement zero trust network architecture
5. **Compliance Zones**: Separate PCI DSS and HIPAA compliant networks

### Firewall Management
1. **Default Deny**: Implement default deny policies
2. **Regular Reviews**: Regularly review and update firewall rules
3. **Logging**: Enable comprehensive logging for all firewall events
4. **Change Management**: Implement proper change management procedures
5. **Testing**: Regularly test firewall configurations

### Monitoring and Detection
1. **Real-time Monitoring**: Implement real-time network monitoring
2. **Anomaly Detection**: Use behavioral analysis for threat detection
3. **SIEM Integration**: Integrate with Security Information and Event Management
4. **Incident Response**: Have automated incident response procedures
5. **Compliance Reporting**: Generate regular compliance reports

## 🔗 Additional Resources

- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
- [SANS Network Security](https://www.sans.org/white-papers/network-security/)
- [Cisco Network Security Best Practices](https://www.cisco.com/c/en/us/solutions/enterprise-networks/network-security.html)
- [OWASP Network Security](https://owasp.org/www-community/controls/Network_Security)
- [Healthcare Network Security Guidelines](https://www.hhs.gov/hipaa/for-professionals/security/guidance/cybersecurity/index.html)

---

**Next**: [Authentication Fundamentals](10-auth-fundamentals.md) | **Previous**: [Container Security](08-container-security.md)

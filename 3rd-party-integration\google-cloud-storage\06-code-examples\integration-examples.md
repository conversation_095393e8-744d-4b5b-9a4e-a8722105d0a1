# Healthcare Platform Integration Examples

## Overview

This guide provides complete integration examples for implementing Google Cloud Storage in your healthcare platform, including doctor availability, patient consultations, and payment processing workflows.

## Complete Healthcare Platform Integration

### 1. Healthcare Platform Storage Service

```python
from google.cloud import storage
from google.cloud import kms
from google.cloud import logging
from datetime import datetime, timedelta
import json
import hashlib
import os
import uuid

class HealthcarePlatformStorage:
    def __init__(self, project_id, config):
        self.project_id = project_id
        self.client = storage.Client(project=project_id)
        self.kms_client = kms.KeyManagementServiceClient()
        self.logging_client = logging.Client()
        
        # Configuration
        self.buckets = {
            'patient_records': config['buckets']['patient_records'],
            'consultation_files': config['buckets']['consultation_files'],
            'doctor_documents': config['buckets']['doctor_documents'],
            'platform_assets': config['buckets']['platform_assets']
        }
        
        self.kms_key_ring = config['kms']['key_ring']
        self.kms_location = config['kms']['location']
        
        # Initialize buckets
        self._initialize_buckets()
    
    def _initialize_buckets(self):
        """Initialize all required buckets with HIPAA compliance"""
        
        for bucket_type, bucket_name in self.buckets.items():
            try:
                bucket = self.client.bucket(bucket_name)
                if not bucket.exists():
                    self._create_hipaa_compliant_bucket(bucket_name, bucket_type)
                else:
                    self._verify_bucket_compliance(bucket_name)
            except Exception as e:
                print(f"Error initializing bucket {bucket_name}: {str(e)}")
    
    def _create_hipaa_compliant_bucket(self, bucket_name, bucket_type):
        """Create HIPAA-compliant bucket with proper configuration"""
        
        bucket = self.client.bucket(bucket_name)
        bucket.location = "us-central1"  # HIPAA-compliant region
        bucket.storage_class = "STANDARD"
        
        # Enable uniform bucket-level access
        bucket.iam_configuration.uniform_bucket_level_access_enabled = True
        
        # Set default encryption key
        kms_key_name = f"projects/{self.project_id}/locations/{self.kms_location}/keyRings/{self.kms_key_ring}/cryptoKeys/{bucket_type}-key"
        bucket.default_kms_key_name = kms_key_name
        
        # Set lifecycle rules based on bucket type
        lifecycle_rules = self._get_lifecycle_rules(bucket_type)
        bucket.lifecycle_rules = lifecycle_rules
        
        # Create bucket
        bucket.create()
        
        # Set IAM policies
        self._set_bucket_iam_policies(bucket, bucket_type)
        
        print(f"Created HIPAA-compliant bucket: {bucket_name}")
    
    def _get_lifecycle_rules(self, bucket_type):
        """Get lifecycle rules based on bucket type and healthcare requirements"""
        
        rules = {
            'patient_records': [
                {
                    'action': {'type': 'SetStorageClass', 'storageClass': 'NEARLINE'},
                    'condition': {'age': 90}
                },
                {
                    'action': {'type': 'SetStorageClass', 'storageClass': 'COLDLINE'},
                    'condition': {'age': 365}
                },
                {
                    'action': {'type': 'SetStorageClass', 'storageClass': 'ARCHIVE'},
                    'condition': {'age': 2555}  # 7 years
                }
            ],
            'consultation_files': [
                {
                    'action': {'type': 'SetStorageClass', 'storageClass': 'NEARLINE'},
                    'condition': {'age': 30}
                },
                {
                    'action': {'type': 'SetStorageClass', 'storageClass': 'COLDLINE'},
                    'condition': {'age': 365}
                }
            ],
            'doctor_documents': [
                {
                    'action': {'type': 'SetStorageClass', 'storageClass': 'NEARLINE'},
                    'condition': {'age': 180}
                }
            ],
            'platform_assets': [
                {
                    'action': {'type': 'SetStorageClass', 'storageClass': 'NEARLINE'},
                    'condition': {'age': 365}
                }
            ]
        }
        
        return rules.get(bucket_type, [])

# Doctor Availability Integration
class DoctorAvailabilityStorage:
    def __init__(self, platform_storage):
        self.platform_storage = platform_storage
        self.bucket = platform_storage.client.bucket(
            platform_storage.buckets['doctor_documents']
        )
    
    def upload_doctor_credentials(self, doctor_id, credential_type, file_path, metadata=None):
        """Upload doctor credentials and certifications"""
        
        try:
            # Validate doctor credential file
            if not self._validate_credential_file(file_path, credential_type):
                return {'success': False, 'error': 'Invalid credential file'}
            
            # Generate secure blob name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_extension = os.path.splitext(file_path)[1]
            blob_name = f"doctors/{doctor_id}/credentials/{credential_type}/{timestamp}{file_extension}"
            
            blob = self.bucket.blob(blob_name)
            
            # Set metadata
            blob.metadata = {
                'doctor_id': doctor_id,
                'credential_type': credential_type,
                'upload_timestamp': datetime.now().isoformat(),
                'verification_status': 'pending',
                'hipaa_compliant': 'true',
                **(metadata or {})
            }
            
            # Upload file
            blob.upload_from_filename(file_path)
            
            # Log upload
            self._log_credential_upload(doctor_id, credential_type, blob_name)
            
            return {
                'success': True,
                'blob_name': blob_name,
                'verification_required': True
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def verify_doctor_credentials(self, doctor_id, blob_name, verified_by):
        """Mark doctor credentials as verified"""
        
        try:
            blob = self.bucket.blob(blob_name)
            blob.reload()
            
            # Update metadata
            if blob.metadata:
                blob.metadata['verification_status'] = 'verified'
                blob.metadata['verified_by'] = verified_by
                blob.metadata['verification_date'] = datetime.now().isoformat()
                blob.patch()
            
            # Log verification
            self._log_credential_verification(doctor_id, blob_name, verified_by)
            
            return {'success': True, 'verified': True}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_doctor_availability_documents(self, doctor_id):
        """Get all availability-related documents for a doctor"""
        
        prefix = f"doctors/{doctor_id}/"
        blobs = self.bucket.list_blobs(prefix=prefix)
        
        documents = []
        for blob in blobs:
            blob.reload()
            if blob.metadata:
                documents.append({
                    'name': blob.name,
                    'credential_type': blob.metadata.get('credential_type'),
                    'verification_status': blob.metadata.get('verification_status'),
                    'upload_date': blob.metadata.get('upload_timestamp'),
                    'size': blob.size
                })
        
        return {'success': True, 'documents': documents}

# Patient Consultation Integration
class ConsultationStorageIntegration:
    def __init__(self, platform_storage):
        self.platform_storage = platform_storage
        self.bucket = platform_storage.client.bucket(
            platform_storage.buckets['consultation_files']
        )
    
    def create_consultation_session(self, consultation_id, patient_id, doctor_id, 
                                  consultation_type='video'):
        """Create storage workspace for consultation session"""
        
        session_data = {
            'consultation_id': consultation_id,
            'patient_id': patient_id,
            'doctor_id': doctor_id,
            'consultation_type': consultation_type,
            'created_at': datetime.now().isoformat(),
            'status': 'active'
        }
        
        # Create session metadata file
        session_blob = self.bucket.blob(f"consultations/{consultation_id}/session_info.json")
        session_blob.metadata = session_data
        session_blob.upload_from_string(
            json.dumps(session_data, indent=2),
            content_type='application/json'
        )
        
        # Create folder structure
        folders = [
            f"consultations/{consultation_id}/pre_consultation/",
            f"consultations/{consultation_id}/during_consultation/",
            f"consultations/{consultation_id}/post_consultation/",
            f"consultations/{consultation_id}/recordings/",
            f"consultations/{consultation_id}/shared_files/"
        ]
        
        for folder in folders:
            placeholder = self.bucket.blob(f"{folder}.placeholder")
            placeholder.upload_from_string("", content_type='text/plain')
        
        return {'success': True, 'consultation_id': consultation_id}
    
    def upload_consultation_file(self, consultation_id, file_path, file_category, 
                               uploaded_by, user_role):
        """Upload file during consultation with real-time access"""
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_extension = os.path.splitext(file_path)[1]
            blob_name = f"consultations/{consultation_id}/{file_category}/{timestamp}_{uuid.uuid4().hex[:8]}{file_extension}"
            
            blob = self.bucket.blob(blob_name)
            blob.metadata = {
                'consultation_id': consultation_id,
                'file_category': file_category,
                'uploaded_by': uploaded_by,
                'user_role': user_role,
                'upload_timestamp': datetime.now().isoformat(),
                'original_filename': os.path.basename(file_path)
            }
            
            blob.upload_from_filename(file_path)
            
            # Generate immediate access URLs for consultation participants
            access_urls = self._generate_participant_access_urls(
                consultation_id, blob_name
            )
            
            return {
                'success': True,
                'blob_name': blob_name,
                'access_urls': access_urls
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _generate_participant_access_urls(self, consultation_id, blob_name):
        """Generate access URLs for consultation participants"""
        
        # Get consultation participants from session info
        session_blob = self.bucket.blob(f"consultations/{consultation_id}/session_info.json")
        session_data = json.loads(session_blob.download_as_text())
        
        participants = [
            {'id': session_data['patient_id'], 'role': 'patient'},
            {'id': session_data['doctor_id'], 'role': 'doctor'}
        ]
        
        access_urls = {}
        blob = self.bucket.blob(blob_name)
        
        for participant in participants:
            expiration = datetime.utcnow() + timedelta(hours=2)
            signed_url = blob.generate_signed_url(
                version="v4",
                expiration=expiration,
                method="GET"
            )
            access_urls[participant['id']] = signed_url
        
        return access_urls

# Payment Integration with File Storage
class PaymentDocumentStorage:
    def __init__(self, platform_storage):
        self.platform_storage = platform_storage
        self.bucket = platform_storage.client.bucket(
            platform_storage.buckets['patient_records']
        )
    
    def store_payment_receipt(self, patient_id, consultation_id, payment_data, 
                            receipt_file=None):
        """Store payment receipt and related documents"""
        
        try:
            # Create payment record
            payment_record = {
                'patient_id': patient_id,
                'consultation_id': consultation_id,
                'payment_amount': payment_data['amount'],
                'payment_method': payment_data['method'],
                'stripe_payment_id': payment_data.get('stripe_payment_id'),
                'platform_commission': payment_data.get('commission', '2%'),
                'payment_timestamp': datetime.now().isoformat(),
                'status': 'completed'
            }
            
            # Store payment record
            record_blob_name = f"patients/{patient_id}/payments/{consultation_id}/payment_record.json"
            record_blob = self.bucket.blob(record_blob_name)
            record_blob.metadata = {
                'patient_id': patient_id,
                'consultation_id': consultation_id,
                'document_type': 'payment_record',
                'created_at': datetime.now().isoformat()
            }
            
            record_blob.upload_from_string(
                json.dumps(payment_record, indent=2),
                content_type='application/json'
            )
            
            # Store receipt file if provided
            receipt_blob_name = None
            if receipt_file:
                receipt_blob_name = f"patients/{patient_id}/payments/{consultation_id}/receipt.pdf"
                receipt_blob = self.bucket.blob(receipt_blob_name)
                receipt_blob.upload_from_filename(receipt_file)
            
            return {
                'success': True,
                'payment_record': record_blob_name,
                'receipt_file': receipt_blob_name
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

# Complete Platform Integration Example
class HealthcarePlatformIntegration:
    def __init__(self, project_id):
        # Configuration
        config = {
            'buckets': {
                'patient_records': 'healthcare-patient-records',
                'consultation_files': 'healthcare-consultation-files',
                'doctor_documents': 'healthcare-doctor-documents',
                'platform_assets': 'healthcare-platform-assets'
            },
            'kms': {
                'key_ring': 'healthcare-keys',
                'location': 'us-central1'
            }
        }
        
        # Initialize storage services
        self.storage = HealthcarePlatformStorage(project_id, config)
        self.doctor_storage = DoctorAvailabilityStorage(self.storage)
        self.consultation_storage = ConsultationStorageIntegration(self.storage)
        self.payment_storage = PaymentDocumentStorage(self.storage)
    
    def complete_consultation_workflow(self, consultation_data):
        """Complete end-to-end consultation workflow with file management"""
        
        consultation_id = consultation_data['consultation_id']
        patient_id = consultation_data['patient_id']
        doctor_id = consultation_data['doctor_id']
        
        workflow_results = {}
        
        try:
            # 1. Create consultation session
            session_result = self.consultation_storage.create_consultation_session(
                consultation_id, patient_id, doctor_id
            )
            workflow_results['session_created'] = session_result
            
            # 2. Handle file uploads during consultation
            if 'uploaded_files' in consultation_data:
                upload_results = []
                for file_info in consultation_data['uploaded_files']:
                    upload_result = self.consultation_storage.upload_consultation_file(
                        consultation_id=consultation_id,
                        file_path=file_info['file_path'],
                        file_category=file_info['category'],
                        uploaded_by=file_info['uploaded_by'],
                        user_role=file_info['user_role']
                    )
                    upload_results.append(upload_result)
                workflow_results['file_uploads'] = upload_results
            
            # 3. Process payment and store receipt
            if 'payment_data' in consultation_data:
                payment_result = self.payment_storage.store_payment_receipt(
                    patient_id=patient_id,
                    consultation_id=consultation_id,
                    payment_data=consultation_data['payment_data'],
                    receipt_file=consultation_data.get('receipt_file')
                )
                workflow_results['payment_processed'] = payment_result
            
            # 4. Generate consultation summary
            summary_result = self._generate_consultation_summary(
                consultation_id, consultation_data
            )
            workflow_results['summary_generated'] = summary_result
            
            return {
                'success': True,
                'consultation_id': consultation_id,
                'workflow_results': workflow_results
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Workflow failed: {str(e)}",
                'partial_results': workflow_results
            }
    
    def _generate_consultation_summary(self, consultation_id, consultation_data):
        """Generate comprehensive consultation summary"""
        
        summary = {
            'consultation_id': consultation_id,
            'patient_id': consultation_data['patient_id'],
            'doctor_id': consultation_data['doctor_id'],
            'consultation_date': consultation_data.get('date', datetime.now().isoformat()),
            'consultation_type': consultation_data.get('type', 'video'),
            'duration_minutes': consultation_data.get('duration', 0),
            'files_uploaded': len(consultation_data.get('uploaded_files', [])),
            'payment_processed': 'payment_data' in consultation_data,
            'summary_generated_at': datetime.now().isoformat()
        }
        
        # Store summary
        summary_blob = self.consultation_storage.bucket.blob(
            f"consultations/{consultation_id}/consultation_summary.json"
        )
        summary_blob.upload_from_string(
            json.dumps(summary, indent=2),
            content_type='application/json'
        )
        
        return {'success': True, 'summary_file': summary_blob.name}

# Usage Example
if __name__ == "__main__":
    # Initialize platform
    platform = HealthcarePlatformIntegration("my-healthcare-project")
    
    # Example consultation workflow
    consultation_data = {
        'consultation_id': 'cons_20240617_001',
        'patient_id': 'patient_12345',
        'doctor_id': 'dr_smith_001',
        'date': '2024-06-17T14:30:00Z',
        'type': 'video',
        'duration': 30,
        'uploaded_files': [
            {
                'file_path': '/tmp/patient_form.pdf',
                'category': 'pre_consultation',
                'uploaded_by': 'patient_12345',
                'user_role': 'patient'
            },
            {
                'file_path': '/tmp/prescription.pdf',
                'category': 'post_consultation',
                'uploaded_by': 'dr_smith_001',
                'user_role': 'doctor'
            }
        ],
        'payment_data': {
            'amount': 150.00,
            'method': 'stripe',
            'stripe_payment_id': 'pi_1234567890',
            'commission': '2%'
        },
        'receipt_file': '/tmp/payment_receipt.pdf'
    }
    
    # Execute complete workflow
    result = platform.complete_consultation_workflow(consultation_data)
    
    if result['success']:
        print(f"Consultation workflow completed successfully: {result['consultation_id']}")
        print(f"Workflow results: {json.dumps(result['workflow_results'], indent=2)}")
    else:
        print(f"Workflow failed: {result['error']}")
```

## Environment Configuration

```python
# config/storage_config.py
import os

STORAGE_CONFIG = {
    'project_id': os.getenv('GOOGLE_CLOUD_PROJECT_ID'),
    'buckets': {
        'patient_records': os.getenv('PATIENT_RECORDS_BUCKET', 'healthcare-patient-records'),
        'consultation_files': os.getenv('CONSULTATION_FILES_BUCKET', 'healthcare-consultation-files'),
        'doctor_documents': os.getenv('DOCTOR_DOCUMENTS_BUCKET', 'healthcare-doctor-documents'),
        'platform_assets': os.getenv('PLATFORM_ASSETS_BUCKET', 'healthcare-platform-assets')
    },
    'kms': {
        'key_ring': os.getenv('KMS_KEY_RING', 'healthcare-keys'),
        'location': os.getenv('KMS_LOCATION', 'us-central1')
    },
    'regions': {
        'primary': os.getenv('PRIMARY_REGION', 'us-central1'),
        'backup': os.getenv('BACKUP_REGION', 'us-east1')
    }
}

# Environment variables to set
"""
export GOOGLE_CLOUD_PROJECT_ID="your-healthcare-project"
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account-key.json"
export PATIENT_RECORDS_BUCKET="healthcare-patient-records"
export CONSULTATION_FILES_BUCKET="healthcare-consultation-files"
export DOCTOR_DOCUMENTS_BUCKET="healthcare-doctor-documents"
export PLATFORM_ASSETS_BUCKET="healthcare-platform-assets"
export KMS_KEY_RING="healthcare-keys"
export KMS_LOCATION="us-central1"
"""
```

## Next Steps

1. **[Best Practices](../07-best-practices/)** - Security and performance optimization
2. **[Monitoring & Alerting](../07-best-practices/monitoring.md)** - Operational excellence
3. **[Cost Management](../07-best-practices/cost-management.md)** - Budget optimization

---

*This integration example provides a complete foundation for implementing Google Cloud Storage in your healthcare platform with doctor availability, consultation management, and payment processing.*

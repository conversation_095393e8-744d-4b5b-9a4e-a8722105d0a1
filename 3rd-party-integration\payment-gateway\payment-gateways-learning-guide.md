# Comprehensive Payment Gateways Learning Guide

## Focus: Stripe and Modern Payment Processing

---

## 📚 Learning Path Overview

This guide follows a progressive structure from fundamental concepts to advanced implementation scenarios. Each section builds upon previous knowledge.

### Phase 1: Foundation (Week 1-2)

- Payment Gateway Fundamentals
- Payment Processing Flow
- Core Terminology

### Phase 2: Stripe Deep Dive (Week 3-4)

- Stripe Architecture & APIs
- Account Types & Structure
- Payment Methods

### Phase 3: Legal & Compliance (Week 5)

- PCI DSS, KYC, AML
- Regional Regulations
- Data Protection

### Phase 4: Implementation Patterns (Week 6-8)

- Common Use Cases
- Advanced Features
- Real-world Scenarios

---

## 🏗️ Phase 1: Foundation

### 1.1 What Are Payment Gateways?

**Definition**: A payment gateway is a technology that captures and transfers payment data from customers to acquiring banks for processing.

**Key Functions**:

- **Authorization**: Verifying payment method validity
- **Authentication**: Confirming customer identity
- **Capture**: Actually charging the payment method
- **Settlement**: Transferring funds to merchant accounts

**Role in E-commerce Ecosystem**:

```
Customer → Payment Gateway → Payment Processor → Card Network → Issuing Bank
    ↓                                                                    ↑
Merchant ← Acquiring Bank ← Payment Processor ← Card Network ← Issuing Bank
```

### 1.2 End-to-End Payment Processing Flow

**Step-by-Step Process**:

1. **Customer Initiates Payment**

   - Enters payment details on checkout
   - Clicks "Pay" button

2. **Payment Gateway Receives Data**

   - Encrypts sensitive information
   - Validates format and basic checks

3. **Authorization Request**

   - Gateway sends to payment processor
   - Processor routes to card network (Visa/Mastercard)
   - Card network contacts issuing bank

4. **Authorization Response**

   - Bank approves/declines transaction
   - Response travels back through chain
   - Gateway receives final decision

5. **Capture & Settlement**
   - Approved transactions are "captured"
   - Funds are transferred (usually 1-3 business days)
   - Merchant receives payment minus fees

**Timeline**:

- Authorization: 2-5 seconds
- Settlement: 1-3 business days
- Dispute window: 60-120 days

### 1.3 Core Terminology

**Essential Terms**:

- **Merchant**: Business accepting payments
- **Acquirer**: Bank that processes merchant payments
- **Issuer**: Bank that issued customer's card
- **Authorization**: Permission to charge
- **Capture**: Actually charging the card
- **Settlement**: Transfer of funds
- **Chargeback**: Customer dispute of transaction
- **3D Secure**: Additional authentication layer
- **PCI DSS**: Security standards for card data
- **Interchange**: Fees between banks
- **Processing Fee**: Gateway/processor charges

---

## 🔷 Phase 2: Stripe Deep Dive

### 2.1 Stripe's Business Model & Value Proposition

**Business Model**:

- **Revenue**: Transaction fees (2.9% + 30¢ for online payments)
- **Volume-based pricing**: Lower rates for high-volume merchants
- **Additional services**: Billing, Connect, Terminal, etc.

**Core Value Propositions**:

1. **Developer Experience**: Clean APIs, excellent documentation
2. **Global Reach**: 46+ countries, 135+ currencies
3. **Compliance**: PCI Level 1 certified
4. **Innovation**: Continuous feature development
5. **Reliability**: 99.99% uptime SLA

**Problems Stripe Solves**:

- Complex payment integration → Simple API calls
- PCI compliance burden → Stripe handles security
- Global expansion → Built-in international support
- Multiple payment methods → Unified interface
- Fraud management → Machine learning protection

### 2.2 Stripe Architecture & Core APIs

**Key API Concepts**:

**Payment Intents API** (Recommended):

```javascript
// Create a Payment Intent
const paymentIntent = await stripe.paymentIntents.create({
  amount: 2000, // $20.00
  currency: "usd",
  metadata: { order_id: "12345" },
});
```

**Charges API** (Legacy):

```javascript
// Direct charge (not recommended for new integrations)
const charge = await stripe.charges.create({
  amount: 2000,
  currency: "usd",
  source: "tok_visa",
});
```

**Key Objects**:

- **Customer**: Represents your customer
- **Payment Method**: Credit card, bank account, etc.
- **Payment Intent**: Represents intent to collect payment
- **Subscription**: Recurring billing setup
- **Invoice**: Bill sent to customer

### 2.3 Account Types & Structure

**Standard Accounts**:

- Direct relationship with Stripe
- Full dashboard access
- Merchant handles onboarding
- Best for: Single businesses

**Express Accounts**:

- Simplified onboarding
- Limited dashboard access
- Platform handles some compliance
- Best for: Marketplaces with simple needs

**Custom Accounts**:

- Platform controls entire experience
- No Stripe branding
- Platform handles all compliance
- Best for: Complex marketplaces

**Comparison Table**:
| Feature | Standard | Express | Custom |
|---------|----------|---------|---------|
| Onboarding | Self-service | Simplified | Platform-controlled |
| Dashboard | Full access | Limited | None |
| Branding | Stripe | Mixed | Platform |
| Compliance | Self | Shared | Platform |

---

## ⚖️ Phase 3: Legal & Compliance

### 3.1 PCI DSS Compliance

**PCI DSS Levels**:

- **Level 1**: 6M+ transactions/year
- **Level 2**: 1-6M transactions/year
- **Level 3**: 20K-1M e-commerce transactions/year
- **Level 4**: <20K e-commerce transactions/year

**Key Requirements**:

1. Secure network and systems
2. Protect cardholder data
3. Maintain vulnerability management
4. Implement access controls
5. Monitor and test networks
6. Maintain information security policy

**Stripe's Role**:

- Stripe is PCI Level 1 compliant
- Using Stripe.js keeps you out of PCI scope
- Never store card data on your servers

### 3.2 Regional Regulations

**United States**:

- **Durbin Amendment**: Debit card interchange caps
- **State regulations**: Vary by state
- **CCPA**: California privacy law

**European Union**:

- **PSD2**: Strong Customer Authentication (SCA)
- **GDPR**: Data protection requirements
- **Open Banking**: Account-to-account payments

**Asia-Pacific**:

- **Japan**: J-Debit, konbini payments
- **China**: Alipay, WeChat Pay integration
- **India**: UPI, RuPay cards

### 3.3 KYC & AML Requirements

**Know Your Customer (KYC)**:

- Identity verification
- Business verification
- Beneficial ownership disclosure
- Ongoing monitoring

**Anti-Money Laundering (AML)**:

- Transaction monitoring
- Suspicious activity reporting
- Customer due diligence
- Record keeping requirements

---

## 🛠️ Phase 4: Implementation Patterns

### 4.1 Common Use Cases

**E-commerce Checkout**:

```javascript
// Basic checkout flow
const { error } = await stripe.confirmCardPayment(clientSecret, {
  payment_method: {
    card: cardElement,
    billing_details: { name: "Customer Name" },
  },
});
```

**Subscription Billing**:

```javascript
// Create subscription
const subscription = await stripe.subscriptions.create({
  customer: "cus_customer_id",
  items: [{ price: "price_monthly_plan" }],
  payment_behavior: "default_incomplete",
  expand: ["latest_invoice.payment_intent"],
});
```

**Marketplace Payments** (Your Use Case):

```javascript
// Create connected account
const account = await stripe.accounts.create({
  type: "express",
  country: "US",
  email: "<EMAIL>",
});

// Create payment with application fee
const paymentIntent = await stripe.paymentIntents.create({
  amount: 10000, // $100 consultation
  currency: "usd",
  application_fee_amount: 200, // $2 platform fee (2%)
  transfer_data: {
    destination: "acct_doctor_account",
  },
});
```

### 4.2 Advanced Features

**Webhooks**:

```javascript
// Handle successful payment
app.post("/webhook", (req, res) => {
  const event = req.body;

  switch (event.type) {
    case "payment_intent.succeeded":
      // Create appointment in your system
      createAppointment(event.data.object);
      break;
    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.json({ received: true });
});
```

**Fraud Prevention**:

- Radar for fraud detection
- 3D Secure for additional authentication
- Machine learning risk scoring
- Custom rules and blocklists

---

## 🏥 Your Healthcare AI Platform Analysis

Based on your description, your use case perfectly matches the **Marketplace Payment Pattern** with these characteristics:

### Use Case Classification: **Multi-Party Marketplace**

**Your Business Model**:

- **Platform**: Healthcare AI platform
- **Sellers**: Doctors offering consultations
- **Buyers**: Patients booking appointments
- **Commission**: 2% platform fee

### Recommended Stripe Architecture:

**1. Stripe Connect with Express Accounts**

```javascript
// Doctor onboarding flow
const accountLink = await stripe.accountLinks.create({
  account: doctorAccountId,
  refresh_url: "https://yourplatform.com/reauth",
  return_url: "https://yourplatform.com/return",
  type: "account_onboarding",
});
```

**2. Payment Flow**:

```javascript
// When patient books appointment
const paymentIntent = await stripe.paymentIntents.create({
  amount: consultationFee * 100, // Convert to cents
  currency: "usd",
  application_fee_amount: Math.round(consultationFee * 100 * 0.02), // 2%
  transfer_data: {
    destination: doctorStripeAccountId,
  },
  metadata: {
    appointment_id: appointmentId,
    doctor_id: doctorId,
    patient_id: patientId,
  },
});
```

**3. Webhook Handling**:

```javascript
// Only create appointment after successful payment
case 'payment_intent.succeeded':
  const appointmentData = {
    doctor_id: event.data.object.metadata.doctor_id,
    patient_id: event.data.object.metadata.patient_id,
    amount: event.data.object.amount,
    status: 'confirmed'
  };
  await createAppointment(appointmentData);
  break;
```

### Key Considerations for Your Platform:

**1. Account Setup Flow**:

- Use Express accounts for simplified doctor onboarding
- Collect minimal required information
- Handle verification asynchronously

**2. Payment Timing**:

- Charge patients immediately upon booking
- Use `payment_intent.succeeded` webhook to confirm appointment
- Consider hold/capture for cancellation policies

**3. Compliance Considerations**:

- Healthcare data privacy (HIPAA if US)
- Medical service regulations
- International doctor licensing verification

**4. User Experience**:

- Clear fee disclosure to both parties
- Automated payout schedules for doctors
- Dispute handling for medical consultations

This matches the **"Marketplace and Multi-vendor Platforms"** use case perfectly, with healthcare-specific considerations around compliance and user trust.

---

## 🏥 Detailed Implementation Guide for Your Healthcare Platform

### Step-by-Step Implementation Plan

#### Phase 1: Stripe Connect Setup

**1. Create Platform Account**:

```javascript
// In your Stripe dashboard, enable Connect
// Get your platform's client_id from Connect settings
const STRIPE_CLIENT_ID = "ca_xxxxx"; // Your Connect client ID
```

**2. Doctor Account Creation Flow**:

```javascript
// Backend: Create Express account for doctor
async function createDoctorStripeAccount(doctorData) {
  try {
    const account = await stripe.accounts.create({
      type: "express",
      country: doctorData.country || "US",
      email: doctorData.email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: "individual", // or 'company' for clinics
      individual: {
        first_name: doctorData.firstName,
        last_name: doctorData.lastName,
        email: doctorData.email,
        phone: doctorData.phone,
      },
      metadata: {
        doctor_id: doctorData.id,
        platform: "healthcare_ai",
      },
    });

    // Save account ID to your database
    await updateDoctorStripeAccount(doctorData.id, account.id);

    return account;
  } catch (error) {
    console.error("Error creating Stripe account:", error);
    throw error;
  }
}
```

**3. Onboarding Link Generation**:

```javascript
async function generateOnboardingLink(doctorId) {
  const doctor = await getDoctorById(doctorId);

  const accountLink = await stripe.accountLinks.create({
    account: doctor.stripe_account_id,
    refresh_url: `${process.env.FRONTEND_URL}/doctor/stripe-refresh`,
    return_url: `${process.env.FRONTEND_URL}/doctor/stripe-success`,
    type: "account_onboarding",
  });

  return accountLink.url;
}
```

#### Phase 2: Payment Flow Implementation

**1. Appointment Booking with Payment**:

```javascript
// Frontend: Booking form submission
async function bookAppointment(appointmentData) {
  try {
    // Step 1: Create payment intent on backend
    const response = await fetch("/api/create-payment-intent", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        doctor_id: appointmentData.doctorId,
        appointment_time: appointmentData.time,
        consultation_fee: appointmentData.fee,
        patient_id: currentUser.id,
      }),
    });

    const { client_secret, payment_intent_id } = await response.json();

    // Step 2: Confirm payment with Stripe
    const { error, paymentIntent } = await stripe.confirmCardPayment(
      client_secret,
      {
        payment_method: {
          card: cardElement,
          billing_details: {
            name: currentUser.name,
            email: currentUser.email,
          },
        },
      }
    );

    if (error) {
      showError(error.message);
    } else if (paymentIntent.status === "succeeded") {
      // Payment successful - appointment will be created via webhook
      showSuccess("Payment successful! Your appointment is being confirmed.");
      redirectToAppointmentConfirmation(payment_intent_id);
    }
  } catch (error) {
    showError("Booking failed. Please try again.");
  }
}
```

**2. Backend Payment Intent Creation**:

```javascript
app.post("/api/create-payment-intent", async (req, res) => {
  try {
    const { doctor_id, appointment_time, consultation_fee, patient_id } =
      req.body;

    // Validate doctor and availability
    const doctor = await getDoctorById(doctor_id);
    if (!doctor.stripe_account_id) {
      return res.status(400).json({ error: "Doctor payment setup incomplete" });
    }

    const isAvailable = await checkDoctorAvailability(
      doctor_id,
      appointment_time
    );
    if (!isAvailable) {
      return res.status(400).json({ error: "Time slot no longer available" });
    }

    // Calculate fees
    const amount = Math.round(consultation_fee * 100); // Convert to cents
    const platformFee = Math.round(amount * 0.02); // 2% commission

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amount,
      currency: "usd",
      application_fee_amount: platformFee,
      transfer_data: {
        destination: doctor.stripe_account_id,
      },
      metadata: {
        doctor_id: doctor_id,
        patient_id: patient_id,
        appointment_time: appointment_time,
        consultation_fee: consultation_fee.toString(),
        booking_type: "consultation",
      },
    });

    // Temporarily reserve the time slot
    await reserveTimeSlot(doctor_id, appointment_time, paymentIntent.id);

    res.json({
      client_secret: paymentIntent.client_secret,
      payment_intent_id: paymentIntent.id,
    });
  } catch (error) {
    console.error("Payment intent creation failed:", error);
    res.status(500).json({ error: "Payment setup failed" });
  }
});
```

#### Phase 3: Webhook Implementation

**1. Webhook Endpoint Setup**:

```javascript
const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

app.post('/webhook', express.raw({type: 'application/json'}), (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.log(`Webhook signature verification failed.`, err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  switch (event.type) {
    case 'payment_intent.succeeded':
      await handleSuccessfulPayment(event.data.object);
      break;
    case 'payment_intent.payment_failed':
      await handleFailedPayment(event.data.object);
      break;
    case 'account.updated':
      await handleAccountUpdate(event.data.object);
      break;
    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.json({received: true});
});
```

**2. Successful Payment Handler**:

```javascript
async function handleSuccessfulPayment(paymentIntent) {
  try {
    const { doctor_id, patient_id, appointment_time, consultation_fee } =
      paymentIntent.metadata;

    // Create confirmed appointment
    const appointment = await createAppointment({
      doctor_id: parseInt(doctor_id),
      patient_id: parseInt(patient_id),
      appointment_time: new Date(appointment_time),
      consultation_fee: parseFloat(consultation_fee),
      payment_intent_id: paymentIntent.id,
      status: "confirmed",
      payment_status: "paid",
    });

    // Send confirmation emails
    await sendAppointmentConfirmation(appointment);

    // Update doctor's availability
    await markTimeSlotBooked(doctor_id, appointment_time);

    console.log(`Appointment ${appointment.id} created successfully`);
  } catch (error) {
    console.error("Error handling successful payment:", error);
    // Consider implementing retry logic or manual review queue
  }
}
```

#### Phase 4: Account Management Features

**1. Doctor Dashboard - Earnings Overview**:

```javascript
// Get doctor's earnings and payout information
async function getDoctorEarnings(doctorId) {
  const doctor = await getDoctorById(doctorId);

  if (!doctor.stripe_account_id) {
    return { error: "Stripe account not connected" };
  }

  try {
    // Get account balance
    const balance = await stripe.balance.retrieve({
      stripeAccount: doctor.stripe_account_id,
    });

    // Get recent transfers
    const transfers = await stripe.transfers.list({
      destination: doctor.stripe_account_id,
      limit: 10,
    });

    // Get upcoming payouts
    const payouts = await stripe.payouts.list({
      stripeAccount: doctor.stripe_account_id,
      limit: 5,
    });

    return {
      available_balance: balance.available,
      pending_balance: balance.pending,
      recent_transfers: transfers.data,
      upcoming_payouts: payouts.data,
    };
  } catch (error) {
    console.error("Error fetching earnings:", error);
    return { error: "Unable to fetch earnings data" };
  }
}
```

**2. Account Status Monitoring**:

```javascript
async function checkDoctorAccountStatus(doctorId) {
  const doctor = await getDoctorById(doctorId);

  if (!doctor.stripe_account_id) {
    return { status: "not_connected" };
  }

  try {
    const account = await stripe.accounts.retrieve(doctor.stripe_account_id);

    return {
      status: account.details_submitted ? "active" : "pending",
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
      requirements: account.requirements,
      verification_status: account.individual?.verification?.status,
    };
  } catch (error) {
    console.error("Error checking account status:", error);
    return { status: "error" };
  }
}
```

### Common Issues & Solutions

**1. Account Setup Issues**:

- **Problem**: Doctors abandon onboarding flow
- **Solution**: Save progress, send reminder emails, simplify required fields

**2. Payment Failures**:

- **Problem**: Cards declined, insufficient funds
- **Solution**: Clear error messages, alternative payment methods, retry logic

**3. Dispute Management**:

- **Problem**: Patients dispute medical consultation charges
- **Solution**: Clear service descriptions, appointment confirmations, evidence collection

**4. International Considerations**:

- **Problem**: Doctors in different countries
- **Solution**: Country-specific account creation, local payment methods, currency handling

### Security Best Practices

1. **Never store card data** - Use Stripe.js and Payment Elements
2. **Validate webhooks** - Always verify webhook signatures
3. **Secure metadata** - Don't include sensitive medical information
4. **Monitor transactions** - Set up alerts for unusual patterns
5. **Regular audits** - Review connected accounts and permissions

This implementation provides a robust foundation for your healthcare marketplace payment system while maintaining compliance and user trust.

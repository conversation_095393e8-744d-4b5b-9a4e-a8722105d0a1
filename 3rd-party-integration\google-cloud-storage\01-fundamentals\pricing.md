# Google Cloud Storage Pricing Structure

## Overview

Understanding GCS pricing is crucial for healthcare platforms managing large volumes of patient data. This guide provides detailed cost analysis, optimization strategies, and budgeting frameworks for healthcare applications.

## Pricing Components

### 1. Storage Costs

Storage costs vary by class and region. All prices are per GB per month.

#### US Regions (us-central1, us-east1, us-west1)
```
Standard:    $0.020/GB/month
Nearline:    $0.010/GB/month
Coldline:    $0.004/GB/month
Archive:     $0.0012/GB/month
```

#### Multi-Regional (US, EU, ASIA)
```
Standard:    $0.026/GB/month
Nearline:    $0.013/GB/month
Coldline:    $0.0065/GB/month
Archive:     $0.0025/GB/month
```

#### Healthcare Cost Example
```
1TB Patient Records (Standard): $20.48/month
1TB Historical Data (Nearline): $10.24/month
1TB Archive Data (Archive): $1.23/month
Total: $31.95/month for 3TB
```

### 2. Operation Costs

Operations are charged per 1,000 requests.

#### Class A Operations (Insert, List, Create)
```
Standard:    $0.05 per 1,000 operations
Nearline:    $0.10 per 1,000 operations
Coldline:    $0.10 per 1,000 operations
Archive:     $0.50 per 1,000 operations
```

#### Class B Operations (Get, Head, Read)
```
Standard:    $0.004 per 1,000 operations
Nearline:    $0.01 per 1,000 operations
Coldline:    $0.05 per 1,000 operations
Archive:     $0.50 per 1,000 operations
```

#### Healthcare Operation Patterns
```python
# Typical monthly operations for 1,000 patients
patient_file_uploads = 5000      # Class A operations
patient_file_downloads = 15000   # Class B operations
metadata_queries = 25000         # Class B operations

# Cost calculation for Standard storage
class_a_cost = (5000 / 1000) * 0.05 = $0.25
class_b_cost = (40000 / 1000) * 0.004 = $0.16
total_operations_cost = $0.41/month
```

### 3. Data Transfer Costs

#### Ingress (Upload to GCS)
```
From Internet: FREE
From Google Services: FREE
From other Cloud providers: FREE
```

#### Egress (Download from GCS)
```
To Google Services (same region): FREE
To Internet (first 1GB/month): FREE
To Internet (1GB - 10TB): $0.12/GB
To Internet (10TB - 150TB): $0.11/GB
To Internet (150TB+): $0.08/GB
```

#### Healthcare Egress Optimization
```python
# Calculate monthly egress costs
def calculate_egress_cost(gb_transferred):
    """Calculate egress costs with tiered pricing"""
    if gb_transferred <= 1:
        return 0
    elif gb_transferred <= 10240:  # 10TB
        return (gb_transferred - 1) * 0.12
    elif gb_transferred <= 153600:  # 150TB
        return 1228.68 + (gb_transferred - 10240) * 0.11
    else:
        return 17064.68 + (gb_transferred - 153600) * 0.08

# Example: 500GB monthly downloads
monthly_cost = calculate_egress_cost(500)
print(f"Monthly egress cost: ${monthly_cost:.2f}")
```

### 4. Retrieval Costs

Additional fees for accessing data in Nearline, Coldline, and Archive.

```
Nearline:    $0.01/GB retrieved
Coldline:    $0.02/GB retrieved
Archive:     $0.05/GB retrieved
```

## Healthcare Cost Modeling

### Patient Data Lifecycle Costing

```python
class HealthcareStorageCostModel:
    def __init__(self):
        self.storage_costs = {
            'standard': 0.020,
            'nearline': 0.010,
            'coldline': 0.004,
            'archive': 0.0012
        }
        
        self.retrieval_costs = {
            'nearline': 0.01,
            'coldline': 0.02,
            'archive': 0.05
        }
    
    def calculate_patient_storage_cost(self, patient_data_gb, months_active):
        """Calculate storage cost for patient data lifecycle"""
        total_cost = 0
        
        # Active period (Standard storage)
        if months_active <= 3:
            total_cost += patient_data_gb * self.storage_costs['standard'] * months_active
        else:
            # First 3 months in Standard
            total_cost += patient_data_gb * self.storage_costs['standard'] * 3
            
            # Next 9 months in Nearline
            nearline_months = min(months_active - 3, 9)
            total_cost += patient_data_gb * self.storage_costs['nearline'] * nearline_months
            
            # Remaining in Coldline
            if months_active > 12:
                coldline_months = months_active - 12
                total_cost += patient_data_gb * self.storage_costs['coldline'] * coldline_months
        
        return total_cost

# Example usage
model = HealthcareStorageCostModel()
cost_2_years = model.calculate_patient_storage_cost(0.5, 24)  # 500MB patient data
print(f"2-year storage cost per patient: ${cost_2_years:.2f}")
```

### Clinic-Scale Cost Projection

```python
def project_clinic_costs(num_patients, avg_data_per_patient_gb, growth_rate=0.1):
    """Project storage costs for healthcare clinic"""
    monthly_costs = []
    
    for month in range(1, 37):  # 3 years
        # Account for patient growth
        current_patients = num_patients * (1 + growth_rate) ** (month / 12)
        
        # Calculate data distribution across storage classes
        total_data = current_patients * avg_data_per_patient_gb
        
        # Data aging distribution
        standard_data = total_data * 0.3  # Recent data
        nearline_data = total_data * 0.4  # 1-12 months old
        coldline_data = total_data * 0.3  # 1+ years old
        
        # Calculate monthly cost
        monthly_cost = (
            standard_data * 0.020 +
            nearline_data * 0.010 +
            coldline_data * 0.004
        )
        
        monthly_costs.append(monthly_cost)
    
    return monthly_costs

# Example: 1000 patients, 2GB average data per patient
costs = project_clinic_costs(1000, 2.0)
print(f"Year 1 average monthly cost: ${sum(costs[:12])/12:.2f}")
print(f"Year 3 monthly cost: ${costs[-1]:.2f}")
```

## Cost Optimization Strategies

### 1. Lifecycle Management

Automatically transition data to cheaper storage classes.

```json
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "SetStorageClass", "storageClass": "NEARLINE"},
        "condition": {"age": 90, "matchesStorageClass": ["STANDARD"]}
      },
      {
        "action": {"type": "SetStorageClass", "storageClass": "COLDLINE"},
        "condition": {"age": 365, "matchesStorageClass": ["NEARLINE"]}
      },
      {
        "action": {"type": "SetStorageClass", "storageClass": "ARCHIVE"},
        "condition": {"age": 2555, "matchesStorageClass": ["COLDLINE"]}
      },
      {
        "action": {"type": "Delete"},
        "condition": {"age": 3650, "matchesStorageClass": ["ARCHIVE"]}
      }
    ]
  }
}
```

**Potential Savings**: 60-80% reduction in storage costs for aged data.

### 2. Regional Strategy

Choose regions based on compliance requirements and costs.

```python
# Cost comparison for different regions
regions_cost_comparison = {
    'us-central1': {'standard': 0.020, 'egress': 0.12},
    'us-east1': {'standard': 0.020, 'egress': 0.12},
    'europe-west1': {'standard': 0.020, 'egress': 0.12},
    'asia-southeast1': {'standard': 0.023, 'egress': 0.12}
}

def calculate_regional_costs(data_gb, monthly_egress_gb, region):
    """Calculate total monthly costs by region"""
    region_pricing = regions_cost_comparison[region]
    
    storage_cost = data_gb * region_pricing['standard']
    egress_cost = max(0, monthly_egress_gb - 1) * region_pricing['egress']
    
    return storage_cost + egress_cost

# Compare costs across regions
for region in regions_cost_comparison:
    cost = calculate_regional_costs(1000, 100, region)
    print(f"{region}: ${cost:.2f}/month")
```

### 3. Compression and Deduplication

Reduce storage costs through data optimization.

```python
import gzip
import hashlib

def upload_compressed_file(bucket_name, source_file, destination_name):
    """Upload file with compression to reduce storage costs"""
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    blob = bucket.blob(destination_name)
    
    # Compress file before upload
    with open(source_file, 'rb') as f_in:
        with gzip.open(f'{source_file}.gz', 'wb') as f_out:
            f_out.writelines(f_in)
    
    # Upload compressed file
    blob.upload_from_filename(f'{source_file}.gz')
    blob.content_encoding = 'gzip'
    blob.patch()
    
    print(f"Uploaded compressed {destination_name}")

def check_duplicate_files(bucket_name):
    """Identify duplicate files to reduce storage costs"""
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    
    file_hashes = {}
    duplicates = []
    
    for blob in bucket.list_blobs():
        # Download and hash file content
        content = blob.download_as_bytes()
        file_hash = hashlib.md5(content).hexdigest()
        
        if file_hash in file_hashes:
            duplicates.append((blob.name, file_hashes[file_hash]))
        else:
            file_hashes[file_hash] = blob.name
    
    return duplicates
```

**Potential Savings**: 30-70% reduction through compression, varies by file type.

### 4. Committed Use Discounts

Long-term commitments for predictable workloads.

```
1-year commitment: 7% discount
3-year commitment: 16% discount

Example:
Standard storage: $0.020/GB → $0.0168/GB (3-year)
Annual savings: $0.0032/GB = 16% reduction
```

### 5. CDN Integration

Reduce egress costs with Cloud CDN caching.

```python
# CDN cost comparison
def calculate_cdn_savings(monthly_requests, avg_file_size_mb):
    """Calculate potential CDN savings"""
    
    # Without CDN (direct GCS egress)
    monthly_egress_gb = (monthly_requests * avg_file_size_mb) / 1024
    direct_cost = max(0, monthly_egress_gb - 1) * 0.12
    
    # With CDN (90% cache hit rate)
    cache_hit_rate = 0.9
    cdn_egress_gb = monthly_egress_gb * (1 - cache_hit_rate)
    cdn_cost = max(0, cdn_egress_gb - 1) * 0.12
    cdn_service_cost = monthly_egress_gb * 0.08  # CDN pricing
    
    total_cdn_cost = cdn_cost + cdn_service_cost
    savings = direct_cost - total_cdn_cost
    
    return {
        'direct_cost': direct_cost,
        'cdn_cost': total_cdn_cost,
        'monthly_savings': savings,
        'savings_percentage': (savings / direct_cost) * 100 if direct_cost > 0 else 0
    }

# Example: 100,000 requests for 2MB files
savings = calculate_cdn_savings(100000, 2)
print(f"Monthly savings with CDN: ${savings['monthly_savings']:.2f}")
print(f"Savings percentage: {savings['savings_percentage']:.1f}%")
```

## Budget Planning Framework

### Monthly Budget Calculator

```python
class HealthcareBudgetCalculator:
    def __init__(self, num_patients, avg_data_per_patient_gb):
        self.num_patients = num_patients
        self.avg_data_per_patient_gb = avg_data_per_patient_gb
        
    def calculate_monthly_budget(self):
        """Calculate comprehensive monthly storage budget"""
        
        # Storage costs (with lifecycle distribution)
        total_data = self.num_patients * self.avg_data_per_patient_gb
        storage_cost = (
            total_data * 0.4 * 0.020 +  # 40% Standard
            total_data * 0.4 * 0.010 +  # 40% Nearline
            total_data * 0.2 * 0.004    # 20% Coldline
        )
        
        # Operation costs (estimated)
        monthly_uploads = self.num_patients * 10  # 10 uploads per patient
        monthly_downloads = self.num_patients * 30  # 30 downloads per patient
        operation_cost = (
            (monthly_uploads / 1000) * 0.05 +
            (monthly_downloads / 1000) * 0.004
        )
        
        # Egress costs (estimated 10% of data downloaded monthly)
        monthly_egress_gb = total_data * 0.1
        egress_cost = max(0, monthly_egress_gb - 1) * 0.12
        
        return {
            'storage': storage_cost,
            'operations': operation_cost,
            'egress': egress_cost,
            'total': storage_cost + operation_cost + egress_cost
        }

# Example budget calculation
calculator = HealthcareBudgetCalculator(5000, 1.5)  # 5000 patients, 1.5GB each
budget = calculator.calculate_monthly_budget()

print("Monthly Budget Breakdown:")
print(f"Storage: ${budget['storage']:.2f}")
print(f"Operations: ${budget['operations']:.2f}")
print(f"Egress: ${budget['egress']:.2f}")
print(f"Total: ${budget['total']:.2f}")
```

## Cost Monitoring and Alerts

### Budget Alert Setup

```bash
# Create budget alert
gcloud billing budgets create \
    --billing-account=BILLING_ACCOUNT_ID \
    --display-name="Healthcare Storage Budget" \
    --budget-amount=1000 \
    --threshold-rule=percent=50,basis=CURRENT_SPEND \
    --threshold-rule=percent=90,basis=CURRENT_SPEND \
    --all-updates-rule-pubsub-topic=projects/PROJECT_ID/topics/budget-alerts
```

### Cost Analysis Queries

```sql
-- BigQuery cost analysis
SELECT
  service.description as service,
  sku.description as sku,
  location.location as location,
  SUM(cost) as total_cost,
  SUM(usage.amount) as total_usage
FROM `PROJECT_ID.billing_export.gcp_billing_export_v1_BILLING_ACCOUNT_ID`
WHERE service.description = "Cloud Storage"
  AND usage_start_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
GROUP BY service, sku, location
ORDER BY total_cost DESC
```

## Cost Optimization Checklist

### Storage Optimization
- [ ] Lifecycle policies implemented
- [ ] Storage class analysis completed
- [ ] Compression enabled for applicable files
- [ ] Duplicate file detection implemented

### Access Optimization
- [ ] CDN configured for frequently accessed files
- [ ] Signed URLs used to reduce server egress
- [ ] Regional strategy optimized
- [ ] Caching implemented at application level

### Monitoring & Control
- [ ] Budget alerts configured
- [ ] Cost analysis dashboard created
- [ ] Regular cost reviews scheduled
- [ ] Committed use discounts evaluated

## Next Steps

1. **[GCS Deep Dive](../02-gcs-deep-dive/)** - Technical implementation details
2. **[Lifecycle Management](../02-gcs-deep-dive/lifecycle.md)** - Automated cost optimization
3. **[Healthcare Compliance](../03-healthcare-compliance/)** - Compliance cost considerations

---

*Implement these cost optimization strategies to manage your healthcare platform's storage budget effectively while maintaining performance and compliance.*

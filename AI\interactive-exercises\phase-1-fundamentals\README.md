# Phase 1: AI Fundamentals - Interactive Exercises

## 🎯 Learning Objectives

These exercises reinforce core AI concepts through hands-on coding and problem-solving activities.

## 📋 Exercise List

### Exercise 1: AI Classification Challenge
**Difficulty**: Beginner  
**Time**: 30 minutes  
**Skills**: AI concepts, classification

**Scenario**: You're building an AI system classifier. Given descriptions of various systems, classify them as AI, Not AI, or Uncertain.

```python
def classify_ai_system(description, capabilities):
    """
    Classify a system as AI, Not AI, or Uncertain
    
    Args:
        description (str): System description
        capabilities (list): List of system capabilities
    
    Returns:
        str: "AI", "Not AI", or "Uncertain"
    """
    # TODO: Implement classification logic
    ai_indicators = [
        "learning", "adaptation", "pattern recognition",
        "decision making", "natural language", "computer vision"
    ]
    
    # Your code here
    pass

# Test cases
test_cases = [
    {
        "description": "Netflix recommendation system",
        "capabilities": ["pattern recognition", "learning", "personalization"],
        "expected": "AI"
    },
    {
        "description": "Basic calculator",
        "capabilities": ["arithmetic", "computation"],
        "expected": "Not AI"
    },
    {
        "description": "Smart thermostat",
        "capabilities": ["learning", "adaptation", "scheduling"],
        "expected": "AI"
    }
]

# Auto-grader
def grade_exercise_1():
    score = 0
    for test in test_cases:
        result = classify_ai_system(test["description"], test["capabilities"])
        if result == test["expected"]:
            score += 1
    return f"Score: {score}/{len(test_cases)}"
```

### Exercise 2: Machine Learning Algorithm Selector
**Difficulty**: Intermediate  
**Time**: 45 minutes  
**Skills**: ML types, algorithm selection

```python
def select_ml_algorithm(problem_type, data_size, features, target):
    """
    Recommend the best ML algorithm for a given problem
    
    Args:
        problem_type (str): "classification", "regression", "clustering"
        data_size (int): Number of samples
        features (int): Number of features
        target (str): Target variable type
    
    Returns:
        dict: {
            "algorithm": str,
            "reasoning": str,
            "alternatives": list
        }
    """
    # TODO: Implement algorithm selection logic
    
    algorithms = {
        "classification": {
            "small_data": ["Naive Bayes", "SVM", "Decision Tree"],
            "large_data": ["Random Forest", "Gradient Boosting", "Neural Network"]
        },
        "regression": {
            "linear": ["Linear Regression", "Ridge", "Lasso"],
            "nonlinear": ["Random Forest", "SVR", "Neural Network"]
        },
        "clustering": ["K-Means", "DBSCAN", "Hierarchical"]
    }
    
    # Your implementation here
    pass

# Interactive test
def test_algorithm_selector():
    scenarios = [
        {
            "problem": "Email spam detection",
            "type": "classification",
            "data_size": 10000,
            "features": 1000,
            "target": "binary"
        },
        {
            "problem": "House price prediction",
            "type": "regression", 
            "data_size": 5000,
            "features": 20,
            "target": "continuous"
        }
    ]
    
    for scenario in scenarios:
        print(f"Scenario: {scenario['problem']}")
        recommendation = select_ml_algorithm(
            scenario["type"], 
            scenario["data_size"],
            scenario["features"], 
            scenario["target"]
        )
        print(f"Recommendation: {recommendation}")
        print("-" * 50)
```

### Exercise 3: Neural Network Architecture Designer
**Difficulty**: Advanced  
**Time**: 60 minutes  
**Skills**: Deep learning, architecture design

```python
import numpy as np

class SimpleNeuralNetwork:
    def __init__(self, layers):
        """
        Initialize a simple neural network
        
        Args:
            layers (list): List of layer sizes [input, hidden1, hidden2, ..., output]
        """
        self.layers = layers
        self.weights = []
        self.biases = []
        
        # TODO: Initialize weights and biases
        # Hint: Use random initialization
        pass
    
    def forward(self, X):
        """
        Forward propagation
        
        Args:
            X (np.array): Input data
        
        Returns:
            np.array: Output predictions
        """
        # TODO: Implement forward propagation
        # Hint: Apply weights, biases, and activation functions
        pass
    
    def sigmoid(self, x):
        """Sigmoid activation function"""
        return 1 / (1 + np.exp(-np.clip(x, -250, 250)))
    
    def predict(self, X):
        """Make predictions"""
        return self.forward(X)

# Exercise: Design a network for XOR problem
def design_xor_network():
    """
    Design a neural network that can solve the XOR problem
    
    XOR truth table:
    Input: [0,0] -> Output: 0
    Input: [0,1] -> Output: 1  
    Input: [1,0] -> Output: 1
    Input: [1,1] -> Output: 0
    """
    # TODO: Design appropriate architecture
    # Hint: XOR is not linearly separable, needs hidden layer
    
    network = SimpleNeuralNetwork([2, 4, 1])  # Example architecture
    
    # Test data
    X_test = np.array([[0,0], [0,1], [1,0], [1,1]])
    y_test = np.array([[0], [1], [1], [0]])
    
    return network, X_test, y_test

# Grading function
def grade_xor_network():
    network, X_test, y_test = design_xor_network()
    predictions = network.predict(X_test)
    
    # Check if network architecture is reasonable
    if len(network.layers) < 3:
        return "Error: XOR requires at least one hidden layer"
    
    # Note: This is a design exercise, actual training would be needed
    # for functional XOR network
    return "Architecture designed successfully"
```

### Exercise 4: AI Ethics Decision Framework
**Difficulty**: Intermediate  
**Time**: 40 minutes  
**Skills**: AI ethics, decision making

```python
def ai_ethics_evaluator(scenario, stakeholders, potential_impacts):
    """
    Evaluate the ethical implications of an AI system
    
    Args:
        scenario (str): Description of the AI application
        stakeholders (list): List of affected parties
        potential_impacts (dict): Positive and negative impacts
    
    Returns:
        dict: Ethics evaluation with recommendations
    """
    
    ethical_principles = {
        "fairness": "Does the system treat all groups fairly?",
        "transparency": "Can decisions be explained and understood?",
        "privacy": "Is personal data protected appropriately?",
        "accountability": "Is there clear responsibility for decisions?",
        "beneficence": "Does the system benefit society?",
        "autonomy": "Does it respect human agency and choice?"
    }
    
    # TODO: Implement ethics evaluation framework
    evaluation = {
        "overall_score": 0,  # 1-10 scale
        "principle_scores": {},
        "red_flags": [],
        "recommendations": [],
        "approval": "pending"  # "approved", "rejected", "needs_review"
    }
    
    # Your implementation here
    pass

# Test scenario
healthcare_ai_scenario = {
    "scenario": "AI system for medical diagnosis in emergency rooms",
    "stakeholders": ["patients", "doctors", "hospital", "insurance"],
    "potential_impacts": {
        "positive": ["faster diagnosis", "reduced errors", "cost savings"],
        "negative": ["job displacement", "bias in diagnosis", "privacy concerns"]
    }
}

def test_ethics_framework():
    result = ai_ethics_evaluator(
        healthcare_ai_scenario["scenario"],
        healthcare_ai_scenario["stakeholders"], 
        healthcare_ai_scenario["potential_impacts"]
    )
    
    print("Ethics Evaluation Results:")
    print(f"Overall Score: {result['overall_score']}/10")
    print(f"Approval Status: {result['approval']}")
    print(f"Recommendations: {result['recommendations']}")
```

### Exercise 5: AI Performance Metrics Calculator
**Difficulty**: Intermediate  
**Time**: 35 minutes  
**Skills**: Model evaluation, metrics

```python
import numpy as np

def calculate_classification_metrics(y_true, y_pred):
    """
    Calculate comprehensive classification metrics
    
    Args:
        y_true (list): True labels
        y_pred (list): Predicted labels
    
    Returns:
        dict: Dictionary of metrics
    """
    # TODO: Implement metric calculations
    
    # Convert to numpy arrays
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    
    metrics = {}
    
    # Basic metrics
    # TODO: Calculate accuracy
    metrics['accuracy'] = None
    
    # TODO: Calculate precision, recall, F1-score
    # Hint: Handle binary and multiclass cases
    metrics['precision'] = None
    metrics['recall'] = None
    metrics['f1_score'] = None
    
    # TODO: Create confusion matrix
    metrics['confusion_matrix'] = None
    
    return metrics

def calculate_regression_metrics(y_true, y_pred):
    """
    Calculate regression metrics
    
    Args:
        y_true (list): True values
        y_pred (list): Predicted values
    
    Returns:
        dict: Dictionary of metrics
    """
    # TODO: Implement regression metrics
    
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    
    metrics = {}
    
    # TODO: Calculate MSE, RMSE, MAE, R²
    metrics['mse'] = None
    metrics['rmse'] = None  
    metrics['mae'] = None
    metrics['r2_score'] = None
    
    return metrics

# Test cases
def test_metrics():
    # Classification test
    y_true_class = [1, 0, 1, 1, 0, 1, 0, 0]
    y_pred_class = [1, 0, 1, 0, 0, 1, 1, 0]
    
    class_metrics = calculate_classification_metrics(y_true_class, y_pred_class)
    print("Classification Metrics:", class_metrics)
    
    # Regression test
    y_true_reg = [3.0, 2.5, 4.0, 3.5, 2.0]
    y_pred_reg = [2.8, 2.7, 3.8, 3.2, 2.1]
    
    reg_metrics = calculate_regression_metrics(y_true_reg, y_pred_reg)
    print("Regression Metrics:", reg_metrics)
```

## 🎮 Gamification Features

### Progress Tracking
- **Exercise Completion**: Track completed exercises
- **Skill Points**: Earn points for correct solutions
- **Badges**: Unlock achievements for milestones

### Difficulty Progression
- **Beginner**: Guided exercises with hints
- **Intermediate**: Independent problem solving  
- **Advanced**: Open-ended challenges
- **Expert**: Research-level problems

### Collaborative Features
- **Code Reviews**: Peer feedback on solutions
- **Discussion Forums**: Ask questions and help others
- **Study Groups**: Form teams for complex challenges

## 📊 Assessment Rubric

### Code Quality (40%)
- Correctness of implementation
- Code readability and structure
- Proper error handling
- Following best practices

### Conceptual Understanding (35%)
- Demonstrates AI knowledge
- Correct application of concepts
- Appropriate algorithm selection
- Understanding of limitations

### Problem Solving (25%)
- Creative approach to solutions
- Handling edge cases
- Optimization considerations
- Clear reasoning and documentation

## 🚀 Next Steps

After completing Phase 1 exercises:
1. **Review solutions** and compare with provided examples
2. **Complete self-assessment** quiz
3. **Move to Phase 2** integration exercises
4. **Join study groups** for collaborative learning

---

**Ready to start?** Begin with Exercise 1: AI Classification Challenge

*Exercises are auto-graded with immediate feedback and hints available.*

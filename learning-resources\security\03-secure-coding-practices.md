# Secure Coding Practices

## 🛡️ Input Validation and Sanitization

### Comprehensive Input Validation
```python
from marshmallow import Schema, fields, validate, ValidationError
import re
from datetime import datetime

class PatientRegistrationSchema(Schema):
    """Secure patient registration with comprehensive validation"""
    
    first_name = fields.Str(
        required=True,
        validate=[
            validate.Length(min=1, max=50),
            validate.Regexp(r'^[a-zA-Z\s\-\'\.]+$', error='Invalid characters in name')
        ]
    )
    
    email = fields.Email(required=True)
    
    phone = fields.Str(
        required=True,
        validate=validate.Regexp(
            r'^\+?1?[2-9]\d{2}[2-9]\d{2}\d{4}$',
            error='Invalid phone number format'
        )
    )
    
    date_of_birth = fields.Date(
        required=True,
        validate=lambda x: x <= datetime.now().date()
    )
    
    medical_record_number = fields.Str(
        validate=[
            validate.Length(min=8, max=20),
            validate.Regexp(r'^[A-Z0-9]+$', error='Invalid MRN format')
        ]
    )

# Usage
def register_patient(request_data):
    schema = PatientRegistrationSchema()
    try:
        validated_data = schema.load(request_data)
        # Process validated data
        return create_patient(validated_data)
    except ValidationError as err:
        return {'errors': err.messages}, 400
```

### SQL Injection Prevention
```python
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker

class SecurePatientRepository:
    def __init__(self, session):
        self.session = session
    
    def get_patient_by_id(self, patient_id):
        """Secure parameterized query"""
        # Using SQLAlchemy ORM (preferred)
        return self.session.query(Patient).filter(
            Patient.id == patient_id
        ).first()
    
    def search_patients(self, search_term, limit=10):
        """Secure text search with parameterized queries"""
        # Using parameterized raw SQL when needed
        query = text("""
            SELECT * FROM patients 
            WHERE LOWER(first_name) LIKE LOWER(:search_term) 
               OR LOWER(last_name) LIKE LOWER(:search_term)
            LIMIT :limit
        """)
        
        return self.session.execute(
            query,
            {
                'search_term': f'%{search_term}%',
                'limit': limit
            }
        ).fetchall()
    
    def get_patient_appointments(self, patient_id, user_id):
        """Secure query with authorization check"""
        # Ensure user can only access their own data
        if not self.can_access_patient_data(user_id, patient_id):
            raise PermissionError("Access denied")
        
        return self.session.query(Appointment).filter(
            Appointment.patient_id == patient_id
        ).all()
```

### XSS Prevention
```javascript
// Frontend XSS prevention
class SecureRenderer {
    static escapeHtml(unsafe) {
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }
    
    static sanitizeUserContent(content) {
        // Use DOMPurify for rich content
        return DOMPurify.sanitize(content, {
            ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
            ALLOWED_ATTR: []
        });
    }
    
    static renderPatientNote(note) {
        const sanitizedNote = this.sanitizeUserContent(note.content);
        const escapedAuthor = this.escapeHtml(note.author);
        
        return `
            <div class="patient-note">
                <div class="note-content">${sanitizedNote}</div>
                <div class="note-author">By: ${escapedAuthor}</div>
            </div>
        `;
    }
}

// Backend XSS prevention with Content Security Policy
app.use((req, res, next) => {
    res.setHeader(
        'Content-Security-Policy',
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline'; " +
        "style-src 'self' 'unsafe-inline'; " +
        "img-src 'self' data: https:; " +
        "connect-src 'self'; " +
        "font-src 'self'; " +
        "object-src 'none'; " +
        "media-src 'self'; " +
        "frame-src 'none';"
    );
    next();
});
```

## 🔐 Authentication and Session Management

### Secure Password Handling
```python
import bcrypt
import secrets
import hashlib
from datetime import datetime, timedelta

class SecurePasswordManager:
    MIN_PASSWORD_LENGTH = 12
    PASSWORD_HISTORY_COUNT = 5
    
    @staticmethod
    def generate_salt():
        """Generate cryptographically secure salt"""
        return bcrypt.gensalt(rounds=12)
    
    @staticmethod
    def hash_password(password, salt=None):
        """Hash password with bcrypt"""
        if salt is None:
            salt = SecurePasswordManager.generate_salt()
        
        return bcrypt.hashpw(password.encode('utf-8'), salt)
    
    @staticmethod
    def verify_password(password, hashed):
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed)
    
    @staticmethod
    def validate_password_strength(password):
        """Validate password meets security requirements"""
        if len(password) < SecurePasswordManager.MIN_PASSWORD_LENGTH:
            raise ValueError("Password too short")
        
        checks = [
            (r'[a-z]', "lowercase letter"),
            (r'[A-Z]', "uppercase letter"),
            (r'\d', "digit"),
            (r'[!@#$%^&*(),.?":{}|<>]', "special character")
        ]
        
        for pattern, requirement in checks:
            if not re.search(pattern, password):
                raise ValueError(f"Password must contain at least one {requirement}")
    
    @staticmethod
    def generate_secure_token():
        """Generate cryptographically secure token"""
        return secrets.token_urlsafe(32)

# Secure session management
class SessionManager:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.session_timeout = timedelta(hours=1)
    
    def create_session(self, user_id, additional_data=None):
        """Create secure session"""
        session_id = secrets.token_urlsafe(32)
        session_data = {
            'user_id': user_id,
            'created_at': datetime.utcnow().isoformat(),
            'last_activity': datetime.utcnow().isoformat(),
            'ip_address': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', '')
        }
        
        if additional_data:
            session_data.update(additional_data)
        
        # Store in Redis with expiration
        self.redis.setex(
            f"session:{session_id}",
            self.session_timeout,
            json.dumps(session_data)
        )
        
        return session_id
    
    def validate_session(self, session_id):
        """Validate and refresh session"""
        session_data = self.redis.get(f"session:{session_id}")
        if not session_data:
            return None
        
        session = json.loads(session_data)
        
        # Check for session hijacking
        if session.get('ip_address') != request.remote_addr:
            self.invalidate_session(session_id)
            return None
        
        # Update last activity
        session['last_activity'] = datetime.utcnow().isoformat()
        self.redis.setex(
            f"session:{session_id}",
            self.session_timeout,
            json.dumps(session)
        )
        
        return session
```

### Multi-Factor Authentication
```python
import pyotp
import qrcode
from io import BytesIO
import base64

class MFAManager:
    def __init__(self):
        self.issuer_name = "Healthcare Platform"
    
    def generate_secret(self, user_email):
        """Generate TOTP secret for user"""
        secret = pyotp.random_base32()
        
        # Store secret securely (encrypted in database)
        encrypted_secret = self.encrypt_secret(secret)
        self.store_user_secret(user_email, encrypted_secret)
        
        return secret
    
    def generate_qr_code(self, user_email, secret):
        """Generate QR code for TOTP setup"""
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user_email,
            issuer_name=self.issuer_name
        )
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        
        return base64.b64encode(buffer.getvalue()).decode()
    
    def verify_totp(self, user_email, token):
        """Verify TOTP token"""
        secret = self.get_user_secret(user_email)
        if not secret:
            return False
        
        totp = pyotp.TOTP(secret)
        return totp.verify(token, valid_window=1)  # Allow 30-second window
    
    def generate_backup_codes(self, user_email):
        """Generate backup codes for account recovery"""
        codes = [secrets.token_hex(4).upper() for _ in range(10)]
        
        # Hash and store backup codes
        hashed_codes = [
            hashlib.sha256(code.encode()).hexdigest() 
            for code in codes
        ]
        self.store_backup_codes(user_email, hashed_codes)
        
        return codes
```

## 🔒 Data Protection and Encryption

### Encryption at Rest
```python
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import os
import base64

class DataEncryption:
    def __init__(self, master_key=None):
        if master_key:
            self.key = master_key.encode()
        else:
            self.key = os.environ.get('ENCRYPTION_KEY', '').encode()
        
        if not self.key:
            raise ValueError("Encryption key not provided")
    
    def derive_key(self, password, salt):
        """Derive encryption key from password"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return Fernet(key)
    
    def encrypt_sensitive_data(self, data):
        """Encrypt sensitive healthcare data"""
        f = Fernet(self.key)
        encrypted_data = f.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    def decrypt_sensitive_data(self, encrypted_data):
        """Decrypt sensitive healthcare data"""
        f = Fernet(self.key)
        decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted_data = f.decrypt(decoded_data)
        return decrypted_data.decode()
    
    def encrypt_file(self, file_path, output_path):
        """Encrypt file for secure storage"""
        f = Fernet(self.key)
        
        with open(file_path, 'rb') as file:
            file_data = file.read()
        
        encrypted_data = f.encrypt(file_data)
        
        with open(output_path, 'wb') as file:
            file.write(encrypted_data)

# Database field encryption
class EncryptedField:
    def __init__(self, encryption_service):
        self.encryption = encryption_service
    
    def encrypt_for_storage(self, value):
        """Encrypt value before database storage"""
        if value is None:
            return None
        return self.encryption.encrypt_sensitive_data(str(value))
    
    def decrypt_from_storage(self, encrypted_value):
        """Decrypt value after database retrieval"""
        if encrypted_value is None:
            return None
        return self.encryption.decrypt_sensitive_data(encrypted_value)

# Usage in models
class Patient:
    def __init__(self, encryption_service):
        self.encryption = EncryptedField(encryption_service)
    
    def set_ssn(self, ssn):
        self.encrypted_ssn = self.encryption.encrypt_for_storage(ssn)
    
    def get_ssn(self):
        return self.encryption.decrypt_from_storage(self.encrypted_ssn)
```

### Secure File Upload
```python
import magic
import hashlib
from werkzeug.utils import secure_filename

class SecureFileUpload:
    ALLOWED_EXTENSIONS = {
        'image': ['jpg', 'jpeg', 'png', 'gif'],
        'document': ['pdf', 'doc', 'docx'],
        'medical': ['dcm', 'hl7']  # DICOM, HL7
    }
    
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    
    def __init__(self, upload_folder, encryption_service):
        self.upload_folder = upload_folder
        self.encryption = encryption_service
    
    def validate_file(self, file):
        """Comprehensive file validation"""
        if not file or not file.filename:
            raise ValueError("No file provided")
        
        # Check file size
        file.seek(0, 2)  # Seek to end
        size = file.tell()
        file.seek(0)  # Reset to beginning
        
        if size > self.MAX_FILE_SIZE:
            raise ValueError("File too large")
        
        # Validate file extension
        filename = secure_filename(file.filename)
        if not self.is_allowed_extension(filename):
            raise ValueError("File type not allowed")
        
        # Validate MIME type
        file_content = file.read(1024)  # Read first 1KB
        file.seek(0)  # Reset
        
        mime_type = magic.from_buffer(file_content, mime=True)
        if not self.is_allowed_mime_type(mime_type):
            raise ValueError("Invalid file type")
        
        return filename
    
    def save_secure_file(self, file, patient_id):
        """Save file with encryption and secure naming"""
        filename = self.validate_file(file)
        
        # Generate secure filename
        file_hash = hashlib.sha256(file.read()).hexdigest()
        file.seek(0)
        
        secure_name = f"{patient_id}_{file_hash}_{filename}"
        file_path = os.path.join(self.upload_folder, secure_name)
        
        # Save and encrypt file
        temp_path = f"{file_path}.tmp"
        file.save(temp_path)
        
        # Encrypt the saved file
        self.encryption.encrypt_file(temp_path, file_path)
        os.remove(temp_path)  # Remove unencrypted temp file
        
        return secure_name
```

## 🚦 Error Handling and Logging

### Secure Error Handling
```python
import logging
import traceback
from flask import jsonify

class SecurityLogger:
    def __init__(self):
        self.logger = logging.getLogger('security')
        self.logger.setLevel(logging.INFO)
        
        # Configure secure logging
        handler = logging.FileHandler('/var/log/security.log')
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def log_security_event(self, event_type, user_id, details):
        """Log security-related events"""
        self.logger.warning(
            f"SECURITY_EVENT: {event_type} | "
            f"User: {user_id} | "
            f"Details: {details} | "
            f"IP: {request.remote_addr}"
        )

def handle_errors(app):
    """Secure error handling"""
    
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({
            'error': 'Bad request',
            'message': 'Invalid input provided'
        }), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        return jsonify({
            'error': 'Unauthorized',
            'message': 'Authentication required'
        }), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        return jsonify({
            'error': 'Forbidden',
            'message': 'Access denied'
        }), 403
    
    @app.errorhandler(500)
    def internal_error(error):
        # Log detailed error for debugging (server-side only)
        app.logger.error(f"Internal error: {traceback.format_exc()}")
        
        # Return generic error to client
        return jsonify({
            'error': 'Internal server error',
            'message': 'An unexpected error occurred'
        }), 500

# Rate limiting for security
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["1000 per day", "100 per hour"]
)

@app.route('/api/login', methods=['POST'])
@limiter.limit("5 per minute")
def login():
    # Login implementation with rate limiting
    pass
```

## 🔍 Code Review Security Checklist

### Pre-commit Security Checks
```bash
#!/bin/bash
# pre-commit-security.sh

echo "Running security checks..."

# Static analysis
bandit -r . -f json -o bandit-report.json

# Dependency vulnerability check
safety check --json --output safety-report.json

# Secret detection
truffleHog --regex --entropy=False .

# SAST scanning
semgrep --config=auto --json --output=semgrep-report.json

echo "Security checks completed"
```

### Security Code Review Guidelines
- [ ] **Input validation** on all user inputs
- [ ] **Output encoding** to prevent XSS
- [ ] **Parameterized queries** to prevent SQL injection
- [ ] **Authentication** on all protected endpoints
- [ ] **Authorization** checks for data access
- [ ] **Encryption** for sensitive data
- [ ] **Secure session management**
- [ ] **Error handling** without information disclosure
- [ ] **Logging** of security events
- [ ] **Rate limiting** on sensitive operations

## 📚 Next Steps

1. **Study**: Web Application Security (04-web-app-security.md)
2. **Implement**: Security linting in your IDE
3. **Practice**: Code review with security focus
4. **Setup**: Automated security testing in CI/CD

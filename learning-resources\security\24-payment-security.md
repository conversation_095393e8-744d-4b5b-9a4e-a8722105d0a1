# Payment Security - PCI DSS Compliance

## 💳 PCI DSS Requirements Overview

### Requirement 1: Install and maintain a firewall configuration

#### Network Segmentation

```python
class NetworkSegmentation:
    def __init__(self):
        self.network_zones = {
            'dmz': '10.1.0.0/24',
            'cardholder_data_environment': '10.2.0.0/24',
            'internal_network': '10.3.0.0/24',
            'management_network': '10.4.0.0/24'
        }

    def implement_firewall_rules(self):
        """Implement PCI DSS compliant firewall rules"""
        firewall_rules = {
            'inbound_rules': [
                {
                    'source': 'any',
                    'destination': 'dmz',
                    'ports': [80, 443],
                    'protocol': 'tcp',
                    'action': 'allow'
                },
                {
                    'source': 'dmz',
                    'destination': 'cardholder_data_environment',
                    'ports': [443],
                    'protocol': 'tcp',
                    'action': 'allow'
                }
            ],
            'outbound_rules': [
                {
                    'source': 'cardholder_data_environment',
                    'destination': 'any',
                    'ports': [443],
                    'protocol': 'tcp',
                    'action': 'allow'
                }
            ],
            'default_action': 'deny'
        }

        return firewall_rules

    def validate_network_isolation(self):
        """Validate cardholder data environment isolation"""
        isolation_tests = {
            'cde_to_internal': self.test_network_connectivity('cde', 'internal'),
            'internal_to_cde': self.test_network_connectivity('internal', 'cde'),
            'dmz_to_cde': self.test_network_connectivity('dmz', 'cde'),
            'internet_to_cde': self.test_network_connectivity('internet', 'cde')
        }

        return isolation_tests
```

### Requirement 2: Do not use vendor-supplied defaults

#### Secure Configuration Management

```python
class SecureConfiguration:
    def __init__(self):
        self.default_passwords = [
            'admin', 'password', 'default', '123456',
            'root', 'guest', 'user', 'test'
        ]

    def harden_system_configuration(self, system_type):
        """Implement secure system hardening"""
        hardening_configs = {
            'web_server': {
                'remove_default_accounts': True,
                'disable_unnecessary_services': True,
                'secure_headers': True,
                'error_page_customization': True
            },
            'database': {
                'change_default_passwords': True,
                'remove_sample_databases': True,
                'disable_remote_access': True,
                'encrypt_connections': True
            },
            'application': {
                'remove_development_accounts': True,
                'disable_debug_mode': True,
                'secure_session_management': True,
                'input_validation': True
            }
        }

        return hardening_configs.get(system_type, {})

    def validate_password_policy(self, password):
        """Validate password meets PCI DSS requirements"""
        requirements = {
            'min_length': len(password) >= 7,
            'contains_numeric': any(c.isdigit() for c in password),
            'contains_alpha': any(c.isalpha() for c in password),
            'not_default': password.lower() not in self.default_passwords,
            'changed_every_90_days': True  # Implement based on last change date
        }

        return all(requirements.values()), requirements
```

### Requirement 3: Protect stored cardholder data

#### Data Encryption and Tokenization

```python
from cryptography.fernet import Fernet
import hashlib
import secrets

class CardholderDataProtection:
    def __init__(self):
        self.encryption_key = self.load_encryption_key()
        self.token_vault = {}

    def encrypt_cardholder_data(self, card_data):
        """Encrypt cardholder data using strong cryptography"""
        f = Fernet(self.encryption_key)

        encrypted_data = {
            'pan': f.encrypt(card_data['pan'].encode()) if card_data.get('pan') else None,
            'expiry_date': f.encrypt(card_data['expiry_date'].encode()) if card_data.get('expiry_date') else None,
            'cardholder_name': f.encrypt(card_data['cardholder_name'].encode()) if card_data.get('cardholder_name') else None,
            # Never store CVV/CVC
            'encryption_timestamp': datetime.now().isoformat()
        }

        return encrypted_data

    def tokenize_pan(self, pan):
        """Tokenize PAN for secure storage"""
        # Generate cryptographically secure token
        token = secrets.token_hex(16)

        # Store mapping in secure token vault
        self.token_vault[token] = {
            'pan_hash': hashlib.sha256(pan.encode()).hexdigest(),
            'created_at': datetime.now(),
            'usage_count': 0
        }

        return token

    def mask_pan(self, pan):
        """Mask PAN for display purposes"""
        if len(pan) < 6:
            return '*' * len(pan)

        # Show first 6 and last 4 digits
        return pan[:6] + '*' * (len(pan) - 10) + pan[-4:]

    def secure_data_disposal(self, data_id):
        """Securely dispose of cardholder data"""
        disposal_methods = {
            'overwrite': self.overwrite_data(data_id),
            'degauss': self.degauss_media(data_id),
            'physical_destruction': self.physically_destroy_media(data_id)
        }

        disposal_log = {
            'data_id': data_id,
            'disposal_date': datetime.now(),
            'disposal_method': 'overwrite',
            'verified_by': 'security_officer',
            'certificate_of_destruction': True
        }

        return disposal_log
```

### Requirement 4: Encrypt transmission of cardholder data

#### Secure Transmission

```python
import ssl
import requests
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes

class SecureTransmission:
    def __init__(self):
        self.tls_version = ssl.PROTOCOL_TLSv1_2
        self.cipher_suites = [
            'ECDHE-RSA-AES256-GCM-SHA384',
            'ECDHE-RSA-AES128-GCM-SHA256',
            'DHE-RSA-AES256-GCM-SHA384'
        ]

    def configure_secure_session(self):
        """Configure secure HTTPS session"""
        session = requests.Session()

        # Configure SSL/TLS settings
        session.verify = True  # Verify SSL certificates
        session.mount('https://', requests.adapters.HTTPAdapter(
            ssl_context=self.create_ssl_context()
        ))

        return session

    def create_ssl_context(self):
        """Create secure SSL context"""
        context = ssl.create_default_context()
        context.minimum_version = ssl.TLSVersion.TLSv1_2
        context.set_ciphers(':'.join(self.cipher_suites))
        context.check_hostname = True
        context.verify_mode = ssl.CERT_REQUIRED

        return context

    def encrypt_payment_data(self, payment_data, recipient_public_key):
        """Encrypt payment data for transmission"""
        from cryptography.hazmat.primitives.asymmetric import rsa, padding
        from cryptography.hazmat.primitives import hashes

        # Serialize payment data
        data_json = json.dumps(payment_data)

        # Encrypt with recipient's public key
        encrypted_data = recipient_public_key.encrypt(
            data_json.encode(),
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )

        return base64.b64encode(encrypted_data).decode()

    def validate_certificate_chain(self, hostname):
        """Validate SSL certificate chain"""
        try:
            context = ssl.create_default_context()
            with socket.create_connection((hostname, 443)) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()

            validation = {
                'valid': True,
                'issuer': cert.get('issuer'),
                'subject': cert.get('subject'),
                'not_after': cert.get('notAfter'),
                'san': cert.get('subjectAltName', [])
            }

            return validation
        except Exception as e:
            return {'valid': False, 'error': str(e)}
```

### Requirement 5: Protect all systems against malware

#### Malware Protection

```python
class MalwareProtection:
    def __init__(self):
        self.antivirus_engines = ['clamav', 'windows_defender', 'sophos']
        self.scan_schedule = {
            'full_scan': 'weekly',
            'quick_scan': 'daily',
            'real_time': 'enabled'
        }

    def implement_antivirus_protection(self):
        """Implement comprehensive antivirus protection"""
        protection_config = {
            'real_time_scanning': True,
            'scheduled_scans': True,
            'automatic_updates': True,
            'quarantine_enabled': True,
            'scan_removable_media': True,
            'email_scanning': True,
            'web_protection': True
        }

        return protection_config

    def scan_uploaded_files(self, file_path):
        """Scan uploaded files for malware"""
        scan_results = {
            'file_path': file_path,
            'scan_timestamp': datetime.now(),
            'engines_used': self.antivirus_engines,
            'threats_detected': [],
            'clean': True
        }

        # Simulate antivirus scanning
        for engine in self.antivirus_engines:
            result = self.scan_with_engine(file_path, engine)
            if result['threats']:
                scan_results['threats_detected'].extend(result['threats'])
                scan_results['clean'] = False

        # Quarantine if threats detected
        if not scan_results['clean']:
            self.quarantine_file(file_path)

        return scan_results

    def update_malware_definitions(self):
        """Update malware definitions"""
        update_log = {
            'update_timestamp': datetime.now(),
            'previous_version': self.get_current_definitions_version(),
            'new_version': None,
            'update_successful': False
        }

        try:
            # Update definitions
            new_version = self.download_latest_definitions()
            self.install_definitions(new_version)

            update_log['new_version'] = new_version
            update_log['update_successful'] = True
        except Exception as e:
            update_log['error'] = str(e)

        return update_log
```

### Requirement 6: Develop and maintain secure systems

#### Secure Development Lifecycle

```python
class SecureDevelopment:
    def __init__(self):
        self.security_gates = [
            'threat_modeling',
            'static_analysis',
            'dynamic_analysis',
            'penetration_testing',
            'security_review'
        ]

    def implement_secure_coding_standards(self):
        """Implement secure coding standards"""
        standards = {
            'input_validation': {
                'validate_all_inputs': True,
                'whitelist_validation': True,
                'parameterized_queries': True,
                'output_encoding': True
            },
            'authentication': {
                'strong_passwords': True,
                'multi_factor_auth': True,
                'session_management': True,
                'account_lockout': True
            },
            'authorization': {
                'principle_of_least_privilege': True,
                'role_based_access': True,
                'resource_authorization': True
            },
            'cryptography': {
                'strong_algorithms': True,
                'proper_key_management': True,
                'secure_random_generation': True
            }
        }

        return standards

    def conduct_security_testing(self, application):
        """Conduct comprehensive security testing"""
        testing_results = {
            'static_analysis': self.run_static_analysis(application),
            'dynamic_analysis': self.run_dynamic_analysis(application),
            'dependency_check': self.check_vulnerable_dependencies(application),
            'penetration_test': self.run_penetration_test(application)
        }

        # Calculate overall security score
        testing_results['security_score'] = self.calculate_security_score(testing_results)

        return testing_results

    def manage_security_patches(self):
        """Implement security patch management"""
        patch_management = {
            'vulnerability_scanning': 'monthly',
            'critical_patch_timeline': '30 days',
            'high_patch_timeline': '60 days',
            'medium_patch_timeline': '90 days',
            'patch_testing': 'required',
            'rollback_procedures': 'documented'
        }

        return patch_management
```

### Requirement 7: Restrict access to cardholder data by business need

#### Access Control Implementation

```python
class AccessControl:
    def __init__(self):
        self.access_levels = {
            'no_access': 0,
            'read_only': 1,
            'read_write': 2,
            'admin': 3
        }

    def implement_need_to_know_access(self, user_role, data_type):
        """Implement need-to-know access control"""
        access_matrix = {
            'customer_service': {
                'masked_pan': 'read_only',
                'cardholder_name': 'read_only',
                'transaction_history': 'read_only',
                'full_pan': 'no_access'
            },
            'payment_processor': {
                'masked_pan': 'read_only',
                'full_pan': 'read_write',
                'transaction_data': 'read_write',
                'encryption_keys': 'no_access'
            },
            'security_admin': {
                'audit_logs': 'read_only',
                'access_controls': 'read_write',
                'encryption_keys': 'read_write',
                'system_config': 'read_write'
            }
        }

        user_permissions = access_matrix.get(user_role, {})
        return user_permissions.get(data_type, 'no_access')

    def enforce_data_minimization(self, user_id, requested_data):
        """Enforce data minimization principle"""
        user_profile = self.get_user_profile(user_id)
        authorized_data = {}

        for data_field, data_value in requested_data.items():
            access_level = self.implement_need_to_know_access(
                user_profile['role'],
                data_field
            )

            if access_level != 'no_access':
                if data_field == 'pan' and access_level == 'read_only':
                    # Return masked PAN
                    authorized_data[data_field] = self.mask_pan(data_value)
                else:
                    authorized_data[data_field] = data_value

        return authorized_data
```

### Requirement 8: Identify and authenticate access

#### Strong Authentication

```python
class StrongAuthentication:
    def __init__(self):
        self.password_policy = {
            'min_length': 7,
            'require_numeric': True,
            'require_alpha': True,
            'max_age_days': 90,
            'history_count': 4,
            'lockout_threshold': 6,
            'lockout_duration': 30  # minutes
        }

    def implement_multi_factor_auth(self, user_id):
        """Implement multi-factor authentication"""
        mfa_methods = {
            'something_you_know': 'password',
            'something_you_have': 'token_or_phone',
            'something_you_are': 'biometric'
        }

        # Generate TOTP token
        secret = pyotp.random_base32()
        totp = pyotp.TOTP(secret)

        mfa_setup = {
            'user_id': user_id,
            'secret': secret,
            'qr_code': self.generate_qr_code(user_id, secret),
            'backup_codes': self.generate_backup_codes(),
            'enabled': False
        }

        return mfa_setup

    def validate_authentication(self, username, password, mfa_token=None):
        """Validate user authentication"""
        user = self.get_user(username)
        if not user:
            return {'success': False, 'reason': 'invalid_user'}

        # Check account lockout
        if self.is_account_locked(user['id']):
            return {'success': False, 'reason': 'account_locked'}

        # Validate password
        if not self.verify_password(password, user['password_hash']):
            self.increment_failed_attempts(user['id'])
            return {'success': False, 'reason': 'invalid_password'}

        # Validate MFA if enabled
        if user['mfa_enabled']:
            if not mfa_token or not self.verify_mfa_token(user['id'], mfa_token):
                return {'success': False, 'reason': 'invalid_mfa'}

        # Reset failed attempts on successful login
        self.reset_failed_attempts(user['id'])

        return {'success': True, 'user_id': user['id']}
```

## 💰 Stripe Connect Security Implementation

### Secure Payment Processing

```python
import stripe
from decimal import Decimal

class StripeConnectSecurity:
    def __init__(self):
        stripe.api_key = os.environ.get('STRIPE_SECRET_KEY')
        self.platform_fee_percentage = Decimal('0.02')  # 2% commission

    def create_secure_payment_intent(self, amount, currency, connected_account_id, metadata=None):
        """Create secure payment intent with proper validation"""
        # Validate amount
        if amount <= 0 or amount > 999999:  # $9,999.99 max
            raise ValueError("Invalid payment amount")

        # Calculate platform fee
        platform_fee = int(amount * self.platform_fee_percentage)

        try:
            payment_intent = stripe.PaymentIntent.create(
                amount=amount,
                currency=currency,
                application_fee_amount=platform_fee,
                transfer_data={
                    'destination': connected_account_id,
                },
                metadata=metadata or {},
                stripe_account=connected_account_id
            )

            # Log payment creation
            self.log_payment_event('payment_intent_created', {
                'payment_intent_id': payment_intent.id,
                'amount': amount,
                'platform_fee': platform_fee,
                'connected_account': connected_account_id
            })

            return payment_intent

        except stripe.error.StripeError as e:
            self.log_payment_error('payment_intent_creation_failed', str(e))
            raise

    def validate_webhook_signature(self, payload, signature, endpoint_secret):
        """Validate Stripe webhook signature"""
        try:
            event = stripe.Webhook.construct_event(
                payload, signature, endpoint_secret
            )
            return event
        except ValueError:
            # Invalid payload
            raise ValueError("Invalid payload")
        except stripe.error.SignatureVerificationError:
            # Invalid signature
            raise ValueError("Invalid signature")

    def secure_refund_processing(self, payment_intent_id, amount=None, reason=None):
        """Process secure refund with proper authorization"""
        try:
            refund = stripe.Refund.create(
                payment_intent=payment_intent_id,
                amount=amount,
                reason=reason,
                metadata={
                    'processed_by': session.get('user_id'),
                    'timestamp': datetime.now().isoformat()
                }
            )

            # Log refund
            self.log_payment_event('refund_processed', {
                'refund_id': refund.id,
                'payment_intent_id': payment_intent_id,
                'amount': amount,
                'reason': reason
            })

            return refund

        except stripe.error.StripeError as e:
            self.log_payment_error('refund_failed', str(e))
            raise
```

## 📊 PCI DSS Compliance Monitoring

### Compliance Dashboard

```python
class PCIComplianceDashboard:
    def __init__(self):
        self.requirements = [
            'firewall_configuration',
            'default_passwords_changed',
            'cardholder_data_protected',
            'encrypted_transmission',
            'antivirus_protection',
            'secure_systems',
            'restricted_access',
            'unique_user_ids',
            'physical_access_restricted',
            'network_monitoring',
            'security_testing',
            'security_policy'
        ]

    def generate_compliance_report(self):
        """Generate PCI DSS compliance report"""
        compliance_status = {}

        for requirement in self.requirements:
            compliance_status[requirement] = self.check_requirement_compliance(requirement)

        overall_compliance = all(compliance_status.values())

        report = {
            'assessment_date': datetime.now(),
            'overall_compliance': overall_compliance,
            'compliance_percentage': sum(compliance_status.values()) / len(compliance_status) * 100,
            'requirement_status': compliance_status,
            'next_assessment': datetime.now() + timedelta(days=365),
            'remediation_items': self.get_remediation_items(compliance_status)
        }

        return report

    def schedule_vulnerability_scan(self):
        """Schedule quarterly vulnerability scan"""
        scan_schedule = {
            'scan_type': 'external_vulnerability_scan',
            'frequency': 'quarterly',
            'next_scan': self.calculate_next_scan_date(),
            'approved_scanning_vendor': 'ASV_VENDOR_NAME',
            'scope': 'all_external_facing_systems'
        }

        return scan_schedule
```

## 📋 PCI DSS Compliance Checklist

### Build and Maintain a Secure Network

- [ ] Install and maintain firewall configuration
- [ ] Do not use vendor-supplied defaults for passwords
- [ ] Network segmentation implemented
- [ ] Firewall rules documented and reviewed

### Protect Cardholder Data

- [ ] Protect stored cardholder data
- [ ] Encrypt transmission of cardholder data
- [ ] Data retention policy implemented
- [ ] Secure disposal procedures

### Maintain a Vulnerability Management Program

- [ ] Protect all systems against malware
- [ ] Develop and maintain secure systems
- [ ] Regular security updates applied
- [ ] Vulnerability scanning performed

### Implement Strong Access Control Measures

- [ ] Restrict access by business need-to-know
- [ ] Identify and authenticate access to system components
- [ ] Restrict physical access to cardholder data
- [ ] Multi-factor authentication implemented

### Regularly Monitor and Test Networks

- [ ] Track and monitor access to network resources
- [ ] Regularly test security systems and processes
- [ ] Penetration testing performed annually
- [ ] Security incident response plan

### Maintain an Information Security Policy

- [ ] Information security policy maintained
- [ ] Security awareness program implemented
- [ ] Regular security training conducted
- [ ] Incident response procedures documented

## 🔐 Advanced Payment Security Measures

### Fraud Detection and Prevention

````python
class FraudDetection:
    def __init__(self):
        self.risk_factors = {
            'velocity_checks': True,
            'geolocation_analysis': True,
            'device_fingerprinting': True,
            'behavioral_analysis': True
        }

    def analyze_transaction_risk(self, transaction):
        """Analyze transaction for fraud indicators"""
        risk_score = 0
        risk_factors = []

        # Velocity checks
        if self.check_velocity_limits(transaction):
            risk_score += 25
            risk_factors.append('high_velocity')

        # Geolocation analysis
        if self.check_geolocation_anomaly(transaction):
            risk_score += 30
            risk_factors.append('location_anomaly')

        # Amount analysis
        if self.check_amount_anomaly(transaction):
            risk_score += 20
            risk_factors.append('amount_anomaly')

        # Device fingerprinting
        if self.check_device_anomaly(transaction):
            risk_score += 15
            risk_factors.append('device_anomaly')

        risk_assessment = {
            'transaction_id': transaction['id'],
            'risk_score': risk_score,
            'risk_level': self.calculate_risk_level(risk_score),
            'risk_factors': risk_factors,
            'recommended_action': self.get_recommended_action(risk_score)
        }

        return risk_assessment

    def implement_3ds_authentication(self, payment_data):
        """Implement 3D Secure authentication"""
        threeds_request = {
            'amount': payment_data['amount'],
            'currency': payment_data['currency'],
            'card_number': payment_data['card_number'],
            'return_url': 'https://yoursite.com/3ds-return',
            'merchant_data': {
                'transaction_id': payment_data['transaction_id'],
                'customer_id': payment_data['customer_id']
            }
        }

        # Process 3DS authentication
        threeds_response = self.process_3ds_authentication(threeds_request)

        return threeds_response

### Secure Key Management
```python
class PaymentKeyManagement:
    def __init__(self):
        self.key_rotation_schedule = timedelta(days=90)
        self.key_escrow_enabled = True

    def generate_encryption_keys(self):
        """Generate strong encryption keys"""
        from cryptography.hazmat.primitives.asymmetric import rsa
        from cryptography.hazmat.primitives import serialization

        # Generate RSA key pair
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048
        )

        public_key = private_key.public_key()

        # Serialize keys
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.BestAvailableEncryption(
                self.get_key_password().encode()
            )
        )

        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )

        return {
            'private_key': private_pem,
            'public_key': public_pem,
            'key_id': self.generate_key_id(),
            'created_at': datetime.now(),
            'expires_at': datetime.now() + self.key_rotation_schedule
        }

    def implement_key_escrow(self, key_data):
        """Implement secure key escrow"""
        escrow_data = {
            'key_id': key_data['key_id'],
            'encrypted_key': self.encrypt_for_escrow(key_data['private_key']),
            'escrow_timestamp': datetime.now(),
            'authorized_retrievers': ['security_officer', 'compliance_officer'],
            'retrieval_log': []
        }

        # Store in secure escrow system
        self.store_escrowed_key(escrow_data)

        return escrow_data

## 📚 Next Steps

1. **Study**: Data Privacy (25-data-privacy.md)
2. **Implement**: PCI DSS compliance monitoring
3. **Practice**: Payment security testing
4. **Certification**: Consider PCI DSS training and certification
````

# DevSecOps Fundamentals

## 🔄 Security in Development Lifecycle

### Shift-Left Security Approach
```yaml
# DevSecOps Pipeline Overview
stages:
  - pre-commit:
      - secret_scanning
      - static_analysis
      - dependency_check
  - build:
      - secure_build_environment
      - container_scanning
      - artifact_signing
  - test:
      - dynamic_analysis
      - penetration_testing
      - compliance_validation
  - deploy:
      - infrastructure_scanning
      - runtime_protection
      - monitoring_setup
  - operate:
      - continuous_monitoring
      - threat_detection
      - incident_response
```

### Pre-commit Security Hooks
```bash
#!/bin/bash
# .git/hooks/pre-commit

echo "Running security checks..."

# 1. Secret Detection
echo "🔍 Scanning for secrets..."
truffleHog --regex --entropy=False --max_depth=1 .
if [ $? -ne 0 ]; then
    echo "❌ Secrets detected! Commit blocked."
    exit 1
fi

# 2. Static Analysis Security Testing (SAST)
echo "🔍 Running static analysis..."
bandit -r . -f json -o bandit-report.json
if [ $? -ne 0 ]; then
    echo "❌ Security vulnerabilities found! Check bandit-report.json"
    exit 1
fi

# 3. Dependency Vulnerability Check
echo "🔍 Checking dependencies..."
safety check --json --output safety-report.json
if [ $? -ne 0 ]; then
    echo "❌ Vulnerable dependencies found! Check safety-report.json"
    exit 1
fi

# 4. License Compliance Check
echo "🔍 Checking license compliance..."
pip-licenses --format=json --output-file=licenses.json
python scripts/check_license_compliance.py

# 5. Code Quality and Security Linting
echo "🔍 Running security linting..."
semgrep --config=auto --json --output=semgrep-report.json .

echo "✅ All security checks passed!"
```

### Secure Build Environment
```dockerfile
# Multi-stage secure build
FROM python:3.11-slim as builder

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Install security updates
RUN apt-get update && apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install dependencies with security considerations
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Run security scans during build
RUN bandit -r . -f json -o /tmp/bandit-report.json || true
RUN safety check --json --output /tmp/safety-report.json || true

# Production stage
FROM python:3.11-slim as production

# Install security updates
RUN apt-get update && apt-get upgrade -y && \
    rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Copy application from builder
COPY --from=builder /app /app
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages

# Set working directory and user
WORKDIR /app
USER appuser

# Security configurations
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV FLASK_ENV=production

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD python health_check.py || exit 1

# Run application
CMD ["python", "app.py"]
```

## 🔒 Infrastructure as Code Security

### Terraform Security Scanning
```hcl
# terraform/main.tf - Secure infrastructure configuration

# Provider configuration with security settings
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  
  # Secure backend configuration
  backend "s3" {
    bucket         = "healthcare-terraform-state"
    key            = "prod/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "terraform-locks"
  }
}

# VPC with security groups
resource "aws_vpc" "healthcare_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  tags = {
    Name        = "healthcare-vpc"
    Environment = "production"
    Compliance  = "HIPAA"
  }
}

# Security group with least privilege
resource "aws_security_group" "web_sg" {
  name_prefix = "healthcare-web-"
  vpc_id      = aws_vpc.healthcare_vpc.id
  
  # HTTPS only
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  # No SSH from internet
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [aws_vpc.healthcare_vpc.cidr_block]
  }
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = {
    Name = "healthcare-web-sg"
  }
}

# RDS with encryption
resource "aws_db_instance" "healthcare_db" {
  identifier = "healthcare-db"
  
  engine         = "postgres"
  engine_version = "15.4"
  instance_class = "db.t3.medium"
  
  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_type          = "gp3"
  storage_encrypted     = true
  kms_key_id           = aws_kms_key.rds_key.arn
  
  db_name  = "healthcare"
  username = "dbadmin"
  password = random_password.db_password.result
  
  vpc_security_group_ids = [aws_security_group.db_sg.id]
  db_subnet_group_name   = aws_db_subnet_group.healthcare_db_subnet_group.name
  
  backup_retention_period = 30
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  deletion_protection = true
  skip_final_snapshot = false
  
  enabled_cloudwatch_logs_exports = ["postgresql"]
  
  tags = {
    Name        = "healthcare-db"
    Environment = "production"
    Compliance  = "HIPAA"
  }
}
```

### Infrastructure Security Scanning
```yaml
# .github/workflows/infrastructure-security.yml
name: Infrastructure Security Scan

on:
  pull_request:
    paths:
      - 'terraform/**'
  push:
    branches:
      - main

jobs:
  terraform-security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.6.0
      
      - name: Terraform Format Check
        run: terraform fmt -check -recursive
        working-directory: terraform
      
      - name: Terraform Validate
        run: |
          terraform init -backend=false
          terraform validate
        working-directory: terraform
      
      - name: Run Checkov
        uses: bridgecrewio/checkov-action@master
        with:
          directory: terraform
          framework: terraform
          output_format: sarif
          output_file_path: checkov-report.sarif
      
      - name: Run TFSec
        uses: aquasecurity/tfsec-action@v1.0.0
        with:
          working_directory: terraform
          format: sarif
          sarif_file: tfsec-report.sarif
      
      - name: Run Terrascan
        uses: tenable/terrascan-action@main
        with:
          iac_type: terraform
          iac_dir: terraform
          policy_type: aws
          sarif_upload: true
      
      - name: Upload SARIF files
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: |
            checkov-report.sarif
            tfsec-report.sarif
```

## 🐳 Container Security

### Secure Container Build
```dockerfile
# Dockerfile.secure
FROM python:3.11-slim as base

# Security: Install security updates
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Security: Create non-root user
RUN groupadd -r appuser && \
    useradd -r -g appuser -d /app -s /sbin/nologin appuser

# Development stage
FROM base as development
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY requirements-dev.txt .
RUN pip install --no-cache-dir -r requirements-dev.txt

# Production stage
FROM base as production

# Security: Set secure file permissions
WORKDIR /app
COPY --chown=appuser:appuser requirements.txt .

# Security: Install dependencies as root, then switch to appuser
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY --chown=appuser:appuser . .

# Security: Remove unnecessary packages and files
RUN apt-get autoremove -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Security: Switch to non-root user
USER appuser

# Security: Set read-only filesystem
# VOLUME ["/tmp"]

# Security: Expose only necessary port
EXPOSE 8000

# Security: Use specific command
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "app:app"]
```

### Container Security Scanning
```yaml
# .github/workflows/container-security.yml
name: Container Security Scan

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  container-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Build Docker image
        run: |
          docker build -t healthcare-app:${{ github.sha }} .
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'healthcare-app:${{ github.sha }}'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Run Snyk Container scan
        uses: snyk/actions/docker@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          image: healthcare-app:${{ github.sha }}
          args: --severity-threshold=high
      
      - name: Run Docker Bench Security
        run: |
          docker run --rm --net host --pid host --userns host --cap-add audit_control \
            -e DOCKER_CONTENT_TRUST=$DOCKER_CONTENT_TRUST \
            -v /etc:/etc:ro \
            -v /usr/bin/containerd:/usr/bin/containerd:ro \
            -v /usr/bin/runc:/usr/bin/runc:ro \
            -v /usr/lib/systemd:/usr/lib/systemd:ro \
            -v /var/lib:/var/lib:ro \
            -v /var/run/docker.sock:/var/run/docker.sock:ro \
            --label docker_bench_security \
            docker/docker-bench-security
      
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'
```

## 🔍 Continuous Security Monitoring

### Security Monitoring Pipeline
```python
# monitoring/security_monitor.py
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any

class SecurityMonitor:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.alert_thresholds = {
            'failed_logins': 5,
            'privilege_escalation': 1,
            'unusual_data_access': 10,
            'suspicious_network_activity': 1
        }
    
    def monitor_authentication_events(self, events: List[Dict]) -> List[Dict]:
        """Monitor authentication events for suspicious activity"""
        alerts = []
        
        # Group events by IP address
        ip_events = {}
        for event in events:
            ip = event.get('ip_address')
            if ip not in ip_events:
                ip_events[ip] = []
            ip_events[ip].append(event)
        
        # Check for brute force attacks
        for ip, ip_event_list in ip_events.items():
            failed_attempts = [e for e in ip_event_list if not e.get('success', False)]
            
            if len(failed_attempts) >= self.alert_thresholds['failed_logins']:
                alerts.append({
                    'type': 'brute_force_attack',
                    'severity': 'high',
                    'ip_address': ip,
                    'failed_attempts': len(failed_attempts),
                    'time_window': '5 minutes',
                    'recommended_action': 'block_ip'
                })
        
        return alerts
    
    def monitor_data_access_patterns(self, access_logs: List[Dict]) -> List[Dict]:
        """Monitor data access for unusual patterns"""
        alerts = []
        
        # Group by user
        user_access = {}
        for log in access_logs:
            user_id = log.get('user_id')
            if user_id not in user_access:
                user_access[user_id] = []
            user_access[user_id].append(log)
        
        # Check for unusual access patterns
        for user_id, user_logs in user_access.items():
            # Check for excessive data access
            if len(user_logs) > self.alert_thresholds['unusual_data_access']:
                alerts.append({
                    'type': 'excessive_data_access',
                    'severity': 'medium',
                    'user_id': user_id,
                    'access_count': len(user_logs),
                    'recommended_action': 'investigate_user'
                })
            
            # Check for off-hours access
            off_hours_access = [
                log for log in user_logs 
                if self.is_off_hours(log.get('timestamp'))
            ]
            
            if off_hours_access:
                alerts.append({
                    'type': 'off_hours_access',
                    'severity': 'medium',
                    'user_id': user_id,
                    'off_hours_count': len(off_hours_access),
                    'recommended_action': 'verify_legitimate_access'
                })
        
        return alerts
    
    def is_off_hours(self, timestamp: str) -> bool:
        """Check if access occurred during off-hours"""
        dt = datetime.fromisoformat(timestamp)
        hour = dt.hour
        
        # Define off-hours as 10 PM to 6 AM and weekends
        is_night = hour >= 22 or hour <= 6
        is_weekend = dt.weekday() >= 5
        
        return is_night or is_weekend
```

### Automated Security Response
```python
# security/automated_response.py
class AutomatedSecurityResponse:
    def __init__(self):
        self.response_actions = {
            'block_ip': self.block_ip_address,
            'lockout_user': self.lockout_user_account,
            'isolate_system': self.isolate_system,
            'increase_monitoring': self.increase_monitoring_level,
            'notify_security_team': self.notify_security_team
        }
    
    def process_security_alert(self, alert: Dict) -> Dict:
        """Process security alert and execute automated response"""
        alert_type = alert.get('type')
        severity = alert.get('severity')
        
        # Determine response actions based on alert type and severity
        response_plan = self.get_response_plan(alert_type, severity)
        
        execution_results = []
        
        for action in response_plan['actions']:
            try:
                result = self.execute_action(action, alert)
                execution_results.append({
                    'action': action,
                    'status': 'success',
                    'result': result
                })
            except Exception as e:
                execution_results.append({
                    'action': action,
                    'status': 'failed',
                    'error': str(e)
                })
        
        return {
            'alert_id': alert.get('id'),
            'response_plan': response_plan,
            'execution_results': execution_results,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_response_plan(self, alert_type: str, severity: str) -> Dict:
        """Get automated response plan for alert"""
        response_plans = {
            'brute_force_attack': {
                'high': ['block_ip', 'notify_security_team'],
                'medium': ['increase_monitoring', 'notify_security_team'],
                'low': ['increase_monitoring']
            },
            'malware_detection': {
                'critical': ['isolate_system', 'notify_security_team'],
                'high': ['isolate_system', 'notify_security_team'],
                'medium': ['increase_monitoring', 'notify_security_team']
            },
            'data_exfiltration': {
                'critical': ['block_ip', 'isolate_system', 'notify_security_team'],
                'high': ['block_ip', 'increase_monitoring', 'notify_security_team']
            }
        }
        
        plan = response_plans.get(alert_type, {})
        actions = plan.get(severity, ['notify_security_team'])
        
        return {
            'alert_type': alert_type,
            'severity': severity,
            'actions': actions,
            'auto_execute': severity in ['critical', 'high']
        }
    
    def execute_action(self, action: str, alert: Dict) -> Dict:
        """Execute specific response action"""
        handler = self.response_actions.get(action)
        
        if not handler:
            raise ValueError(f"Unknown action: {action}")
        
        return handler(alert)
    
    def block_ip_address(self, alert: Dict) -> Dict:
        """Block IP address in firewall"""
        ip_address = alert.get('ip_address')
        
        if not ip_address:
            raise ValueError("No IP address provided in alert")
        
        # Implement firewall rule to block IP
        firewall_rule = {
            'action': 'deny',
            'source': ip_address,
            'destination': 'any',
            'protocol': 'any',
            'duration': '24 hours'
        }
        
        # Apply firewall rule (implementation depends on firewall system)
        rule_id = self.apply_firewall_rule(firewall_rule)
        
        return {
            'ip_blocked': ip_address,
            'rule_id': rule_id,
            'duration': '24 hours'
        }
```

## 📊 Security Metrics and KPIs

### DevSecOps Metrics Dashboard
```python
# metrics/security_metrics.py
class SecurityMetrics:
    def __init__(self):
        self.metrics = {
            'vulnerability_metrics': {},
            'deployment_security': {},
            'incident_response': {},
            'compliance_metrics': {}
        }
    
    def calculate_vulnerability_metrics(self, scan_results: List[Dict]) -> Dict:
        """Calculate vulnerability-related metrics"""
        total_vulns = len(scan_results)
        
        severity_counts = {
            'critical': 0,
            'high': 0,
            'medium': 0,
            'low': 0
        }
        
        for vuln in scan_results:
            severity = vuln.get('severity', 'low').lower()
            if severity in severity_counts:
                severity_counts[severity] += 1
        
        # Calculate mean time to remediation
        remediated_vulns = [v for v in scan_results if v.get('status') == 'fixed']
        if remediated_vulns:
            remediation_times = [
                (datetime.fromisoformat(v['fixed_date']) - 
                 datetime.fromisoformat(v['discovered_date'])).days
                for v in remediated_vulns
            ]
            mean_time_to_remediation = sum(remediation_times) / len(remediation_times)
        else:
            mean_time_to_remediation = 0
        
        return {
            'total_vulnerabilities': total_vulns,
            'severity_breakdown': severity_counts,
            'mean_time_to_remediation_days': mean_time_to_remediation,
            'vulnerability_density': total_vulns / 1000,  # per 1000 lines of code
            'remediation_rate': len(remediated_vulns) / total_vulns if total_vulns > 0 else 0
        }
    
    def calculate_deployment_security_metrics(self, deployments: List[Dict]) -> Dict:
        """Calculate deployment security metrics"""
        total_deployments = len(deployments)
        
        security_gate_passes = sum(
            1 for d in deployments 
            if d.get('security_scan_passed', False)
        )
        
        failed_deployments = sum(
            1 for d in deployments 
            if d.get('status') == 'failed' and 'security' in d.get('failure_reason', '')
        )
        
        return {
            'total_deployments': total_deployments,
            'security_gate_pass_rate': security_gate_passes / total_deployments if total_deployments > 0 else 0,
            'security_failure_rate': failed_deployments / total_deployments if total_deployments > 0 else 0,
            'average_security_scan_time': self.calculate_average_scan_time(deployments)
        }
```

## 📋 DevSecOps Checklist

### Development Phase
- [ ] Security requirements defined
- [ ] Threat modeling completed
- [ ] Secure coding standards established
- [ ] Developer security training completed
- [ ] IDE security plugins configured

### Build Phase
- [ ] Static analysis security testing (SAST) integrated
- [ ] Dependency vulnerability scanning enabled
- [ ] Secret detection implemented
- [ ] Container security scanning configured
- [ ] Build environment hardened

### Test Phase
- [ ] Dynamic analysis security testing (DAST) implemented
- [ ] Interactive application security testing (IAST) configured
- [ ] Security test cases automated
- [ ] Penetration testing scheduled
- [ ] Compliance validation automated

### Deploy Phase
- [ ] Infrastructure security scanning enabled
- [ ] Configuration security validated
- [ ] Runtime application self-protection (RASP) deployed
- [ ] Security monitoring configured
- [ ] Incident response procedures activated

### Operate Phase
- [ ] Continuous security monitoring active
- [ ] Threat detection systems operational
- [ ] Security metrics collection enabled
- [ ] Regular security assessments scheduled
- [ ] Security awareness training ongoing

## 📚 Next Steps

1. **Study**: Security Testing (14-security-testing.md)
2. **Implement**: CI/CD security pipeline
3. **Practice**: Container security hardening
4. **Setup**: Automated security monitoring

# AI Algorithms Overview

Comprehensive overview of the key algorithms that power artificial intelligence systems.

## 🎯 Learning Objectives

By completing this module, you will:
- Understand the main categories of AI algorithms and their applications
- Learn how different algorithms solve different types of problems
- Recognize which algorithms are appropriate for specific use cases
- Understand the trade-offs between different algorithmic approaches
- Gain practical knowledge of algorithm implementation patterns

## 🧠 Algorithm Categories

### 1. Search Algorithms
**Purpose**: Finding solutions in large problem spaces
**Key Concepts**: State space, search tree, heuristics

#### Uninformed Search
- **Breadth-First Search (BFS)**
  - Explores all nodes level by level
  - Guarantees optimal solution for unweighted graphs
  - Applications: Shortest path, web crawling
  
- **Depth-First Search (DFS)**
  - Explores as far as possible before backtracking
  - Memory efficient but may not find optimal solution
  - Applications: Maze solving, topological sorting

#### Informed Search
- **A* Search Algorithm**
  - Uses heuristic function to guide search
  - Optimal if heuristic is admissible
  - Applications: GPS navigation, game AI

```python
def a_star_search(start, goal, graph, heuristic):
    """
    A* search algorithm implementation
    """
    open_set = [(0, start)]  # (f_score, node)
    came_from = {}
    g_score = {start: 0}
    
    while open_set:
        current_f, current = heappop(open_set)
        
        if current == goal:
            return reconstruct_path(came_from, current)
        
        for neighbor in graph[current]:
            tentative_g = g_score[current] + distance(current, neighbor)
            
            if neighbor not in g_score or tentative_g < g_score[neighbor]:
                came_from[neighbor] = current
                g_score[neighbor] = tentative_g
                f_score = tentative_g + heuristic(neighbor, goal)
                heappush(open_set, (f_score, neighbor))
    
    return None  # No path found
```

### 2. Optimization Algorithms
**Purpose**: Finding the best solution among many possibilities

#### Gradient-Based Optimization
- **Gradient Descent**
  - Iteratively moves toward minimum of function
  - Core algorithm for training neural networks
  - Variants: SGD, Adam, RMSprop

```python
def gradient_descent(f, df, x0, learning_rate=0.01, epochs=1000):
    """
    Basic gradient descent implementation
    f: objective function
    df: gradient of objective function
    x0: starting point
    """
    x = x0
    history = []
    
    for epoch in range(epochs):
        gradient = df(x)
        x = x - learning_rate * gradient
        history.append((x, f(x)))
        
        # Convergence check
        if abs(gradient) < 1e-6:
            break
    
    return x, history
```

#### Evolutionary Algorithms
- **Genetic Algorithm**
  - Mimics natural selection process
  - Good for complex optimization problems
  - Applications: Feature selection, neural architecture search

```python
def genetic_algorithm(fitness_func, pop_size=100, generations=1000):
    """
    Simple genetic algorithm implementation
    """
    population = initialize_population(pop_size)
    
    for generation in range(generations):
        # Evaluate fitness
        fitness_scores = [fitness_func(individual) for individual in population]
        
        # Selection
        parents = selection(population, fitness_scores)
        
        # Crossover and mutation
        offspring = []
        for i in range(0, len(parents), 2):
            child1, child2 = crossover(parents[i], parents[i+1])
            offspring.extend([mutate(child1), mutate(child2)])
        
        population = offspring
    
    return best_individual(population, fitness_func)
```

### 3. Machine Learning Algorithms

#### Supervised Learning
- **Linear Regression**
  - Predicts continuous values
  - Learns linear relationship between features and target
  - Simple but powerful baseline

- **Logistic Regression**
  - Binary and multiclass classification
  - Uses sigmoid function for probability estimation
  - Interpretable and fast

- **Decision Trees**
  - Tree-like model of decisions
  - Easy to interpret and visualize
  - Handles both numerical and categorical data

- **Random Forest**
  - Ensemble of decision trees
  - Reduces overfitting through averaging
  - Provides feature importance

- **Support Vector Machines (SVM)**
  - Finds optimal separating hyperplane
  - Effective for high-dimensional data
  - Can use kernel trick for non-linear problems

#### Unsupervised Learning
- **K-Means Clustering**
  - Partitions data into k clusters
  - Minimizes within-cluster sum of squares
  - Applications: Customer segmentation, image segmentation

```python
def k_means(data, k, max_iterations=100):
    """
    K-means clustering algorithm
    """
    centroids = initialize_centroids(data, k)
    
    for iteration in range(max_iterations):
        # Assign points to nearest centroid
        clusters = assign_to_clusters(data, centroids)
        
        # Update centroids
        new_centroids = update_centroids(clusters)
        
        # Check for convergence
        if centroids_converged(centroids, new_centroids):
            break
        
        centroids = new_centroids
    
    return centroids, clusters
```

- **Principal Component Analysis (PCA)**
  - Dimensionality reduction technique
  - Finds directions of maximum variance
  - Applications: Data visualization, feature reduction

#### Reinforcement Learning
- **Q-Learning**
  - Model-free reinforcement learning
  - Learns value of actions in states
  - Applications: Game playing, robotics

```python
class QLearningAgent:
    def __init__(self, state_size, action_size, learning_rate=0.1, 
                 discount_factor=0.9, epsilon=0.1):
        self.q_table = np.zeros((state_size, action_size))
        self.lr = learning_rate
        self.gamma = discount_factor
        self.epsilon = epsilon
    
    def choose_action(self, state):
        if random.random() < self.epsilon:
            return random.randint(0, self.action_size - 1)  # Explore
        return np.argmax(self.q_table[state])  # Exploit
    
    def update(self, state, action, reward, next_state):
        old_value = self.q_table[state, action]
        next_max = np.max(self.q_table[next_state])
        
        new_value = reward + self.gamma * next_max
        self.q_table[state, action] = old_value + self.lr * (new_value - old_value)
```

### 4. Deep Learning Algorithms

#### Neural Network Architectures
- **Feedforward Neural Networks**
  - Basic neural network structure
  - Information flows in one direction
  - Applications: Classification, regression

- **Convolutional Neural Networks (CNNs)**
  - Specialized for processing grid-like data
  - Uses convolution and pooling operations
  - Applications: Image recognition, computer vision

- **Recurrent Neural Networks (RNNs)**
  - Can process sequential data
  - Has memory through hidden states
  - Applications: Natural language processing, time series

- **Transformer Networks**
  - Attention-based architecture
  - Processes sequences in parallel
  - Applications: Language models, machine translation

#### Training Algorithms
- **Backpropagation**
  - Algorithm for training neural networks
  - Computes gradients using chain rule
  - Foundation of deep learning

```python
def backpropagation(network, X, y, learning_rate):
    """
    Simplified backpropagation algorithm
    """
    # Forward pass
    activations = forward_pass(network, X)
    
    # Compute loss
    loss = compute_loss(activations[-1], y)
    
    # Backward pass
    gradients = []
    delta = compute_output_delta(activations[-1], y)
    
    for layer in reversed(range(len(network))):
        # Compute gradients for weights and biases
        weight_grad = np.dot(activations[layer].T, delta)
        bias_grad = np.sum(delta, axis=0)
        gradients.append((weight_grad, bias_grad))
        
        # Compute delta for previous layer
        if layer > 0:
            delta = np.dot(delta, network[layer]['weights'].T) * \
                   activation_derivative(activations[layer])
    
    # Update weights
    gradients.reverse()
    for layer, (weight_grad, bias_grad) in enumerate(gradients):
        network[layer]['weights'] -= learning_rate * weight_grad
        network[layer]['biases'] -= learning_rate * bias_grad
    
    return loss
```

## 📊 Algorithm Comparison Matrix

| Algorithm Type | Data Requirements | Interpretability | Training Time | Prediction Speed | Best Use Cases |
|----------------|------------------|------------------|---------------|------------------|----------------|
| **Linear Regression** | Small-Medium | High | Fast | Very Fast | Simple relationships |
| **Decision Trees** | Small-Medium | High | Fast | Fast | Rule-based decisions |
| **Random Forest** | Medium | Medium | Medium | Fast | General classification |
| **SVM** | Medium | Low | Medium | Fast | High-dimensional data |
| **Neural Networks** | Large | Low | Slow | Fast | Complex patterns |
| **K-Means** | Medium | Medium | Fast | Very Fast | Clustering |
| **Q-Learning** | N/A (Interactive) | Medium | Very Slow | Fast | Sequential decisions |

## 🛠️ Algorithm Selection Guide

### Classification Problems
```
Data Size: Small (< 1K samples)
├─ Linear/Simple: Logistic Regression
└─ Non-linear: Decision Tree

Data Size: Medium (1K-100K samples)  
├─ Interpretable: Random Forest
├─ High Performance: SVM
└─ Feature Rich: Gradient Boosting

Data Size: Large (> 100K samples)
├─ Structured Data: XGBoost/LightGBM
├─ Images: Convolutional Neural Network
├─ Text: Transformer/BERT-based models
└─ General: Deep Neural Network
```

### Regression Problems
```
Linear Relationship: Linear Regression
Non-linear Relationship: 
├─ Small Data: Polynomial Regression
├─ Medium Data: Random Forest Regressor
└─ Large Data: Neural Network

Time Series:
├─ Traditional: ARIMA, Prophet
└─ Deep Learning: LSTM, Transformer
```

### Clustering Problems
```
Spherical Clusters: K-Means
Arbitrary Shapes: DBSCAN
Hierarchical Structure: Agglomerative Clustering
High Dimensions: Spectral Clustering
```

## 🧪 Practical Implementation Examples

### Example 1: Text Classification Pipeline
```python
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report

def text_classification_pipeline(texts, labels):
    """
    Complete text classification pipeline
    """
    # Text preprocessing and feature extraction
    vectorizer = TfidfVectorizer(max_features=5000, stop_words='english')
    X = vectorizer.fit_transform(texts)
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, labels, test_size=0.2, random_state=42
    )
    
    # Train model
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    
    # Evaluate
    predictions = model.predict(X_test)
    performance = classification_report(y_test, predictions)
    
    return model, vectorizer, performance

# Usage
model, vectorizer, results = text_classification_pipeline(documents, categories)
print("Classification Results:", results)
```

### Example 2: Image Classification with CNN
```python
import tensorflow as tf
from tensorflow.keras import layers, models

def create_cnn_model(input_shape, num_classes):
    """
    Create a simple CNN for image classification
    """
    model = models.Sequential([
        # Convolutional layers
        layers.Conv2D(32, (3, 3), activation='relu', input_shape=input_shape),
        layers.MaxPooling2D((2, 2)),
        layers.Conv2D(64, (3, 3), activation='relu'),
        layers.MaxPooling2D((2, 2)),
        layers.Conv2D(64, (3, 3), activation='relu'),
        
        # Dense layers
        layers.Flatten(),
        layers.Dense(64, activation='relu'),
        layers.Dropout(0.5),
        layers.Dense(num_classes, activation='softmax')
    ])
    
    model.compile(
        optimizer='adam',
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    return model

# Usage
model = create_cnn_model(input_shape=(32, 32, 3), num_classes=10)
model.fit(X_train, y_train, epochs=20, validation_data=(X_val, y_val))
```

### Example 3: Recommendation System
```python
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

class CollaborativeFiltering:
    def __init__(self):
        self.user_item_matrix = None
        self.user_similarity = None
    
    def fit(self, ratings_matrix):
        """
        Fit collaborative filtering model
        ratings_matrix: users x items matrix
        """
        self.user_item_matrix = ratings_matrix
        # Compute user similarity
        self.user_similarity = cosine_similarity(ratings_matrix)
    
    def predict(self, user_id, item_id, k=5):
        """
        Predict rating for user-item pair
        """
        # Find k most similar users
        similarities = self.user_similarity[user_id]
        similar_users = np.argsort(similarities)[-k-1:-1]
        
        # Weighted average of similar users' ratings
        numerator = 0
        denominator = 0
        
        for similar_user in similar_users:
            if self.user_item_matrix[similar_user, item_id] > 0:
                similarity = similarities[similar_user]
                rating = self.user_item_matrix[similar_user, item_id]
                numerator += similarity * rating
                denominator += similarity
        
        if denominator == 0:
            return 0
        
        return numerator / denominator
    
    def recommend(self, user_id, n_recommendations=10):
        """
        Recommend top n items for user
        """
        user_ratings = self.user_item_matrix[user_id]
        unrated_items = np.where(user_ratings == 0)[0]
        
        predictions = []
        for item_id in unrated_items:
            pred_rating = self.predict(user_id, item_id)
            predictions.append((item_id, pred_rating))
        
        # Sort by predicted rating
        predictions.sort(key=lambda x: x[1], reverse=True)
        
        return predictions[:n_recommendations]

# Usage
cf_model = CollaborativeFiltering()
cf_model.fit(user_item_ratings)
recommendations = cf_model.recommend(user_id=123, n_recommendations=5)
```

## 🎯 Hands-on Exercises

### Exercise 1: Algorithm Performance Comparison
**Task**: Compare the performance of different classification algorithms on a dataset.

```python
from sklearn.datasets import make_classification
from sklearn.model_selection import cross_val_score
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC

# Generate sample dataset
X, y = make_classification(n_samples=1000, n_features=20, n_classes=2, random_state=42)

# Define algorithms to compare
algorithms = {
    'Logistic Regression': LogisticRegression(),
    'Decision Tree': DecisionTreeClassifier(),
    'Random Forest': RandomForestClassifier(),
    'SVM': SVC()
}

# Compare performance
results = {}
for name, algorithm in algorithms.items():
    scores = cross_val_score(algorithm, X, y, cv=5, scoring='accuracy')
    results[name] = {
        'mean_accuracy': scores.mean(),
        'std_accuracy': scores.std()
    }

# Print results
for name, metrics in results.items():
    print(f"{name}: {metrics['mean_accuracy']:.3f} (+/- {metrics['std_accuracy']*2:.3f})")
```

### Exercise 2: Implement Simple Neural Network
**Task**: Build a neural network from scratch using NumPy.

```python
import numpy as np

class SimpleNeuralNetwork:
    def __init__(self, input_size, hidden_size, output_size):
        # Initialize weights randomly
        self.W1 = np.random.randn(input_size, hidden_size) * 0.1
        self.b1 = np.zeros((1, hidden_size))
        self.W2 = np.random.randn(hidden_size, output_size) * 0.1
        self.b2 = np.zeros((1, output_size))
    
    def sigmoid(self, x):
        return 1 / (1 + np.exp(-np.clip(x, -250, 250)))
    
    def sigmoid_derivative(self, x):
        return x * (1 - x)
    
    def forward(self, X):
        self.z1 = np.dot(X, self.W1) + self.b1
        self.a1 = self.sigmoid(self.z1)
        self.z2 = np.dot(self.a1, self.W2) + self.b2
        self.a2 = self.sigmoid(self.z2)
        return self.a2
    
    def backward(self, X, y, output):
        m = X.shape[0]
        
        # Calculate gradients
        dZ2 = output - y
        dW2 = (1/m) * np.dot(self.a1.T, dZ2)
        db2 = (1/m) * np.sum(dZ2, axis=0, keepdims=True)
        
        dA1 = np.dot(dZ2, self.W2.T)
        dZ1 = dA1 * self.sigmoid_derivative(self.a1)
        dW1 = (1/m) * np.dot(X.T, dZ1)
        db1 = (1/m) * np.sum(dZ1, axis=0, keepdims=True)
        
        return dW1, db1, dW2, db2
    
    def train(self, X, y, epochs, learning_rate):
        for epoch in range(epochs):
            # Forward pass
            output = self.forward(X)
            
            # Calculate loss
            loss = np.mean(np.square(y - output))
            
            # Backward pass
            dW1, db1, dW2, db2 = self.backward(X, y, output)
            
            # Update weights
            self.W1 -= learning_rate * dW1
            self.b1 -= learning_rate * db1
            self.W2 -= learning_rate * dW2
            self.b2 -= learning_rate * db2
            
            if epoch % 100 == 0:
                print(f"Epoch {epoch}, Loss: {loss:.4f}")

# Test the neural network
# Your task: Create sample data and train the network
```

## ✅ Self-Assessment Questions

### Conceptual Understanding
1. **When would you choose a decision tree over a neural network?**
2. **What are the main advantages of ensemble methods like Random Forest?**
3. **Why is the backpropagation algorithm important for deep learning?**
4. **What makes reinforcement learning different from supervised learning?**

### Practical Application
1. **You have a dataset with 500 samples and 50 features. Which algorithms would you try first and why?**
2. **How would you approach a problem where you need to predict house prices based on location, size, and amenities?**
3. **What preprocessing steps are typically needed before applying different algorithms?**

### Algorithm Selection
1. **For each scenario, recommend the most appropriate algorithm:**
   - Detecting spam emails (10,000 emails dataset)
   - Recognizing handwritten digits (60,000 image dataset)
   - Grouping customers by purchasing behavior (no labels)
   - Playing a strategic board game
   - Predicting stock prices based on historical data

## 🔗 Real-World Applications

### Healthcare
- **Diagnostic Imaging**: CNNs for analyzing X-rays, MRIs
- **Drug Discovery**: Genetic algorithms for molecular optimization
- **Treatment Recommendation**: Decision trees for clinical decision support

### Finance
- **Fraud Detection**: Anomaly detection algorithms
- **Algorithmic Trading**: Reinforcement learning for strategy optimization
- **Credit Scoring**: Ensemble methods for risk assessment

### Technology
- **Search Engines**: PageRank algorithm for ranking web pages
- **Recommendation Systems**: Collaborative filtering and matrix factorization
- **Computer Vision**: Object detection using CNN architectures

### Transportation
- **Route Optimization**: A* search and genetic algorithms
- **Autonomous Vehicles**: Deep reinforcement learning for control
- **Traffic Management**: Optimization algorithms for signal timing

## 📚 Further Reading

### Books
- "Introduction to Algorithms" - Cormen, Leiserson, Rivest, Stein
- "Pattern Recognition and Machine Learning" - Christopher Bishop
- "The Elements of Statistical Learning" - Hastie, Tibshirani, Friedman

### Online Resources
- [Coursera: Machine Learning](https://www.coursera.org/learn/machine-learning)
- [MIT 6.006: Introduction to Algorithms](https://ocw.mit.edu/courses/electrical-engineering-and-computer-science/6-006-introduction-to-algorithms-fall-2011/)
- [Fast.ai: Practical Deep Learning](https://course.fast.ai/)

### Implementation Resources
- [Scikit-learn Documentation](https://scikit-learn.org/stable/)
- [TensorFlow Tutorials](https://www.tensorflow.org/tutorials)
- [PyTorch Tutorials](https://pytorch.org/tutorials/)

---

**Next Module**: [Industry Landscape](../industry-landscape/README.md)

*Estimated Study Time: 6-8 hours including exercises and implementations*
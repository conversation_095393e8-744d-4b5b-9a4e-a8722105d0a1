# File Operations with Google Cloud Storage

## Overview

This guide provides practical code examples for common file operations in healthcare applications, including secure uploads, downloads, metadata management, and batch operations.

## Basic File Operations

### 1. Secure File Upload

```python
from google.cloud import storage
from google.cloud.exceptions import GoogleCloudError
import hashlib
import mimetypes
import os
from datetime import datetime, timedelta

class HealthcareFileManager:
    def __init__(self, project_id, bucket_name):
        self.client = storage.Client(project=project_id)
        self.bucket = self.client.bucket(bucket_name)
        
    def upload_patient_file(self, local_file_path, patient_id, file_type, metadata=None):
        """
        Upload patient file with healthcare-specific metadata and validation
        """
        try:
            # Validate file
            if not os.path.exists(local_file_path):
                raise FileNotFoundError(f"File not found: {local_file_path}")
            
            # Generate secure file path
            file_extension = os.path.splitext(local_file_path)[1]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            blob_name = f"patients/{patient_id}/{file_type}/{timestamp}{file_extension}"
            
            # Create blob
            blob = self.bucket.blob(blob_name)
            
            # Set content type
            content_type, _ = mimetypes.guess_type(local_file_path)
            if content_type:
                blob.content_type = content_type
            
            # Calculate file hash for integrity
            with open(local_file_path, 'rb') as f:
                file_content = f.read()
                file_hash = hashlib.sha256(file_content).hexdigest()
            
            # Set healthcare-specific metadata
            healthcare_metadata = {
                'patient_id': patient_id,
                'file_type': file_type,
                'upload_timestamp': datetime.now().isoformat(),
                'file_hash': file_hash,
                'file_size': str(len(file_content)),
                'hipaa_compliant': 'true'
            }
            
            # Add custom metadata if provided
            if metadata:
                healthcare_metadata.update(metadata)
            
            blob.metadata = healthcare_metadata
            
            # Upload file
            blob.upload_from_filename(local_file_path)
            
            # Verify upload integrity
            blob.reload()
            if blob.size != len(file_content):
                raise Exception("Upload integrity check failed")
            
            return {
                'success': True,
                'blob_name': blob_name,
                'file_hash': file_hash,
                'upload_time': healthcare_metadata['upload_timestamp'],
                'public_url': None,  # Never expose PHI publicly
                'signed_url_expires': None
            }
            
        except GoogleCloudError as e:
            return {
                'success': False,
                'error': f"Google Cloud error: {str(e)}",
                'blob_name': None
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"Upload error: {str(e)}",
                'blob_name': None
            }

# Usage example
file_manager = HealthcareFileManager("my-healthcare-project", "patient-files")

# Upload medical record
result = file_manager.upload_patient_file(
    local_file_path="/path/to/patient_record.pdf",
    patient_id="patient_12345",
    file_type="medical_records",
    metadata={
        'doctor_id': 'dr_smith_001',
        'department': 'cardiology',
        'document_type': 'consultation_notes'
    }
)

print(f"Upload result: {result}")
```

### 2. Secure File Download

```python
def download_patient_file(self, blob_name, local_file_path, verify_integrity=True):
    """
    Download patient file with integrity verification
    """
    try:
        blob = self.bucket.blob(blob_name)
        
        # Check if file exists
        if not blob.exists():
            return {
                'success': False,
                'error': 'File not found',
                'local_path': None
            }
        
        # Get file metadata
        blob.reload()
        original_hash = blob.metadata.get('file_hash') if blob.metadata else None
        
        # Download file
        blob.download_to_filename(local_file_path)
        
        # Verify integrity if hash is available
        if verify_integrity and original_hash:
            with open(local_file_path, 'rb') as f:
                downloaded_content = f.read()
                downloaded_hash = hashlib.sha256(downloaded_content).hexdigest()
                
                if downloaded_hash != original_hash:
                    os.remove(local_file_path)  # Remove corrupted file
                    return {
                        'success': False,
                        'error': 'File integrity check failed',
                        'local_path': None
                    }
        
        return {
            'success': True,
            'local_path': local_file_path,
            'metadata': dict(blob.metadata) if blob.metadata else {},
            'file_size': blob.size,
            'last_modified': blob.updated.isoformat() if blob.updated else None
        }
        
    except GoogleCloudError as e:
        return {
            'success': False,
            'error': f"Google Cloud error: {str(e)}",
            'local_path': None
        }
    except Exception as e:
        return {
            'success': False,
            'error': f"Download error: {str(e)}",
            'local_path': None
        }

# Add to HealthcareFileManager class
HealthcareFileManager.download_patient_file = download_patient_file

# Usage
download_result = file_manager.download_patient_file(
    blob_name="patients/patient_12345/medical_records/20240617_143022.pdf",
    local_file_path="/tmp/downloaded_record.pdf"
)

print(f"Download result: {download_result}")
```

### 3. Generate Signed URLs for Secure Access

```python
def generate_secure_access_url(self, blob_name, expiration_hours=1, method='GET'):
    """
    Generate signed URL for secure, temporary access to patient files
    """
    try:
        blob = self.bucket.blob(blob_name)
        
        # Check if file exists
        if not blob.exists():
            return {
                'success': False,
                'error': 'File not found',
                'signed_url': None
            }
        
        # Set expiration (max 7 days for security)
        max_hours = min(expiration_hours, 168)  # 7 days max
        expiration = datetime.utcnow() + timedelta(hours=max_hours)
        
        # Generate signed URL
        signed_url = blob.generate_signed_url(
            version="v4",
            expiration=expiration,
            method=method,
            headers={'Content-Type': blob.content_type} if method == 'PUT' else None
        )
        
        # Log access for audit trail
        self._log_file_access(blob_name, method, expiration)
        
        return {
            'success': True,
            'signed_url': signed_url,
            'expires_at': expiration.isoformat(),
            'method': method,
            'blob_name': blob_name
        }
        
    except GoogleCloudError as e:
        return {
            'success': False,
            'error': f"Google Cloud error: {str(e)}",
            'signed_url': None
        }
    except Exception as e:
        return {
            'success': False,
            'error': f"Signed URL error: {str(e)}",
            'signed_url': None
        }

def _log_file_access(self, blob_name, method, expiration):
    """Log file access for audit purposes"""
    from google.cloud import logging
    
    logging_client = logging.Client()
    logger = logging_client.logger('healthcare-file-access')
    
    logger.log_struct({
        'event_type': 'signed_url_generated',
        'blob_name': blob_name,
        'method': method,
        'expiration': expiration.isoformat(),
        'timestamp': datetime.utcnow().isoformat(),
        'bucket_name': self.bucket.name
    }, severity='INFO')

# Add to HealthcareFileManager class
HealthcareFileManager.generate_secure_access_url = generate_secure_access_url
HealthcareFileManager._log_file_access = _log_file_access

# Usage for doctor to view patient file
access_url = file_manager.generate_secure_access_url(
    blob_name="patients/patient_12345/medical_records/20240617_143022.pdf",
    expiration_hours=2
)

print(f"Secure access URL: {access_url['signed_url']}")
```

## Advanced File Operations

### 4. Batch File Operations

```python
def batch_upload_patient_files(self, file_list, patient_id, file_type):
    """
    Upload multiple files for a patient in batch
    """
    results = []
    
    for file_info in file_list:
        local_path = file_info['local_path']
        metadata = file_info.get('metadata', {})
        
        result = self.upload_patient_file(
            local_file_path=local_path,
            patient_id=patient_id,
            file_type=file_type,
            metadata=metadata
        )
        
        results.append({
            'local_path': local_path,
            'result': result
        })
    
    # Summary
    successful_uploads = sum(1 for r in results if r['result']['success'])
    failed_uploads = len(results) - successful_uploads
    
    return {
        'total_files': len(results),
        'successful_uploads': successful_uploads,
        'failed_uploads': failed_uploads,
        'details': results
    }

def list_patient_files(self, patient_id, file_type=None, limit=100):
    """
    List all files for a specific patient
    """
    try:
        prefix = f"patients/{patient_id}/"
        if file_type:
            prefix += f"{file_type}/"
        
        blobs = self.bucket.list_blobs(prefix=prefix, max_results=limit)
        
        files = []
        for blob in blobs:
            blob.reload()  # Get metadata
            
            file_info = {
                'name': blob.name,
                'size': blob.size,
                'created': blob.time_created.isoformat() if blob.time_created else None,
                'updated': blob.updated.isoformat() if blob.updated else None,
                'content_type': blob.content_type,
                'metadata': dict(blob.metadata) if blob.metadata else {}
            }
            files.append(file_info)
        
        return {
            'success': True,
            'patient_id': patient_id,
            'file_type': file_type,
            'total_files': len(files),
            'files': files
        }
        
    except GoogleCloudError as e:
        return {
            'success': False,
            'error': f"Google Cloud error: {str(e)}",
            'files': []
        }

# Add to HealthcareFileManager class
HealthcareFileManager.batch_upload_patient_files = batch_upload_patient_files
HealthcareFileManager.list_patient_files = list_patient_files

# Usage examples
# Batch upload
file_list = [
    {
        'local_path': '/path/to/xray1.jpg',
        'metadata': {'exam_type': 'chest_xray', 'technician': 'tech_001'}
    },
    {
        'local_path': '/path/to/xray2.jpg',
        'metadata': {'exam_type': 'chest_xray', 'technician': 'tech_001'}
    }
]

batch_result = file_manager.batch_upload_patient_files(
    file_list=file_list,
    patient_id="patient_12345",
    file_type="radiology"
)

# List patient files
patient_files = file_manager.list_patient_files(
    patient_id="patient_12345",
    file_type="radiology"
)
```

### 5. File Metadata Management

```python
def update_file_metadata(self, blob_name, new_metadata, merge=True):
    """
    Update file metadata for healthcare compliance
    """
    try:
        blob = self.bucket.blob(blob_name)
        
        if not blob.exists():
            return {
                'success': False,
                'error': 'File not found'
            }
        
        blob.reload()
        
        if merge and blob.metadata:
            # Merge with existing metadata
            updated_metadata = dict(blob.metadata)
            updated_metadata.update(new_metadata)
        else:
            # Replace metadata
            updated_metadata = new_metadata
        
        # Add update timestamp
        updated_metadata['last_metadata_update'] = datetime.now().isoformat()
        
        # Update metadata
        blob.metadata = updated_metadata
        blob.patch()
        
        return {
            'success': True,
            'blob_name': blob_name,
            'updated_metadata': updated_metadata
        }
        
    except GoogleCloudError as e:
        return {
            'success': False,
            'error': f"Google Cloud error: {str(e)}"
        }

def search_files_by_metadata(self, search_criteria, limit=100):
    """
    Search files based on metadata criteria
    """
    try:
        matching_files = []
        
        # List all blobs (in production, use more efficient filtering)
        blobs = self.bucket.list_blobs(max_results=limit * 10)  # Get more to filter
        
        for blob in blobs:
            blob.reload()
            
            if not blob.metadata:
                continue
            
            # Check if blob matches search criteria
            matches = True
            for key, value in search_criteria.items():
                if key not in blob.metadata or blob.metadata[key] != value:
                    matches = False
                    break
            
            if matches:
                matching_files.append({
                    'name': blob.name,
                    'size': blob.size,
                    'created': blob.time_created.isoformat() if blob.time_created else None,
                    'metadata': dict(blob.metadata)
                })
                
                if len(matching_files) >= limit:
                    break
        
        return {
            'success': True,
            'search_criteria': search_criteria,
            'total_matches': len(matching_files),
            'files': matching_files
        }
        
    except GoogleCloudError as e:
        return {
            'success': False,
            'error': f"Google Cloud error: {str(e)}",
            'files': []
        }

# Add to HealthcareFileManager class
HealthcareFileManager.update_file_metadata = update_file_metadata
HealthcareFileManager.search_files_by_metadata = search_files_by_metadata

# Usage examples
# Update metadata
metadata_update = file_manager.update_file_metadata(
    blob_name="patients/patient_12345/medical_records/20240617_143022.pdf",
    new_metadata={
        'reviewed_by': 'dr_smith_001',
        'review_date': '2024-06-17',
        'status': 'reviewed'
    }
)

# Search files by metadata
search_results = file_manager.search_files_by_metadata(
    search_criteria={
        'doctor_id': 'dr_smith_001',
        'department': 'cardiology'
    }
)
```

### 6. File Deletion with Audit Trail

```python
def delete_patient_file(self, blob_name, reason, deleted_by):
    """
    Delete patient file with proper audit trail
    """
    try:
        blob = self.bucket.blob(blob_name)
        
        if not blob.exists():
            return {
                'success': False,
                'error': 'File not found'
            }
        
        # Get file info before deletion
        blob.reload()
        file_info = {
            'name': blob.name,
            'size': blob.size,
            'created': blob.time_created.isoformat() if blob.time_created else None,
            'metadata': dict(blob.metadata) if blob.metadata else {}
        }
        
        # Log deletion for audit
        self._log_file_deletion(blob_name, reason, deleted_by, file_info)
        
        # Delete the file
        blob.delete()
        
        return {
            'success': True,
            'deleted_file': file_info,
            'deletion_reason': reason,
            'deleted_by': deleted_by,
            'deletion_time': datetime.now().isoformat()
        }
        
    except GoogleCloudError as e:
        return {
            'success': False,
            'error': f"Google Cloud error: {str(e)}"
        }

def _log_file_deletion(self, blob_name, reason, deleted_by, file_info):
    """Log file deletion for audit purposes"""
    from google.cloud import logging
    
    logging_client = logging.Client()
    logger = logging_client.logger('healthcare-file-deletion')
    
    logger.log_struct({
        'event_type': 'file_deleted',
        'blob_name': blob_name,
        'deletion_reason': reason,
        'deleted_by': deleted_by,
        'file_info': file_info,
        'timestamp': datetime.utcnow().isoformat(),
        'bucket_name': self.bucket.name
    }, severity='WARNING')

# Add to HealthcareFileManager class
HealthcareFileManager.delete_patient_file = delete_patient_file
HealthcareFileManager._log_file_deletion = _log_file_deletion

# Usage
deletion_result = file_manager.delete_patient_file(
    blob_name="patients/patient_12345/medical_records/old_record.pdf",
    reason="Patient requested deletion",
    deleted_by="admin_user_001"
)
```

## Error Handling and Retry Logic

```python
import time
from google.api_core import retry
from google.api_core import exceptions

def upload_with_retry(self, local_file_path, patient_id, file_type, max_retries=3):
    """
    Upload file with retry logic for reliability
    """
    @retry.Retry(
        predicate=retry.if_exception_type(
            exceptions.ServiceUnavailable,
            exceptions.TooManyRequests,
            exceptions.InternalServerError
        ),
        deadline=300.0  # 5 minutes total
    )
    def _upload_with_retry():
        return self.upload_patient_file(local_file_path, patient_id, file_type)
    
    try:
        return _upload_with_retry()
    except Exception as e:
        return {
            'success': False,
            'error': f"Upload failed after retries: {str(e)}",
            'blob_name': None
        }

# Add to HealthcareFileManager class
HealthcareFileManager.upload_with_retry = upload_with_retry
```

## Usage in Healthcare Application

```python
# Initialize file manager
file_manager = HealthcareFileManager("healthcare-project", "patient-files")

# Example: Doctor uploads consultation notes
upload_result = file_manager.upload_patient_file(
    local_file_path="/tmp/consultation_notes.pdf",
    patient_id="patient_12345",
    file_type="consultation_notes",
    metadata={
        'doctor_id': 'dr_smith_001',
        'consultation_date': '2024-06-17',
        'department': 'cardiology'
    }
)

if upload_result['success']:
    # Generate secure URL for patient to view
    access_url = file_manager.generate_secure_access_url(
        blob_name=upload_result['blob_name'],
        expiration_hours=24
    )
    
    # Send URL to patient (via secure channel)
    print(f"File uploaded successfully. Access URL: {access_url['signed_url']}")
else:
    print(f"Upload failed: {upload_result['error']}")
```

## Next Steps

1. **[Signed URLs](./signed-urls.md)** - Advanced signed URL patterns
2. **[Integration Examples](./integration-examples.md)** - Full application integration
3. **[Authentication Setup](./authentication.md)** - Service account configuration

---

*These file operations provide a secure, HIPAA-compliant foundation for managing patient files in your healthcare platform.*

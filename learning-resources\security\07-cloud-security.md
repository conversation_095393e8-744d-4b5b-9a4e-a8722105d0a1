# Cloud Security

## Overview

Cloud security is essential for healthcare platforms that leverage cloud infrastructure to store sensitive patient data and process payments. This guide covers comprehensive security measures for major cloud providers (AWS, Azure, GCP) with specific focus on healthcare compliance (HIPAA) and payment processing (PCI DSS) requirements.

## 🎯 Learning Objectives

By the end of this module, you will understand:

- Cloud security fundamentals and shared responsibility model
- Identity and Access Management (IAM) best practices
- Data encryption and key management in the cloud
- Network security and VPC configuration
- Compliance frameworks for healthcare and payments
- Cloud security monitoring and incident response

## ☁️ Cloud Security Fundamentals

### 1. Shared Responsibility Model

```yaml
# Cloud Security Responsibility Matrix
Provider Responsibilities:
  - Physical security of data centers
  - Infrastructure hardware and software
  - Network controls and hypervisor security
  - Host operating system patching
  - Service availability and redundancy

Customer Responsibilities:
  - Data encryption and classification
  - Identity and access management
  - Operating system and application patching
  - Network traffic protection
  - Firewall configuration
  - Application-level security
```

### 2. Healthcare Cloud Architecture

```yaml
# AWS Healthcare Architecture Example
apiVersion: v1
kind: ConfigMap
metadata:
  name: healthcare-cloud-config
data:
  architecture.yaml: |
    # Multi-tier architecture for healthcare platform
    presentation_tier:
      - CloudFront (CDN with WAF)
      - Application Load Balancer
      - Auto Scaling Groups

    application_tier:
      - ECS Fargate containers
      - Lambda functions for serverless processing
      - API Gateway for REST APIs

    data_tier:
      - RDS with encryption at rest
      - ElastiCache for session storage
      - S3 for file storage with versioning

    security_services:
      - AWS WAF for application protection
      - GuardDuty for threat detection
      - CloudTrail for audit logging
      - KMS for key management
      - Secrets Manager for credential storage
```

## 🔐 Identity and Access Management (IAM)

### 1. AWS IAM Best Practices

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "HealthcareDataAccess",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::ACCOUNT:role/HealthcareAppRole"
      },
      "Action": ["s3:GetObject", "s3:PutObject"],
      "Resource": "arn:aws:s3:::healthcare-patient-data/*",
      "Condition": {
        "StringEquals": {
          "s3:x-amz-server-side-encryption": "aws:kms"
        },
        "Bool": {
          "aws:SecureTransport": "true"
        },
        "DateGreaterThan": {
          "aws:CurrentTime": "2024-01-01T00:00:00Z"
        }
      }
    }
  ]
}
```

```javascript
// Terraform configuration for healthcare IAM roles
const healthcareIAMConfig = `
# Healthcare application role
resource "aws_iam_role" "healthcare_app_role" {
  name = "healthcare-app-role"
  
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
        Condition = {
          StringEquals = {
            "aws:RequestedRegion" = ["us-east-1", "us-west-2"]
          }
        }
      }
    ]
  })

  tags = {
    Environment = "production"
    Compliance = "HIPAA"
    DataClassification = "PHI"
  }
}

# Policy for patient data access
resource "aws_iam_policy" "patient_data_policy" {
  name = "patient-data-access"
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject"
        ]
        Resource = "arn:aws:s3:::healthcare-patient-data/*"
        Condition = {
          StringEquals = {
            "s3:x-amz-server-side-encryption" = "aws:kms"
            "s3:x-amz-server-side-encryption-aws-kms-key-id" = aws_kms_key.patient_data_key.arn
          }
        }
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey"
        ]
        Resource = aws_kms_key.patient_data_key.arn
        Condition = {
          StringEquals = {
            "kms:ViaService" = "s3.us-east-1.amazonaws.com"
          }
        }
      }
    ]
  })
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "healthcare_app_policy" {
  role       = aws_iam_role.healthcare_app_role.name
  policy_arn = aws_iam_policy.patient_data_policy.arn
}
`;
```

### 2. Multi-Factor Authentication (MFA) Enforcement

```javascript
// AWS IAM policy requiring MFA for sensitive operations
const mfaPolicy = {
  Version: "2012-10-17",
  Statement: [
    {
      Sid: "AllowViewAccountInfo",
      Effect: "Allow",
      Action: ["iam:GetAccountPasswordPolicy", "iam:ListVirtualMFADevices"],
      Resource: "*",
    },
    {
      Sid: "AllowManageOwnPasswords",
      Effect: "Allow",
      Action: ["iam:ChangePassword", "iam:GetUser"],
      Resource: "arn:aws:iam::*:user/${aws:username}",
    },
    {
      Sid: "AllowManageOwnMFA",
      Effect: "Allow",
      Action: [
        "iam:CreateVirtualMFADevice",
        "iam:DeleteVirtualMFADevice",
        "iam:EnableMFADevice",
        "iam:ResyncMFADevice",
      ],
      Resource: [
        "arn:aws:iam::*:mfa/${aws:username}",
        "arn:aws:iam::*:user/${aws:username}",
      ],
    },
    {
      Sid: "DenyAllExceptUnlessSignedInWithMFA",
      Effect: "Deny",
      NotAction: [
        "iam:CreateVirtualMFADevice",
        "iam:EnableMFADevice",
        "iam:GetUser",
        "iam:ListMFADevices",
        "iam:ListVirtualMFADevices",
        "iam:ResyncMFADevice",
        "sts:GetSessionToken",
      ],
      Resource: "*",
      Condition: {
        BoolIfExists: {
          "aws:MultiFactorAuthPresent": "false",
        },
      },
    },
  ],
};

// Azure AD Conditional Access for healthcare platform
const azureConditionalAccess = `
# Azure AD Conditional Access Policy
resource "azuread_conditional_access_policy" "healthcare_mfa" {
  display_name = "Healthcare Platform - Require MFA"
  state        = "enabled"

  conditions {
    applications {
      included_applications = [azuread_application.healthcare_app.application_id]
    }
    
    users {
      included_groups = [azuread_group.healthcare_users.id]
    }
    
    locations {
      included_locations = ["All"]
      excluded_locations = [azuread_named_location.trusted_locations.id]
    }
    
    client_app_types = ["browser", "mobileAppsAndDesktopClients"]
  }

  grant_controls {
    operator          = "OR"
    built_in_controls = ["mfa"]
  }

  session_controls {
    sign_in_frequency = 4
    sign_in_frequency_period = "hours"
  }
}
`;
```

## 🔒 Data Encryption and Key Management

### 1. AWS KMS Configuration for Healthcare

```javascript
// Terraform configuration for healthcare KMS keys
const kmsConfig = `
# Customer Managed Key for Patient Data
resource "aws_kms_key" "patient_data_key" {
  description             = "KMS key for patient data encryption"
  deletion_window_in_days = 30
  enable_key_rotation     = true
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Enable IAM User Permissions"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "Allow use of the key for healthcare services"
        Effect = "Allow"
        Principal = {
          AWS = [
            aws_iam_role.healthcare_app_role.arn,
            aws_iam_role.healthcare_backup_role.arn
          ]
        }
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Resource = "*"
        Condition = {
          StringEquals = {
            "kms:ViaService" = [
              "s3.us-east-1.amazonaws.com",
              "rds.us-east-1.amazonaws.com"
            ]
          }
        }
      }
    ]
  })

  tags = {
    Name        = "healthcare-patient-data"
    Environment = "production"
    Compliance  = "HIPAA"
  }
}

# Key alias for easier reference
resource "aws_kms_alias" "patient_data_key_alias" {
  name          = "alias/healthcare-patient-data"
  target_key_id = aws_kms_key.patient_data_key.key_id
}

# Separate key for payment data (PCI DSS compliance)
resource "aws_kms_key" "payment_data_key" {
  description             = "KMS key for payment data encryption"
  deletion_window_in_days = 30
  enable_key_rotation     = true
  
  tags = {
    Name        = "healthcare-payment-data"
    Environment = "production"
    Compliance  = "PCI-DSS"
  }
}
`;

// Application-level encryption using AWS KMS
class HealthcareEncryption {
  constructor() {
    this.kmsClient = new AWS.KMS({ region: "us-east-1" });
    this.patientDataKeyId = "alias/healthcare-patient-data";
    this.paymentDataKeyId = "alias/healthcare-payment-data";
  }

  async encryptPatientData(plaintext) {
    try {
      const params = {
        KeyId: this.patientDataKeyId,
        Plaintext: JSON.stringify(plaintext),
        EncryptionContext: {
          purpose: "patient-data",
          application: "healthcare-platform",
        },
      };

      const result = await this.kmsClient.encrypt(params).promise();
      return {
        encryptedData: result.CiphertextBlob.toString("base64"),
        keyId: result.KeyId,
      };
    } catch (error) {
      throw new Error(`Encryption failed: ${error.message}`);
    }
  }

  async decryptPatientData(encryptedData) {
    try {
      const params = {
        CiphertextBlob: Buffer.from(encryptedData, "base64"),
        EncryptionContext: {
          purpose: "patient-data",
          application: "healthcare-platform",
        },
      };

      const result = await this.kmsClient.decrypt(params).promise();
      return JSON.parse(result.Plaintext.toString());
    } catch (error) {
      throw new Error(`Decryption failed: ${error.message}`);
    }
  }

  async encryptPaymentData(plaintext) {
    try {
      const params = {
        KeyId: this.paymentDataKeyId,
        Plaintext: JSON.stringify(plaintext),
        EncryptionContext: {
          purpose: "payment-data",
          application: "healthcare-platform",
          compliance: "PCI-DSS",
        },
      };

      const result = await this.kmsClient.encrypt(params).promise();
      return {
        encryptedData: result.CiphertextBlob.toString("base64"),
        keyId: result.KeyId,
      };
    } catch (error) {
      throw new Error(`Payment encryption failed: ${error.message}`);
    }
  }
}
```

### 2. Database Encryption Configuration

```javascript
// RDS encryption configuration for healthcare data
const rdsConfig = `
# Encrypted RDS instance for patient data
resource "aws_db_instance" "healthcare_db" {
  identifier = "healthcare-patient-db"
  
  engine         = "postgres"
  engine_version = "14.9"
  instance_class = "db.r5.xlarge"
  
  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_type         = "gp3"
  storage_encrypted    = true
  kms_key_id          = aws_kms_key.patient_data_key.arn
  
  db_name  = "healthcare"
  username = "healthcareapp"
  password = random_password.db_password.result
  
  # Network security
  db_subnet_group_name   = aws_db_subnet_group.healthcare_db_subnet_group.name
  vpc_security_group_ids = [aws_security_group.healthcare_db_sg.id]
  
  # Backup and maintenance
  backup_retention_period = 30
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  # Security settings
  deletion_protection = true
  skip_final_snapshot = false
  final_snapshot_identifier = "healthcare-db-final-snapshot"
  
  # Monitoring
  monitoring_interval = 60
  monitoring_role_arn = aws_iam_role.rds_enhanced_monitoring.arn
  
  # Logging
  enabled_cloudwatch_logs_exports = ["postgresql"]
  
  tags = {
    Name        = "healthcare-patient-database"
    Environment = "production"
    Compliance  = "HIPAA"
  }
}

# Encrypted read replica for reporting
resource "aws_db_instance" "healthcare_db_replica" {
  identifier = "healthcare-patient-db-replica"
  
  replicate_source_db = aws_db_instance.healthcare_db.id
  instance_class      = "db.r5.large"
  
  storage_encrypted = true
  kms_key_id       = aws_kms_key.patient_data_key.arn
  
  tags = {
    Name        = "healthcare-patient-database-replica"
    Environment = "production"
    Purpose     = "reporting"
  }
}
`;
```

## 🌐 Network Security and VPC Configuration

### 1. Healthcare VPC Architecture

```javascript
// Terraform configuration for secure VPC
const vpcConfig = `
# Main VPC for healthcare platform
resource "aws_vpc" "healthcare_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  tags = {
    Name        = "healthcare-vpc"
    Environment = "production"
    Compliance  = "HIPAA"
  }
}

# Public subnets for load balancers
resource "aws_subnet" "public_subnets" {
  count = 2
  
  vpc_id                  = aws_vpc.healthcare_vpc.id
  cidr_block              = "10.0.${count.index + 1}.0/24"
  availability_zone       = data.aws_availability_zones.available.names[count.index]
  map_public_ip_on_launch = true
  
  tags = {
    Name = "healthcare-public-subnet-${count.index + 1}"
    Type = "public"
  }
}

# Private subnets for application servers
resource "aws_subnet" "private_subnets" {
  count = 2
  
  vpc_id            = aws_vpc.healthcare_vpc.id
  cidr_block        = "10.0.${count.index + 10}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]
  
  tags = {
    Name = "healthcare-private-subnet-${count.index + 1}"
    Type = "private"
  }
}

# Database subnets (isolated)
resource "aws_subnet" "database_subnets" {
  count = 2
  
  vpc_id            = aws_vpc.healthcare_vpc.id
  cidr_block        = "10.0.${count.index + 20}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]
  
  tags = {
    Name = "healthcare-database-subnet-${count.index + 1}"
    Type = "database"
  }
}

# Internet Gateway
resource "aws_internet_gateway" "healthcare_igw" {
  vpc_id = aws_vpc.healthcare_vpc.id
  
  tags = {
    Name = "healthcare-igw"
  }
}

# NAT Gateways for private subnet internet access
resource "aws_eip" "nat_eips" {
  count = 2
  vpc   = true
  
  tags = {
    Name = "healthcare-nat-eip-${count.index + 1}"
  }
}

resource "aws_nat_gateway" "nat_gateways" {
  count = 2
  
  allocation_id = aws_eip.nat_eips[count.index].id
  subnet_id     = aws_subnet.public_subnets[count.index].id
  
  tags = {
    Name = "healthcare-nat-gateway-${count.index + 1}"
  }
}
`;
```

### 2. Security Groups Configuration

```javascript
// Security groups for healthcare platform
const securityGroupsConfig = `
# Application Load Balancer Security Group
resource "aws_security_group" "alb_sg" {
  name_prefix = "healthcare-alb-"
  vpc_id      = aws_vpc.healthcare_vpc.id
  
  # HTTPS from internet
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS from internet"
  }
  
  # HTTP redirect to HTTPS
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTP redirect to HTTPS"
  }
  
  # Outbound to application servers
  egress {
    from_port       = 8080
    to_port         = 8080
    protocol        = "tcp"
    security_groups = [aws_security_group.app_sg.id]
    description     = "To application servers"
  }
  
  tags = {
    Name = "healthcare-alb-sg"
  }
}

# Application Server Security Group
resource "aws_security_group" "app_sg" {
  name_prefix = "healthcare-app-"
  vpc_id      = aws_vpc.healthcare_vpc.id
  
  # From ALB only
  ingress {
    from_port       = 8080
    to_port         = 8080
    protocol        = "tcp"
    security_groups = [aws_security_group.alb_sg.id]
    description     = "From load balancer"
  }
  
  # Outbound to database
  egress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.database_sg.id]
    description     = "To PostgreSQL database"
  }
  
  # Outbound to Redis
  egress {
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [aws_security_group.redis_sg.id]
    description     = "To Redis cache"
  }
  
  # HTTPS outbound for external APIs
  egress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS outbound for external APIs"
  }
  
  tags = {
    Name = "healthcare-app-sg"
  }
}

# Database Security Group
resource "aws_security_group" "database_sg" {
  name_prefix = "healthcare-db-"
  vpc_id      = aws_vpc.healthcare_vpc.id
  
  # PostgreSQL from application servers only
  ingress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.app_sg.id]
    description     = "PostgreSQL from application servers"
  }
  
  # No outbound rules (default deny)
  
  tags = {
    Name = "healthcare-database-sg"
  }
}
`;
```

## 🔍 Cloud Security Monitoring

### 1. AWS CloudTrail and GuardDuty Configuration

```javascript
// CloudTrail configuration for audit logging
const cloudTrailConfig = `
# S3 bucket for CloudTrail logs
resource "aws_s3_bucket" "cloudtrail_logs" {
  bucket        = "healthcare-cloudtrail-logs-${random_id.bucket_suffix.hex}"
  force_destroy = false
  
  tags = {
    Name        = "healthcare-cloudtrail-logs"
    Environment = "production"
    Purpose     = "audit-logging"
  }
}

# S3 bucket encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "cloudtrail_encryption" {
  bucket = aws_s3_bucket.cloudtrail_logs.id
  
  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.cloudtrail_key.arn
      sse_algorithm     = "aws:kms"
    }
  }
}

# CloudTrail configuration
resource "aws_cloudtrail" "healthcare_trail" {
  name           = "healthcare-audit-trail"
  s3_bucket_name = aws_s3_bucket.cloudtrail_logs.bucket
  s3_key_prefix  = "cloudtrail-logs"
  
  # Enable logging for all regions
  is_multi_region_trail = true
  
  # Include global service events
  include_global_service_events = true
  
  # Enable log file validation
  enable_log_file_validation = true
  
  # KMS encryption
  kms_key_id = aws_kms_key.cloudtrail_key.arn
  
  # Event selectors for data events
  event_selector {
    read_write_type                 = "All"
    include_management_events       = true
    exclude_management_event_sources = []
    
    # Log S3 data events for patient data bucket
    data_resource {
      type   = "AWS::S3::Object"
      values = ["${aws_s3_bucket.patient_data.arn}/*"]
    }
    
    # Log KMS key usage
    data_resource {
      type   = "AWS::KMS::Key"
      values = [aws_kms_key.patient_data_key.arn]
    }
  }
  
  tags = {
    Name        = "healthcare-cloudtrail"
    Environment = "production"
    Compliance  = "HIPAA"
  }
}

# GuardDuty for threat detection
resource "aws_guardduty_detector" "healthcare_guardduty" {
  enable = true
  
  datasources {
    s3_logs {
      enable = true
    }
    kubernetes {
      audit_logs {
        enable = true
      }
    }
    malware_protection {
      scan_ec2_instance_with_findings {
        ebs_volumes {
          enable = true
        }
      }
    }
  }
  
  tags = {
    Name        = "healthcare-guardduty"
    Environment = "production"
  }
}
`;

// Real-time security monitoring with Lambda
class CloudSecurityMonitor {
  constructor() {
    this.sns = new AWS.SNS();
    this.cloudwatch = new AWS.CloudWatch();
  }

  async processCloudTrailEvent(event) {
    const records = event.Records || [];

    for (const record of records) {
      const eventName = record.eventName;
      const sourceIPAddress = record.sourceIPAddress;
      const userIdentity = record.userIdentity;

      // Check for suspicious activities
      await this.checkSuspiciousActivity(
        eventName,
        sourceIPAddress,
        userIdentity
      );

      // Check for compliance violations
      await this.checkComplianceViolations(record);

      // Check for unauthorized access attempts
      await this.checkUnauthorizedAccess(record);
    }
  }

  async checkSuspiciousActivity(eventName, sourceIP, userIdentity) {
    const suspiciousEvents = [
      "DeleteTrail",
      "StopLogging",
      "PutBucketPolicy",
      "DeleteBucket",
      "ModifyDBInstance",
    ];

    if (suspiciousEvents.includes(eventName)) {
      await this.sendSecurityAlert({
        type: "SUSPICIOUS_ACTIVITY",
        event: eventName,
        sourceIP,
        user: userIdentity.userName || userIdentity.type,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async checkComplianceViolations(record) {
    // Check for unencrypted resource creation
    if (record.eventName === "CreateDBInstance") {
      const requestParameters = record.requestParameters;
      if (!requestParameters.storageEncrypted) {
        await this.sendComplianceAlert({
          type: "UNENCRYPTED_DATABASE",
          resource: requestParameters.dBInstanceIdentifier,
          user: record.userIdentity.userName,
        });
      }
    }
  }

  async sendSecurityAlert(alert) {
    const message = {
      alert_type: alert.type,
      details: alert,
      severity: "HIGH",
      timestamp: new Date().toISOString(),
    };

    await this.sns
      .publish({
        TopicArn: process.env.SECURITY_ALERTS_TOPIC,
        Message: JSON.stringify(message),
        Subject: `Security Alert: ${alert.type}`,
      })
      .promise();
  }
}
```

### 2. Azure Security Center Integration

```javascript
// Azure Security Center configuration
const azureSecurityConfig = `
# Azure Security Center workspace
resource "azurerm_log_analytics_workspace" "healthcare_workspace" {
  name                = "healthcare-security-workspace"
  location            = azurerm_resource_group.healthcare.location
  resource_group_name = azurerm_resource_group.healthcare.name
  sku                 = "PerGB2018"
  retention_in_days   = 90

  tags = {
    Environment = "production"
    Purpose     = "security-monitoring"
  }
}

# Security Center subscription pricing
resource "azurerm_security_center_subscription_pricing" "healthcare_pricing" {
  tier          = "Standard"
  resource_type = "VirtualMachines"
}

# Security Center auto provisioning
resource "azurerm_security_center_auto_provisioning" "healthcare_auto_provisioning" {
  auto_provision = "On"
}

# Security Center workspace
resource "azurerm_security_center_workspace" "healthcare_workspace" {
  scope        = "/subscriptions/${data.azurerm_client_config.current.subscription_id}"
  workspace_id = azurerm_log_analytics_workspace.healthcare_workspace.id
}
`;

// Azure Sentinel for advanced threat detection
class AzureSentinelMonitor {
  constructor() {
    this.logAnalyticsClient = new LogAnalyticsClient();
  }

  async createHealthcareDetectionRules() {
    const rules = [
      {
        name: "Suspicious Healthcare Data Access",
        query: `
          AuditLogs
          | where OperationName contains "patient" or OperationName contains "medical"
          | where Result == "failure"
          | summarize FailureCount = count() by Identity, bin(TimeGenerated, 5m)
          | where FailureCount > 10
        `,
        severity: "High",
        tactics: ["CredentialAccess", "Discovery"],
      },
      {
        name: "Unusual Payment Processing Activity",
        query: `
          AuditLogs
          | where OperationName contains "payment" or OperationName contains "billing"
          | where TimeGenerated > ago(1h)
          | summarize EventCount = count() by Identity, bin(TimeGenerated, 10m)
          | where EventCount > 50
        `,
        severity: "Medium",
        tactics: ["Impact"],
      },
    ];

    for (const rule of rules) {
      await this.createDetectionRule(rule);
    }
  }

  async createDetectionRule(rule) {
    // Implementation for creating Sentinel detection rules
    console.log(`Creating detection rule: ${rule.name}`);
  }
}
```

## 🏥 Healthcare-Specific Cloud Security

### 1. HIPAA Compliance in the Cloud

```yaml
# HIPAA compliance checklist for cloud deployment
hipaa_compliance:
  administrative_safeguards:
    - security_officer_assigned: true
    - workforce_training_completed: true
    - access_management_procedures: true
    - incident_response_plan: true

  physical_safeguards:
    - facility_access_controls: true
    - workstation_use_restrictions: true
    - device_and_media_controls: true

  technical_safeguards:
    - access_control: true
    - audit_controls: true
    - integrity: true
    - person_or_entity_authentication: true
    - transmission_security: true

# Cloud service configurations for HIPAA
cloud_hipaa_config:
  aws:
    - sign_baa: true # Business Associate Agreement
    - enable_cloudtrail: true
    - encrypt_all_data: true
    - use_dedicated_instances: false # Not required for HIPAA
    - implement_access_controls: true

  azure:
    - sign_baa: true
    - enable_activity_logs: true
    - use_azure_security_center: true
    - implement_rbac: true

  gcp:
    - sign_baa: true
    - enable_audit_logs: true
    - use_security_command_center: true
    - implement_iam_policies: true
```

### 2. PCI DSS Compliance for Payment Processing

```javascript
// PCI DSS compliant payment processing architecture
const pciCompliantArchitecture = `
# Separate VPC for payment processing (PCI DSS requirement)
resource "aws_vpc" "payment_vpc" {
  cidr_block           = "********/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "payment-processing-vpc"
    Environment = "production"
    Compliance  = "PCI-DSS"
    Scope       = "CDE"  # Cardholder Data Environment
  }
}

# Dedicated subnets for payment processing
resource "aws_subnet" "payment_private_subnets" {
  count = 2

  vpc_id            = aws_vpc.payment_vpc.id
  cidr_block        = "10.1.${count.index + 10}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name = "payment-private-subnet-${count.index + 1}"
    Type = "payment-processing"
    Compliance = "PCI-DSS"
  }
}

# Dedicated security group for payment processing
resource "aws_security_group" "payment_sg" {
  name_prefix = "payment-processing-"
  vpc_id      = aws_vpc.payment_vpc.id

  # Only allow HTTPS traffic from healthcare VPC
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [aws_vpc.healthcare_vpc.cidr_block]
    description = "HTTPS from healthcare platform"
  }

  # Outbound to Stripe API only
  egress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["*************/32", "**************/32"]  # Stripe IPs
    description = "To Stripe API"
  }

  tags = {
    Name = "payment-processing-sg"
    Compliance = "PCI-DSS"
  }
}
`;

// PCI DSS compliant payment service
class PCICompliantPaymentService {
  constructor() {
    this.stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
    this.kmsClient = new AWS.KMS();
    this.paymentKeyId = "alias/healthcare-payment-data";
  }

  async processPayment(paymentData) {
    try {
      // Validate PCI DSS compliance
      await this.validatePCICompliance();

      // Tokenize sensitive data immediately
      const tokenizedData = await this.tokenizePaymentData(paymentData);

      // Process payment through Stripe (PCI DSS compliant)
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: tokenizedData.amount,
        currency: "usd",
        payment_method: tokenizedData.paymentMethodId,
        confirmation_method: "manual",
        confirm: true,
        metadata: {
          appointmentId: tokenizedData.appointmentId,
          patientId: tokenizedData.patientId,
        },
      });

      // Log transaction (without sensitive data)
      await this.logPaymentTransaction({
        transactionId: paymentIntent.id,
        amount: tokenizedData.amount,
        status: paymentIntent.status,
        appointmentId: tokenizedData.appointmentId,
      });

      return {
        success: true,
        transactionId: paymentIntent.id,
        status: paymentIntent.status,
      };
    } catch (error) {
      await this.logPaymentError(error);
      throw new Error("Payment processing failed");
    }
  }

  async validatePCICompliance() {
    // Check if running in PCI DSS compliant environment
    const requiredEnvVars = [
      "PCI_COMPLIANT_ENVIRONMENT",
      "STRIPE_SECRET_KEY",
      "KMS_PAYMENT_KEY_ID",
    ];

    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        throw new Error(`PCI compliance check failed: ${envVar} not set`);
      }
    }
  }

  async tokenizePaymentData(paymentData) {
    // Remove sensitive data and replace with tokens
    const { cardNumber, cvv, ...safeData } = paymentData;

    // Never store card data - use Stripe tokens only
    return {
      ...safeData,
      paymentMethodId: paymentData.paymentMethodId, // Stripe token
    };
  }
}
```

## 📚 Best Practices Summary

### Identity and Access Management

1. **Principle of Least Privilege**: Grant minimum necessary permissions
2. **MFA Enforcement**: Require multi-factor authentication for all users
3. **Regular Access Reviews**: Periodically review and audit access permissions
4. **Service Accounts**: Use dedicated service accounts for applications
5. **Temporary Credentials**: Use temporary credentials where possible

### Data Protection

1. **Encryption at Rest**: Encrypt all data stored in cloud services
2. **Encryption in Transit**: Use TLS/SSL for all data transmission
3. **Key Management**: Use cloud-native key management services
4. **Data Classification**: Classify and label sensitive data appropriately
5. **Backup Encryption**: Ensure backups are also encrypted

### Network Security

1. **VPC Isolation**: Use VPCs to isolate healthcare workloads
2. **Security Groups**: Implement restrictive security group rules
3. **Network Segmentation**: Separate different tiers of the application
4. **Private Subnets**: Keep databases and sensitive services in private subnets
5. **WAF Protection**: Use Web Application Firewall for public-facing services

### Monitoring and Compliance

1. **Comprehensive Logging**: Enable logging for all cloud services
2. **Real-time Monitoring**: Implement real-time security monitoring
3. **Automated Alerts**: Set up automated alerts for security events
4. **Regular Audits**: Conduct regular security audits and assessments
5. **Compliance Frameworks**: Implement HIPAA, PCI DSS compliance controls

## 🔗 Additional Resources

- [AWS Well-Architected Security Pillar](https://docs.aws.amazon.com/wellarchitected/latest/security-pillar/)
- [Azure Security Best Practices](https://docs.microsoft.com/en-us/azure/security/)
- [Google Cloud Security Best Practices](https://cloud.google.com/security/best-practices)
- [HIPAA on AWS](https://aws.amazon.com/compliance/hipaa-compliance/)
- [PCI DSS on Cloud](https://www.pcisecuritystandards.org/documents/PCI_DSS_v3-2-1_Cloud_Guidelines_v1.pdf)

---

**Next**: [Container Security](08-container-security.md) | **Previous**: [Frontend Security](06-frontend-security.md)

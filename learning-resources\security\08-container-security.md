# Container Security

## Overview

Container security is critical for healthcare platforms using Docker and Kubernetes to deploy applications handling sensitive patient data and payment information. This guide covers comprehensive security measures for containerized environments, including image security, runtime protection, and orchestration security.

## 🎯 Learning Objectives

By the end of this module, you will understand:
- Container image security and vulnerability scanning
- Runtime security and monitoring for containers
- Kubernetes security best practices
- Secrets management in containerized environments
- Network security for container orchestration
- Compliance considerations for healthcare containers

## 🐳 Docker Security Fundamentals

### 1. Secure Dockerfile Practices

```dockerfile
# Secure Dockerfile for healthcare application
# Use specific version tags, not 'latest'
FROM node:18.17.0-alpine3.18

# Create non-root user
RUN addgroup -g 1001 -S healthcare && \
    adduser -S healthcare -u 1001 -G healthcare

# Set working directory
WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies as root, then switch to non-root
RUN npm ci --only=production && \
    npm cache clean --force && \
    rm -rf /tmp/* /var/tmp/*

# Copy application code
COPY --chown=healthcare:healthcare . .

# Remove unnecessary packages and files
RUN apk del --purge \
    && rm -rf /var/cache/apk/* \
    && rm -rf /tmp/* \
    && rm -rf /root/.npm

# Switch to non-root user
USER healthcare

# Expose port (non-privileged)
EXPOSE 3000

# Use exec form for better signal handling
CMD ["node", "server.js"]

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Security labels
LABEL security.scan="enabled" \
      compliance="HIPAA" \
      maintainer="<EMAIL>"
```

### 2. Multi-stage Build for Security

```dockerfile
# Multi-stage Dockerfile for production security
# Build stage
FROM node:18.17.0-alpine3.18 AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --include=dev

COPY . .
RUN npm run build && \
    npm prune --production

# Security scanning stage
FROM builder AS security-scan
RUN npm audit --audit-level=high && \
    npm run security:scan

# Production stage
FROM node:18.17.0-alpine3.18 AS production

# Install security updates
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init && \
    rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S healthcare && \
    adduser -S healthcare -u 1001 -G healthcare

WORKDIR /app

# Copy only production files
COPY --from=builder --chown=healthcare:healthcare /app/node_modules ./node_modules
COPY --from=builder --chown=healthcare:healthcare /app/dist ./dist
COPY --from=builder --chown=healthcare:healthcare /app/package.json ./

# Switch to non-root user
USER healthcare

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/server.js"]
```

### 3. Container Image Scanning

```yaml
# GitHub Actions workflow for container security scanning
name: Container Security Scan

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Build Docker image
        run: |
          docker build -t healthcare-app:${{ github.sha }} .
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'healthcare-app:${{ github.sha }}'
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH'
          exit-code: '1'
      
      - name: Run Snyk Container scan
        uses: snyk/actions/docker@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          image: healthcare-app:${{ github.sha }}
          args: --severity-threshold=high
      
      - name: Run Docker Bench Security
        run: |
          docker run --rm --net host --pid host --userns host --cap-add audit_control \
            -e DOCKER_CONTENT_TRUST=$DOCKER_CONTENT_TRUST \
            -v /etc:/etc:ro \
            -v /usr/bin/containerd:/usr/bin/containerd:ro \
            -v /usr/bin/runc:/usr/bin/runc:ro \
            -v /usr/lib/systemd:/usr/lib/systemd:ro \
            -v /var/lib:/var/lib:ro \
            -v /var/run/docker.sock:/var/run/docker.sock:ro \
            --label docker_bench_security \
            docker/docker-bench-security
```

## ☸️ Kubernetes Security

### 1. Secure Pod Security Standards

```yaml
# Pod Security Policy for healthcare workloads
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: healthcare-psp
  annotations:
    seccomp.security.alpha.kubernetes.io/allowedProfileNames: 'runtime/default'
    seccomp.security.alpha.kubernetes.io/defaultProfileName: 'runtime/default'
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  runAsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
  readOnlyRootFilesystem: true
  
---
# Pod Security Standards (PSS) - Restricted profile
apiVersion: v1
kind: Namespace
metadata:
  name: healthcare-production
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
```

### 2. Secure Deployment Configuration

```yaml
# Secure deployment for healthcare application
apiVersion: apps/v1
kind: Deployment
metadata:
  name: healthcare-app
  namespace: healthcare-production
  labels:
    app: healthcare-app
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: healthcare-app
  template:
    metadata:
      labels:
        app: healthcare-app
      annotations:
        # Security annotations
        container.apparmor.security.beta.kubernetes.io/healthcare-app: runtime/default
    spec:
      # Security context for the pod
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
        seccompProfile:
          type: RuntimeDefault
      
      # Service account with minimal permissions
      serviceAccountName: healthcare-app-sa
      automountServiceAccountToken: false
      
      containers:
      - name: healthcare-app
        image: healthcare-app:v1.0.0
        imagePullPolicy: Always
        
        # Container security context
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
          runAsGroup: 1001
          capabilities:
            drop:
              - ALL
        
        # Resource limits
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        
        # Environment variables from secrets
        env:
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: healthcare-db-secret
              key: password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: healthcare-jwt-secret
              key: secret
        
        # Volume mounts
        volumeMounts:
        - name: tmp-volume
          mountPath: /tmp
        - name: cache-volume
          mountPath: /app/cache
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        
        # Health checks
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      
      # Volumes
      volumes:
      - name: tmp-volume
        emptyDir: {}
      - name: cache-volume
        emptyDir: {}
      - name: config-volume
        configMap:
          name: healthcare-config
          defaultMode: 0444
      
      # Node selection and affinity
      nodeSelector:
        kubernetes.io/os: linux
        node-type: application
      
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - healthcare-app
              topologyKey: kubernetes.io/hostname
```

### 3. RBAC Configuration

```yaml
# Service Account for healthcare application
apiVersion: v1
kind: ServiceAccount
metadata:
  name: healthcare-app-sa
  namespace: healthcare-production
automountServiceAccountToken: false

---
# Role with minimal permissions
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: healthcare-production
  name: healthcare-app-role
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get"]
  resourceNames: ["healthcare-db-secret", "healthcare-jwt-secret"]

---
# Role binding
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: healthcare-app-binding
  namespace: healthcare-production
subjects:
- kind: ServiceAccount
  name: healthcare-app-sa
  namespace: healthcare-production
roleRef:
  kind: Role
  name: healthcare-app-role
  apiGroup: rbac.authorization.k8s.io
```

## 🔐 Secrets Management

### 1. External Secrets Operator

```yaml
# External Secrets configuration for AWS Secrets Manager
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: aws-secrets-store
  namespace: healthcare-production
spec:
  provider:
    aws:
      service: SecretsManager
      region: us-east-1
      auth:
        secretRef:
          accessKeyID:
            name: aws-credentials
            key: access-key-id
          secretAccessKey:
            name: aws-credentials
            key: secret-access-key

---
# External Secret for database credentials
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: healthcare-db-external-secret
  namespace: healthcare-production
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-secrets-store
    kind: SecretStore
  target:
    name: healthcare-db-secret
    creationPolicy: Owner
  data:
  - secretKey: password
    remoteRef:
      key: healthcare/database
      property: password
  - secretKey: username
    remoteRef:
      key: healthcare/database
      property: username
```

### 2. Sealed Secrets for GitOps

```yaml
# Sealed Secret for JWT configuration
apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  name: healthcare-jwt-secret
  namespace: healthcare-production
spec:
  encryptedData:
    secret: AgBy3i4OJSWK+PiTySYZZA9rO43cGDEQAx...
  template:
    metadata:
      name: healthcare-jwt-secret
      namespace: healthcare-production
    type: Opaque
```

## 🌐 Network Security

### 1. Network Policies

```yaml
# Network policy for healthcare application
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: healthcare-app-netpol
  namespace: healthcare-production
spec:
  podSelector:
    matchLabels:
      app: healthcare-app
  policyTypes:
  - Ingress
  - Egress
  
  ingress:
  # Allow traffic from ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 3000
  
  # Allow traffic from monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 3000
  
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
  
  # Allow database access
  - to:
    - podSelector:
        matchLabels:
          app: postgresql
    ports:
    - protocol: TCP
      port: 5432
  
  # Allow Redis access
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  
  # Allow HTTPS outbound for external APIs
  - to: []
    ports:
    - protocol: TCP
      port: 443

---
# Network policy for database
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: postgresql-netpol
  namespace: healthcare-production
spec:
  podSelector:
    matchLabels:
      app: postgresql
  policyTypes:
  - Ingress
  
  ingress:
  # Only allow from healthcare app
  - from:
    - podSelector:
        matchLabels:
          app: healthcare-app
    ports:
    - protocol: TCP
      port: 5432
```

### 2. Service Mesh Security with Istio

```yaml
# Istio PeerAuthentication for mTLS
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: healthcare-mtls
  namespace: healthcare-production
spec:
  mtls:
    mode: STRICT

---
# Authorization Policy
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: healthcare-authz
  namespace: healthcare-production
spec:
  selector:
    matchLabels:
      app: healthcare-app
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/ingress-nginx/sa/ingress-nginx"]
  - to:
    - operation:
        methods: ["GET", "POST"]
        paths: ["/api/*"]
  - when:
    - key: request.headers[authorization]
      values: ["Bearer *"]
```

## 📊 Runtime Security Monitoring

### 1. Falco Rules for Healthcare

```yaml
# Custom Falco rules for healthcare security
- rule: Unauthorized Process in Healthcare Container
  desc: Detect unexpected processes in healthcare containers
  condition: >
    spawned_process and
    container and
    k8s.ns.name = "healthcare-production" and
    not proc.name in (node, npm, dumb-init, sh, bash)
  output: >
    Unauthorized process in healthcare container
    (user=%user.name command=%proc.cmdline container=%container.name
    image=%container.image.repository:%container.image.tag)
  priority: WARNING
  tags: [healthcare, process, container]

- rule: Healthcare Data Access Outside Business Hours
  desc: Detect data access outside normal business hours
  condition: >
    open_read and
    fd.name contains "/app/data" and
    k8s.ns.name = "healthcare-production" and
    (evt.time.hour < 6 or evt.time.hour > 22)
  output: >
    Healthcare data accessed outside business hours
    (user=%user.name file=%fd.name container=%container.name time=%evt.time)
  priority: HIGH
  tags: [healthcare, data-access, compliance]

- rule: Sensitive File Access in Healthcare Container
  desc: Monitor access to sensitive configuration files
  condition: >
    open_read and
    k8s.ns.name = "healthcare-production" and
    (fd.name contains "/etc/passwd" or
     fd.name contains "/etc/shadow" or
     fd.name contains "/.env" or
     fd.name contains "/app/config/database")
  output: >
    Sensitive file accessed in healthcare container
    (user=%user.name file=%fd.name container=%container.name)
  priority: HIGH
  tags: [healthcare, sensitive-files]
```

### 2. Container Runtime Security

```javascript
// Runtime security monitoring service
class ContainerSecurityMonitor {
  constructor() {
    this.falcoClient = new FalcoClient();
    this.alertManager = new AlertManager();
  }

  async monitorContainerEvents() {
    this.falcoClient.on('alert', async (alert) => {
      await this.processSecurityAlert(alert);
    });
  }

  async processSecurityAlert(alert) {
    const severity = this.calculateSeverity(alert);
    
    if (severity === 'CRITICAL') {
      await this.handleCriticalAlert(alert);
    } else if (severity === 'HIGH') {
      await this.handleHighAlert(alert);
    }
    
    await this.logSecurityEvent(alert);
  }

  async handleCriticalAlert(alert) {
    // Immediate response for critical alerts
    if (alert.rule.includes('Unauthorized Process')) {
      await this.quarantineContainer(alert.output_fields.container_name);
    }
    
    await this.alertManager.sendImmediateAlert({
      type: 'CRITICAL_CONTAINER_SECURITY',
      alert,
      timestamp: new Date().toISOString()
    });
  }

  async quarantineContainer(containerName) {
    // Implement container quarantine logic
    console.log(`Quarantining container: ${containerName}`);
    
    // Scale down deployment
    await this.kubernetesClient.scaleDeployment(containerName, 0);
    
    // Create network policy to isolate
    await this.createQuarantineNetworkPolicy(containerName);
  }
}
```

## 📚 Best Practices Summary

### Container Image Security
1. **Use Minimal Base Images**: Use distroless or alpine images
2. **Regular Scanning**: Implement automated vulnerability scanning
3. **Multi-stage Builds**: Separate build and runtime environments
4. **Non-root Users**: Always run containers as non-root users
5. **Image Signing**: Use Docker Content Trust for image verification

### Kubernetes Security
1. **Pod Security Standards**: Implement restricted pod security policies
2. **RBAC**: Use role-based access control with minimal permissions
3. **Network Policies**: Implement network segmentation
4. **Secrets Management**: Use external secret management systems
5. **Resource Limits**: Set appropriate resource limits and requests

### Runtime Security
1. **Security Monitoring**: Implement runtime security monitoring
2. **Anomaly Detection**: Use tools like Falco for behavioral analysis
3. **Regular Updates**: Keep container runtime and orchestrator updated
4. **Compliance Scanning**: Regular compliance and configuration scanning
5. **Incident Response**: Have automated incident response procedures

## 🔗 Additional Resources

- [CIS Kubernetes Benchmark](https://www.cisecurity.org/benchmark/kubernetes)
- [NIST Container Security Guide](https://csrc.nist.gov/publications/detail/sp/800-190/final)
- [Kubernetes Security Best Practices](https://kubernetes.io/docs/concepts/security/)
- [Docker Security Best Practices](https://docs.docker.com/engine/security/)
- [OWASP Container Security](https://owasp.org/www-project-container-security/)

---

**Next**: [Network Security](09-network-security.md) | **Previous**: [Cloud Security](07-cloud-security.md)

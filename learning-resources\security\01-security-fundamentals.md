# Security Fundamentals

## 🎯 Core Security Principles

### The CIA Triad
1. **Confidentiality**: Ensuring information is accessible only to authorized individuals
2. **Integrity**: Maintaining accuracy and completeness of data
3. **Availability**: Ensuring systems and data are accessible when needed

### Additional Security Principles
- **Authentication**: Verifying identity of users/systems
- **Authorization**: Granting appropriate access rights
- **Non-repudiation**: Preventing denial of actions performed
- **Accountability**: Tracking and logging user actions

## 🔒 Defense in Depth

### Layered Security Approach
```
┌─────────────────────────────────────┐
│           Physical Security         │
├─────────────────────────────────────┤
│           Network Security          │
├─────────────────────────────────────┤
│           Host Security             │
├─────────────────────────────────────┤
│        Application Security         │
├─────────────────────────────────────┤
│           Data Security             │
└─────────────────────────────────────┘
```

### Implementation Layers
1. **Perimeter Security**: Firewalls, IDS/IPS
2. **Network Segmentation**: VLANs, subnets, micro-segmentation
3. **Endpoint Protection**: Antivirus, EDR, device management
4. **Application Controls**: Input validation, access controls
5. **Data Protection**: Encryption, DLP, backup strategies

## 🚨 Common Attack Vectors

### 1. Social Engineering
- **Phishing**: Fraudulent emails/websites
- **Pretexting**: Creating false scenarios
- **Baiting**: Offering something enticing
- **Tailgating**: Following authorized personnel

### 2. Technical Attacks
- **Malware**: Viruses, trojans, ransomware
- **Network Attacks**: Man-in-the-middle, packet sniffing
- **Web Attacks**: SQL injection, XSS, CSRF
- **Privilege Escalation**: Gaining unauthorized access levels

## 🛡️ Risk Management Framework

### Risk Assessment Process
```python
# Risk Calculation Formula
Risk = Threat × Vulnerability × Impact

# Example Risk Assessment
def calculate_risk(threat_likelihood, vulnerability_severity, impact_level):
    """
    Calculate risk score (1-10 scale)
    """
    return (threat_likelihood * vulnerability_severity * impact_level) / 10

# Risk Matrix Example
risk_levels = {
    "low": (1, 3),
    "medium": (4, 6),
    "high": (7, 8),
    "critical": (9, 10)
}
```

### Risk Treatment Options
1. **Accept**: Acknowledge and monitor the risk
2. **Avoid**: Eliminate the risk source
3. **Mitigate**: Reduce likelihood or impact
4. **Transfer**: Share risk (insurance, outsourcing)

## 🔐 Cryptography Basics

### Symmetric Encryption
```python
from cryptography.fernet import Fernet

# Generate key
key = Fernet.generate_key()
cipher_suite = Fernet(key)

# Encrypt data
plaintext = b"Sensitive healthcare data"
ciphertext = cipher_suite.encrypt(plaintext)

# Decrypt data
decrypted_text = cipher_suite.decrypt(ciphertext)
```

### Asymmetric Encryption
```python
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives import serialization

# Generate key pair
private_key = rsa.generate_private_key(
    public_exponent=65537,
    key_size=2048
)
public_key = private_key.public_key()

# Serialize keys
private_pem = private_key.private_bytes(
    encoding=serialization.Encoding.PEM,
    format=serialization.PrivateFormat.PKCS8,
    encryption_algorithm=serialization.NoEncryption()
)
```

### Hashing
```python
import hashlib
import hmac

# SHA-256 hashing
data = "password123"
hash_object = hashlib.sha256(data.encode())
hex_dig = hash_object.hexdigest()

# HMAC for integrity
secret_key = b"secret_key"
message = b"important_message"
signature = hmac.new(secret_key, message, hashlib.sha256).hexdigest()
```

## 🏥 Healthcare Security Context

### HIPAA Security Requirements
1. **Administrative Safeguards**
   - Security officer designation
   - Workforce training
   - Access management procedures

2. **Physical Safeguards**
   - Facility access controls
   - Workstation security
   - Media controls

3. **Technical Safeguards**
   - Access control
   - Audit controls
   - Integrity controls
   - Transmission security

### Payment Security (PCI DSS)
1. **Build and maintain secure networks**
2. **Protect cardholder data**
3. **Maintain vulnerability management**
4. **Implement strong access controls**
5. **Regularly monitor networks**
6. **Maintain information security policy**

## 📊 Security Metrics and KPIs

### Key Security Metrics
```python
# Security Metrics Dashboard
security_metrics = {
    "vulnerability_metrics": {
        "critical_vulns": 0,
        "high_vulns": 2,
        "medium_vulns": 15,
        "low_vulns": 45,
        "mean_time_to_patch": "72 hours"
    },
    "incident_metrics": {
        "incidents_this_month": 3,
        "mean_time_to_detect": "4 hours",
        "mean_time_to_respond": "2 hours",
        "mean_time_to_recover": "8 hours"
    },
    "compliance_metrics": {
        "policy_compliance": "98%",
        "training_completion": "95%",
        "audit_findings": 2
    }
}
```

## 🎯 Action Items for Healthcare Platform

### Immediate Priorities
1. **Data Classification**: Categorize PHI, PII, and payment data
2. **Access Controls**: Implement role-based access control (RBAC)
3. **Encryption**: Encrypt data at rest and in transit
4. **Logging**: Implement comprehensive audit logging
5. **Backup**: Secure backup and recovery procedures

### Security Checklist
- [ ] Security policy documentation
- [ ] Employee security training
- [ ] Incident response plan
- [ ] Vulnerability management program
- [ ] Third-party risk assessment
- [ ] Regular security assessments
- [ ] Compliance monitoring
- [ ] Business continuity planning

## 📚 Next Steps

1. **Review**: Common Vulnerabilities (02-common-vulnerabilities.md)
2. **Study**: Secure Coding Practices (03-secure-coding-practices.md)
3. **Practice**: Set up a security testing environment
4. **Certification**: Consider Security+ or CISSP preparation

---

**Remember**: Security is not a destination but a continuous journey. Stay updated with the latest threats and best practices.
